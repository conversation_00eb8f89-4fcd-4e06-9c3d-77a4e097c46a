// === Arduino Controller for ESP32 Management ===
// Handles graphical programming logic and ESP32 control
// === Basic Libraries ===
#include "Arduino.h"
#include <SoftwareSerial.h>

// === Serial Communication ===
SoftwareSerial esp32Serial(3, 2);  // RX, TX pins for ESP32 communication
#define ESP32_SERIAL esp32Serial   // Communication with ESP32
#define COMPUTER_SERIAL Serial     // USB communication with computer

// WIND-SPEED-SENSOR config
#define WIND_SPEED_SENSOR_PIN A0

bool initFlag = false;

void setWiFi(String wifiSSID, String wifiPassword) {
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.println(F("Setting WiFi..."));
    COMPUTER_SERIAL.print(F("WiFi SSID: "));
    COMPUTER_SERIAL.println(wifiSSID);
    COMPUTER_SERIAL.print(F("WiFi Password: "));
    COMPUTER_SERIAL.println(wifiPassword);
  }
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("S WiFi"));
    ESP32_SERIAL.println(wifiSSID);
    ESP32_SERIAL.println(wifiPassword);
  }
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.println(F("Waiting for WiFi Response"));
  }
  while (ESP32_SERIAL.available() == 0);
  String response = ESP32_SERIAL.readStringUntil('\n');
  response.trim();
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.print(F("WiFi Response: "));
    COMPUTER_SERIAL.println(response);
  }
}

float readBMESensorTemperature() {
  // Read temperature from BME280 sensor
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R BME T"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for BME Temperature Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String temperature = ESP32_SERIAL.readStringUntil('\n');
  temperature.trim();
  return temperature.toFloat();
}

float readBMESensorHumidity() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R BME H"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for BME Humidity Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String humidity = ESP32_SERIAL.readStringUntil('\n');
  humidity.trim();
  return humidity.toFloat();
}

float readBMESensorPressure() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R BME P"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for BME Pressure Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String pressure = ESP32_SERIAL.readStringUntil('\n');
  pressure.trim();
  return pressure.toFloat();
}

int readPMS7003SensorPM1_0() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R PMS 1.0"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for PMS PM1.0 Feedback"));
        delay(10);
      }
      return -999;  // Error value for int
    }
  }
  String pm1_0 = ESP32_SERIAL.readStringUntil('\n');
  pm1_0.trim();
  return pm1_0.toInt();
}

int readPMS7003SensorPM2_5() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R PMS 2.5"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for PMS PM2.5 Feedback"));
        delay(10);
      }
      return -999;  // Error value for int
    }
  }
  String pm2_5 = ESP32_SERIAL.readStringUntil('\n');
  pm2_5.trim();
  return pm2_5.toInt();
}

int readPMS7003SensorPM10() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R PMS 10.0"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for PMS PM10 Feedback"));
        delay(10);
      }
      return -999;  // Error value for int
    }
  }
  String pm10 = ESP32_SERIAL.readStringUntil('\n');
  pm10.trim();
  return pm10.toInt();
}

int readPMS7003SensorN0_3() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R PMS N 0.3"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for PMS Number of 0.3um Particles Feedback"));
        delay(10);
      }
      return -999;  // Error value for int
    }
  }
  String n0_3 = ESP32_SERIAL.readStringUntil('\n');
  n0_3.trim();
  return n0_3.toInt();
}

int readPMS7003SensorN0_5() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R PMS N 0.5"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for PMS Number of 0.5um Particles Feedback"));
        delay(10);
      }
      return -999;  // Error value for int
    }
  }
  String n0_5 = ESP32_SERIAL.readStringUntil('\n');
  n0_5.trim();
  return n0_5.toInt();
}

int readPMS7003SensorN1_0() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R PMS N 1.0"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for PMS Number of 1.0um Particles Feedback"));
        delay(10);
      }
      return -999;  // Error value for int
    }
  }
  String n1_0 = ESP32_SERIAL.readStringUntil('\n');
  n1_0.trim();
  return n1_0.toInt();
}

int readPMS7003SensorN2_5() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R PMS N 2.5"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for PMS Number of 2.5um Particles Feedback"));
        delay(10);
      }
      return -999;  // Error value for int
    }
  }
  String n2_5 = ESP32_SERIAL.readStringUntil('\n');
  n2_5.trim();
  return n2_5.toInt();
}

int readPMS7003SensorN5_0() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R PMS N 5.0"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for PMS Number of 5.0um Particles Feedback"));
        delay(10);
      }
      return -999;  // Error value for int
    }
  }
  String n5_0 = ESP32_SERIAL.readStringUntil('\n');
  n5_0.trim();
  return n5_0.toInt();
}

int readPMS7003SensorN10_0() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R PMS N 10.0"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for PMS Number of 10.0um Particles Feedback"));
        delay(10);
      }
      return -999;  // Error value for int
    }
  }
  String n10_0 = ESP32_SERIAL.readStringUntil('\n');
  n10_0.trim();
  return n10_0.toInt();
}

int readMHZ19BSensorCO2() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R MHZ19B CO2"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for MHZ19B CO2 Feedback"));
        delay(10);
      }
      return -999;  // Error value for int
    }
  }
  String co2 = ESP32_SERIAL.readStringUntil('\n');
  co2.trim();
  return co2.toInt();
}

int readMHZ19BSensorMinCO2() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R MHZ19B Min CO2"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for MHZ19B Min CO2 Feedback"));
        delay(10);
      }
      return -999;  // Error value for int
    }
  }
  String minCO2 = ESP32_SERIAL.readStringUntil('\n');
  minCO2.trim();
  return minCO2.toInt();
}

// Additional MHZ19B sensor functions
float readMHZ19BSensorTemperature() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R MHZ19B Temperature"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for MHZ19B Temperature Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String temperature = ESP32_SERIAL.readStringUntil('\n');
  temperature.trim();
  return temperature.toFloat();
}

float readMHZ19BSensorAccuracy() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R MHZ19B Accuracy"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for MHZ19B Accuracy Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String accuracy = ESP32_SERIAL.readStringUntil('\n');
  accuracy.trim();
  return accuracy.toFloat();
}

// Wind Speed Sensor functions
float readWindSpeed() {
  float windSpeed = float(6 * analogRead(WIND_SPEED_SENSOR_PIN) * 5) / 1023;
  return windSpeed;
}

// LTR390 UV Sensor functions
float readUVIndex() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R LTR390 Uvi"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for UV Index Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String uvIndex = ESP32_SERIAL.readStringUntil('\n');
  uvIndex.trim();
  return uvIndex.toFloat();
}

float readUVIntensity() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R LTR390 Lux"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for UV Intensity Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String uvIntensity = ESP32_SERIAL.readStringUntil('\n');
  uvIntensity.trim();
  return uvIntensity.toFloat();
}

// GPS Sensor functions
float readGPSQuality() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R GPS Quality"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for GPS Quality Feedback"));
        delay(10);
      }
      return NAN;  // Error value for int
    }
  }
  String gpsQuality = ESP32_SERIAL.readStringUntil('\n');
  gpsQuality.trim();
  return gpsQuality.toFloat();
}

float readGPSSatellites() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R GPS Satellites"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for GPS Satellites Feedback"));
        delay(10);
      }
      return NAN;  // Error value for int
    }
  }
  String gpsSatellites = ESP32_SERIAL.readStringUntil('\n');
  gpsSatellites.trim();
  return gpsSatellites.toFloat();
}

float readGPSAccuracy() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R GPS Accuracy"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for GPS Accuracy Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String gpsAccuracy = ESP32_SERIAL.readStringUntil('\n');
  gpsAccuracy.trim();
  return gpsAccuracy.toFloat();
}

float readGPSAltitude() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R GPS Altitude"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for GPS Altitude Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String gpsAltitude = ESP32_SERIAL.readStringUntil('\n');
  gpsAltitude.trim();
  return gpsAltitude.toFloat();
}

float readGPSLatitude() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R GPS Latitude"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for GPS Latitude Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String gpsLatitude = ESP32_SERIAL.readStringUntil('\n');
  gpsLatitude.trim();
  return gpsLatitude.toFloat();
}

float readGPSLongitude() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R GPS Longitude"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for GPS Longitude Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String gpsLongitude = ESP32_SERIAL.readStringUntil('\n');
  gpsLongitude.trim();
  return gpsLongitude.toFloat();
}

// Get device UUID
String getDeviceUUID() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("G UUID"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for UUID Feedback"));
        delay(10);
      }
      return "Get UUID Timeout";
    }
  }
  String uuid = ESP32_SERIAL.readStringUntil('\n');
  uuid.trim();
  return uuid;
}

// Send Log to Server
void sendLogToServer(String logType, String message) {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("U LOG"));
    ESP32_SERIAL.println(logType);
    ESP32_SERIAL.println(message);
  }
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.println(F("Waiting for Log Upload Feedback"));
  }
  while (ESP32_SERIAL.available() == 0);
  String response = ESP32_SERIAL.readStringUntil('\n');
  response.trim();
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.print(F("Log Response: "));
    COMPUTER_SERIAL.println(response);
  }
}

// Send BME280 sensor group data to server
String sendBME280DataToServer() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("U BME280"));
  }
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.println(F("Waiting for BME280 Upload Feedback"));
  }
  while (ESP32_SERIAL.available() == 0);
  String response = ESP32_SERIAL.readStringUntil('\n');
  response.trim();
  COMPUTER_SERIAL.println(response);
  return response;
}

// Send PMS7003 sensor group data to server
String sendPMS7003DataToServer() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("U PMS7003"));
  }
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.println(F("Waiting for PMS7003 Upload Feedback"));
  }
  while (ESP32_SERIAL.available() == 0);
  String response = ESP32_SERIAL.readStringUntil('\n');
  response.trim();
  COMPUTER_SERIAL.println(response);
  return response;
}

// Send MHZ19B sensor group data to server
String sendMHZ19BDataToServer() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("U MHZ19B"));
  }
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.println(F("Waiting for MHZ19B Upload Feedback"));
  }
  while (ESP32_SERIAL.available() == 0);
  String response = ESP32_SERIAL.readStringUntil('\n');
  response.trim();
  COMPUTER_SERIAL.println(response);
  return response;
}

// Send Wind sensor group data to server
String sendWindDataToServer() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("U WIND"));
    ESP32_SERIAL.println(String(float(6 * analogRead(WIND_SPEED_SENSOR_PIN) * 5) / 1023));
    
  }
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.println(F("Waiting for Wind Upload Feedback"));
  }
  while (ESP32_SERIAL.available() == 0);
  String response = ESP32_SERIAL.readStringUntil('\n');
  response.trim();
  COMPUTER_SERIAL.println(response);
  return response;
}

// Send LTR390 UV sensor group data to server
String sendLTR390DataToServer() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("U LTR390"));
  }
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.println(F("Waiting for LTR390 Upload Feedback"));
  }
  while (ESP32_SERIAL.available() == 0);
  String response = ESP32_SERIAL.readStringUntil('\n');
  response.trim();
  COMPUTER_SERIAL.println(response);
  return response;
}

// Send GPS sensor group data to server
String sendGPSDataToServer() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("U GPS"));
  }
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.println(F("Waiting for GPS Upload Feedback"));
  }
  while (ESP32_SERIAL.available() == 0);
  String response = ESP32_SERIAL.readStringUntil('\n');
  response.trim();
  COMPUTER_SERIAL.println(response);
  return response;
}

// Send all sensor groups data to server
String sendAllSensorGroupsToServer() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("U ALL"));
    ESP32_SERIAL.println(String(float(6 * analogRead(WIND_SPEED_SENSOR_PIN) * 5) / 1023));
  }
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.println(F("Waiting for All Sensors Upload Feedback"));
  }
  while (ESP32_SERIAL.available() == 0);
  String response = ESP32_SERIAL.readStringUntil('\n');
  response.trim();
  COMPUTER_SERIAL.println(response);
  return response;
}

// === SENSOR STATUS FUNCTIONS ===

// Get BME280 sensor status
bool getBME280Status() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("G BME280 STATUS"));
  }
  while (ESP32_SERIAL.available() == 0) {
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("Waiting for BME280 Status"));
      delay(10);
    }
  }
  String status = ESP32_SERIAL.readStringUntil('\n');
  status.trim();
  status.trim();
  return status == "BME280_OK";
}

// Get PMS7003 sensor status
bool getPMS7003Status() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("G PMS7003 STATUS"));
  }
  while (ESP32_SERIAL.available() == 0) {
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("Waiting for PMS7003 Status"));
      delay(10);
    }
  }
  String status = ESP32_SERIAL.readStringUntil('\n');
  status.trim();
  return status == "PMS7003_OK";
}

// Get MHZ19B sensor status
bool getMHZ19BStatus() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("G MHZ19B STATUS"));
  }
  while (ESP32_SERIAL.available() == 0) {
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("Waiting for MHZ19B Status"));
      delay(10);
    }
  }
  String status = ESP32_SERIAL.readStringUntil('\n');
  status.trim();
  return status == "MHZ19B_OK";
}

// Get Wind Speed sensor status
bool getWindSpeedStatus() {
  int windReading = analogRead(WIND_SPEED_SENSOR_PIN);
  bool wind_status = false;
  if (windReading >= 0 && windReading <= 5000) {
    wind_status = true;
  }
  return wind_status;
}

// Get UV sensor status
bool getUVStatus() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("G LTR390 STATUS"));
  }
  while (ESP32_SERIAL.available() == 0) {
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("Waiting for UV Status"));
      delay(10);
    }
  }
  String status = ESP32_SERIAL.readStringUntil('\n');
  status.trim();
  return status == "LTR390_OK";
}

// Get GPS sensor status
bool getGPSStatus() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("G GPS STATUS"));
  }
  while (ESP32_SERIAL.available() == 0) {
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("Waiting for GPS Status"));
      delay(10);
    }
  }
  String status = ESP32_SERIAL.readStringUntil('\n');
  status.trim();
  return status == "GPS_OK";
}

// === Setup ===
void setup() {
  COMPUTER_SERIAL.begin(115200);
  while (!COMPUTER_SERIAL) {
    delay(10);
  }
  COMPUTER_SERIAL.println(F("Arduino Controller Initialized"));
  
  // Initialize ESP32 communication
  ESP32_SERIAL.begin(9600);  // SoftwareSerial typically uses lower baud rates
  while (!ESP32_SERIAL){
    delay(10);
  }

  ESP32_SERIAL.println(F("R INIT"));
  while (!initFlag) {
    delay(10);
    if (ESP32_SERIAL.available() > 0) {
      String feedback = ESP32_SERIAL.readStringUntil('\n');
      feedback.trim();
      if (feedback == "YES") {
        initFlag = true;
        COMPUTER_SERIAL.println(F("System Ready"));
      }
    }
  }
  // WiFi setup will be called from generated code if needed
}

// === Main Loop ===
void loop() {
  if (initFlag) {
    ESP32_SERIAL.println(F("LOOP START"));
    // BME280 Sensor
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("BME Status: "));
      COMPUTER_SERIAL.println(getBME280Status());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("BME Temperature: "));
      COMPUTER_SERIAL.println(readBMESensorTemperature());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("BME Humidity: "));
      COMPUTER_SERIAL.println(readBMESensorHumidity());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("BME Pressure: "));
      COMPUTER_SERIAL.println(readBMESensorPressure());
    }

    // PMS7003 Sensor
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("PMS Status: "));
      COMPUTER_SERIAL.println(getPMS7003Status());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("PMS 1.0: "));
      COMPUTER_SERIAL.println(readPMS7003SensorPM1_0());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("PMS 2.5: "));
      COMPUTER_SERIAL.println(readPMS7003SensorPM2_5());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("PMS 10: "));
      COMPUTER_SERIAL.println(readPMS7003SensorPM10());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("PMS 0.3: "));
      COMPUTER_SERIAL.println(readPMS7003SensorN0_3());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("PMS 0.5: "));
      COMPUTER_SERIAL.println(readPMS7003SensorN0_5());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("PMS 1.0: "));
      COMPUTER_SERIAL.println(readPMS7003SensorN1_0());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("PMS 2.5: "));
      COMPUTER_SERIAL.println(readPMS7003SensorN2_5());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("PMS 5.0: "));
      COMPUTER_SERIAL.println(readPMS7003SensorN5_0());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("PMS 10.0: "));
      COMPUTER_SERIAL.println(readPMS7003SensorN10_0());
    }

    // MHZ19B Sensor
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("MHZ19B Status: "));
      COMPUTER_SERIAL.println(getMHZ19BStatus());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("CO2: "));
      COMPUTER_SERIAL.println(readMHZ19BSensorCO2());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("Min CO2: "));
      COMPUTER_SERIAL.println(readMHZ19BSensorMinCO2());
    }

    // WIND SPEED Sensor
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("WIND SPEED Sensor Status: "));
      COMPUTER_SERIAL.println(getWindSpeedStatus());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("Wind Speed: "));
      COMPUTER_SERIAL.println(readWindSpeed());
    }

    // UV Sensor
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("UV Status: "));
      COMPUTER_SERIAL.println(getUVStatus());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("UV Index: "));
      COMPUTER_SERIAL.println(readUVIndex());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("UV Intensity: "));
      COMPUTER_SERIAL.println(readUVIntensity());
    }

    // GPS Sensor
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("GPS Status: "));
      COMPUTER_SERIAL.println(getGPSStatus());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("GPS Quality: "));
      COMPUTER_SERIAL.println(readGPSQuality());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("GPS Satellites: "));
      COMPUTER_SERIAL.println(readGPSSatellites());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("GPS Accuracy: "));
      COMPUTER_SERIAL.println(readGPSAccuracy());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("GPS Altitude: "));
      COMPUTER_SERIAL.println(readGPSAltitude());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("GPS Latitude: "));
      COMPUTER_SERIAL.println(readGPSLatitude());
    }
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("GPS Longitude: "));
      COMPUTER_SERIAL.println(readGPSLongitude());
    }

    // Send data to server after all sensor reads
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("Send Data to Server Status: "));
      COMPUTER_SERIAL.println(sendAllSensorGroupsToServer());
    }
  }
}