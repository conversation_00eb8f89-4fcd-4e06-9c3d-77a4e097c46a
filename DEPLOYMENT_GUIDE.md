# 機器人教學系統部署指南

## 🏗️ 系統架構概覽

本系統採用前後端分離架構，包含以下組件：
- **前端**: React + Vite (端口 8085)
- **後端**: Node.js + Express (端口 8086) 
- **資料庫**: PostgreSQL (端口 5432)
- **資料庫管理**: Adminer (端口 8087)

## 🧪 本地測試過程記錄

Lawson已完成完整的本地測試，以下是詳細的測試步驟和結果：

### 1. 資料庫設置與測試

#### 啟動PostgreSQL資料庫
```bash
cd server
docker-compose up -d postgres
```

**測試結果**: ✅ PostgreSQL成功啟動在端口5432

#### 啟動Adminer資料庫管理介面
```bash
docker-compose up -d adminer
```

**測試結果**: ✅ Adminer成功啟動在端口8087，可通過 `http://localhost:8087` 訪問

#### 初始化資料庫架構和測試資料（暂未使用sql script）
```bash
node init-correct-data.js
```

**測試結果**: ✅ 成功創建以下測試資料：
- ESP32_001: 90筆記錄
- ESP32_002: 45筆記錄  
- ESP32_003: 1筆記錄
- 總計: 136筆感測器資料記錄

### 2. 後端API測試

#### 啟動後端服務器
```bash
cd server/src
node server.js
```

**測試結果**: ✅ 後端服務器成功啟動在端口8086

#### API端點測試
所有API端點均測試通過：

```bash
# 1. 獲取設備列表
curl http://localhost:8086/api/uuids
# 結果: ["ESP32_003", "ESP32_001", "ESP32_002"]

# 2. 獲取最新感測器資料
curl "http://localhost:8086/api/latest?uuid=ESP32_001"
# 結果: 返回完整的感測器資料JSON

# 3. 獲取分頁資料
curl "http://localhost:8086/api/data?pageSize=5&offset=0"
# 結果: 返回分頁格式的資料

# 4. 獲取統計資訊
curl http://localhost:8086/api/stats
# 結果: {"totalRecords": 136, "totalDevices": 3, "devices": [...]}
```

### 3. 前端配置確認

前端已配置為連接到正確的後端端口：
- 開發環境: `http://localhost:8086`
- 生產環境: `http://**********:8086`

## 🚀 服務器部署

### 階段二：代碼部署

#### 2.1 代碼上傳
將整個項目代碼上傳到服務器，建議路徑：`/opt/robot-teaching-system/`

#### 2.2 創建環境變量文件
在 `server/` 目錄下創建 `.env` 文件：
```bash
# 生產環境配置
NODE_ENV=production
PORT=8086

# 資料庫配置 (請根據實際情況修改)
DATABASE_URL=postgresql://blockly_user:blockly_password@localhost:5432/blockly_db

# 服務器配置
SERVER_HOST=.....
```

### 階段三：資料庫部署

#### 3.1 啟動PostgreSQL
```bash
docker-compose up -d postgres
```

#### 3.2 初始化資料庫
```bash
# 等待資料庫啟動
sleep 10

# 初始化測試資料
node init-correct-data.js
```

#### 3.3 啟動Adminer (可選)
```bash
docker-compose up -d adminer
```

### 階段四：後端部署

#### 4.1 方式一：Docker部署
```bash
docker-compose up -d app
```

#### 4.2 方式二：直接運行
```bash
npm install
npm start
```

### 階段五：前端部署 (Vite + React)

#### 5.1 構建生產版本
```bash
npm install
npm run build
```

#### 5.2 使用Nginx部署
創建Nginx配置文件 `/etc/nginx/sites-available/robot-teaching`:
```nginx
server {
    listen 8085;
    server_name **********;
    
    root /opt/robot-teaching-system/react-app/dist;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:8086/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```

啟用配置：
```bash
sudo ln -s /etc/nginx/sites-available/robot-teaching /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 🔍 部署驗證

### 6.1 服務狀態檢查
```bash
# 檢查端口占用
sudo netstat -tulpn | grep :808[5-7]
sudo netstat -tulpn | grep :5432

# 檢查Docker容器
docker-compose ps
```

### 6.2 功能測試
```bash
# 測試後端API
curl http://**********:8086/api/uuids
curl http://**********:8086/api/stats

# 測試前端 (瀏覽器訪問)
# http://**********:8085
```

### 6.3 資料庫連接測試
```bash
# 通過Adminer測試
# 訪問: http://**********:8087
# 服務器: postgres (Docker內部)
# 用戶名: blockly_user
# 密碼: blockly_password
# 資料庫: blockly_db
```