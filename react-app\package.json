{"name": "react-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.4", "@headlessui/tailwindcss": "^0.2.2", "@heroicons/react": "^2.2.0", "@remixicon/react": "^4.6.0", "@tremor/react": "^3.18.7", "axios": "^1.9.0", "blockly": "^12.0.0", "chart.js": "^4.4.9", "date-fns": "^4.1.0", "react": "^18.3.1", "react-blockly": "^9.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.1", "react-router-dom": "^7.6.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/forms": "^0.5.10", "@types/react": "^18.2.46", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "vite": "^6.3.5"}}