import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { LanguageProvider } from './contexts/LanguageContext';
import Dashboard from './pages/Dashboard';
import Programming from './pages/Programming';
import Report from './pages/Report';

function App() {
  return (
    <LanguageProvider>
      <Router>
          <Routes>
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            <Route path="/dashboard" element={<Dashboard />} />
            <Route path="/programming" element={<Programming />} />
            <Route path="/report" element={<Report />} />
          </Routes>
      </Router>
    </LanguageProvider>
  );
}

export default App;
