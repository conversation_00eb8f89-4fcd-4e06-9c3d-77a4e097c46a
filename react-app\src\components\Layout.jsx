import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';
import LanguageSelector from './LanguageSelector';

const Layout = ({ children }) => {
  const { t } = useLanguage();
  const location = useLocation();

  const isActive = (path) => {
    return location.pathname === path;
  };

  return (
    <div className="bg-white min-h-screen">
      {/* Header with Navigation and Language Switcher */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Navigation */}
          <nav className="flex space-x-1">
            <Link
              to="/dashboard"
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${
                isActive('/dashboard')
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              {t('nav.dashboard')}
            </Link>
            <Link
              to="/programming"
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${
                isActive('/programming')
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              {t('nav.programming')}
            </Link>
            <Link
              to="/report"
              className={`px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${
                isActive('/report')
                  ? 'bg-blue-500 text-white'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
              }`}
            >
              {t('nav.report')}
            </Link>
          </nav>

          {/* Language Switcher */}
          <LanguageSelector />
        </div>
      </header>

      {/* Main Content */}
      <main>{children}</main>
    </div>
  );
};

export default Layout; 