import * as Blockly from 'blockly';

/**
 * Initialize all custom Blockly blocks
 * @param {Function} getTranslation - Translation function
 */
export const initializeBlocks = (getTranslation) => {
  // Helper function for translation with fallback
  const t = (key, fallback) => {
    return getTranslation ? (getTranslation(key) || fallback) : fallback;
  };
  // WiFi Setup Block
  Blockly.Blocks['wifi_setup'] = {
    init: function() {
      this.appendDummyInput()
        .appendField(t('programming.setWifiLabel', 'Set WiFi'))
        .appendField(new Blockly.FieldTextInput('SSID'), 'SSID')
        .appendField(t('programming.wifiPassword', 'Password'))
        .appendField(new Blockly.FieldTextInput('PASSWORD'), 'PASSWORD');
      this.setPreviousStatement(true, 'setup_item');
      this.setNextStatement(true, 'setup_item');
      this.setStyle('arduino_setup_blocks');
      this.setTooltip(t('programming.setWifiTooltip', 'Set WiFi credentials'));
      this.setHelpUrl('');
    }
  };



  // Sensor Status Blocks
  const sensorStatusBlocks = [
    { type: 'get_bme280_status', label: t('programming.bme280Status', 'BME280 Status'), tooltip: t('programming.getBme280StatusTooltip', 'Get BME280 sensor status') },
    { type: 'get_pms7003_status', label: t('programming.pms7003Status', 'PMS7003 Status'), tooltip: t('programming.getPms7003StatusTooltip', 'Get PMS7003 sensor status') },
    { type: 'get_mhz19b_status', label: t('programming.mhz19bStatus', 'MHZ19B Status'), tooltip: t('programming.getMhz19bStatusTooltip', 'Get MHZ19B sensor status') },
    { type: 'get_wind_speed_status', label: t('programming.windSpeedStatus', 'Wind Speed Status'), tooltip: t('programming.getWindSpeedStatusTooltip', 'Get wind speed sensor status') },
    { type: 'get_uv_status', label: t('programming.uvStatus', 'UV Status'), tooltip: t('programming.getUvStatusTooltip', 'Get UV sensor status') },
    { type: 'get_gps_status', label: t('programming.gpsStatus', 'GPS Status'), tooltip: t('programming.getGpsStatusTooltip', 'Get GPS sensor status') },
  ];
  
  sensorStatusBlocks.forEach(({ type, label, tooltip }) => {
    Blockly.Blocks[type] = {
      init: function() {
        let fallbackLabel = label;
        if (!fallbackLabel) {
          switch(type) {
            case 'get_bme280_status':
              fallbackLabel = 'BME280 (Temperature & Humidity Sensor) Status';
              break;
            case 'get_pms7003_status':
              fallbackLabel = 'PMS7003 (PM2.5 Sensor) Status';
              break;
            case 'get_mhz19b_status':
              fallbackLabel = 'MHZ19B (CO2 Sensor) Status';
              break;
            case 'get_wind_speed_status':
              fallbackLabel = 'Wind Speed Sensor Status';
              break;
            case 'get_uv_status':
              fallbackLabel = 'UV Sensor Status';
              break;
            case 'get_gps_status':
              fallbackLabel = 'GPS Sensor Status';
              break;
            default:
              fallbackLabel = 'Sensor Status';
          }
        }
        this.appendDummyInput().appendField(fallbackLabel);
        this.setOutput(true, ['Boolean']);
        this.setStyle('logic_blocks');
        this.setTooltip(tooltip || 'Check sensor status');
        this.setHelpUrl('');
      }
    };
  });

  // Sensor Variable Blocks
  const sensorValueBlocks = [
    { type: 'read_bme_temp', label: t('programming.bmeTemperature', 'BME280 Temperature'), typeLabel: t('programming.floatLabel', 'float'), tooltip: t('programming.readBmeTemperatureTooltip', 'Read BME280 temperature') },
    { type: 'read_bme_humidity', label: t('programming.bmeHumidity', 'BME280 Humidity'), typeLabel: t('programming.floatLabel', 'float'), tooltip: t('programming.readBmeHumidityTooltip', 'Read BME280 humidity') },
    { type: 'read_bme_pressure', label: t('programming.bmePressure', 'BME280 Pressure'), typeLabel: t('programming.floatLabel', 'float'), tooltip: t('programming.readBmePressureTooltip', 'Read BME280 pressure') },
    { type: 'read_pms_pm1_0', label: t('programming.pmsPm1', 'PMS7003 PM1.0'), typeLabel: t('programming.intLabel', 'int'), tooltip: t('programming.readPm1Tooltip', 'Read PMS7003 PM1.0') },
    { type: 'read_pms_pm2_5', label: t('programming.pmsPm25', 'PMS7003 PM2.5'), typeLabel: t('programming.intLabel', 'int'), tooltip: t('programming.readPm25Tooltip', 'Read PMS7003 PM2.5') },
    { type: 'read_pms_pm10', label: t('programming.pmsPm10', 'PMS7003 PM10'), typeLabel: t('programming.intLabel', 'int'), tooltip: t('programming.readPm10Tooltip', 'Read PMS7003 PM10') },
    { type: 'read_pms_n0_3', label: t('programming.pmsN03', 'PMS7003 N0.3'), typeLabel: t('programming.intLabel', 'int'), tooltip: t('programming.readN03Tooltip', 'Read PMS7003 N0.3') },
    { type: 'read_pms_n0_5', label: t('programming.pmsN05', 'PMS7003 N0.5'), typeLabel: t('programming.intLabel', 'int'), tooltip: t('programming.readN05Tooltip', 'Read PMS7003 N0.5') },
    { type: 'read_pms_n1_0', label: t('programming.pmsN10', 'PMS7003 N1.0'), typeLabel: t('programming.intLabel', 'int'), tooltip: t('programming.readN10Tooltip', 'Read PMS7003 N1.0') },
    { type: 'read_pms_n2_5', label: t('programming.pmsN25', 'PMS7003 N2.5'), typeLabel: t('programming.intLabel', 'int'), tooltip: t('programming.readN25Tooltip', 'Read PMS7003 N2.5') },
    { type: 'read_pms_n5_0', label: t('programming.pmsN50', 'PMS7003 N5.0'), typeLabel: t('programming.intLabel', 'int'), tooltip: t('programming.readN50Tooltip', 'Read PMS7003 N5.0') },
    { type: 'read_pms_n10_0', label: t('programming.pmsN100', 'PMS7003 N10.0'), typeLabel: t('programming.intLabel', 'int'), tooltip: t('programming.readN100Tooltip', 'Read PMS7003 N10.0') },
    { type: 'read_mhz_co2', label: t('programming.mhzCo2', 'MHZ19B CO2'), typeLabel: t('programming.intLabel', 'int'), tooltip: t('programming.readCo2Tooltip', 'Read MHZ19B CO2') },
    { type: 'read_mhz_min_co2', label: t('programming.mhzMinCo2', 'MHZ19B Min CO2'), typeLabel: t('programming.intLabel', 'int'), tooltip: t('programming.readMinCo2Tooltip', 'Read MHZ19B Min CO2') },
    { type: 'read_wind_speed', label: t('programming.windSpeed', 'Wind Speed'), typeLabel: t('programming.floatLabel', 'float'), tooltip: t('programming.readWindSpeedTooltip', 'Read wind speed') },
    { type: 'read_uv_index', label: t('programming.uvIndex', 'UV Index'), typeLabel: t('programming.floatLabel', 'float'), tooltip: t('programming.readUvIndexTooltip', 'Read UV index') },
    { type: 'read_uv_intensity', label: t('programming.uvIntensity', 'UV Intensity'), typeLabel: t('programming.floatLabel', 'float'), tooltip: t('programming.readUvIntensityTooltip', 'Read UV intensity') },
    { type: 'read_gps_latitude', label: t('programming.gpsLatitude', 'GPS Latitude'), typeLabel: t('programming.floatLabel', 'float'), tooltip: t('programming.readGpsLatitudeTooltip', 'Read GPS latitude') },
    { type: 'read_gps_longitude', label: t('programming.gpsLongitude', 'GPS Longitude'), typeLabel: t('programming.floatLabel', 'float'), tooltip: t('programming.readGpsLongitudeTooltip', 'Read GPS longitude') },
    { type: 'read_gps_altitude', label: t('programming.gpsAltitude', 'GPS Altitude'), typeLabel: t('programming.floatLabel', 'float'), tooltip: t('programming.readGpsAltitudeTooltip', 'Read GPS altitude') },
    { type: 'read_gps_satellites', label: t('programming.gpsSatellites', 'GPS Satellites'), typeLabel: t('programming.floatLabel', 'float'), tooltip: t('programming.readGpsSatellitesTooltip', 'Read GPS satellites') },
  ];
  
  sensorValueBlocks.forEach(({ type, label, typeLabel, tooltip }) => {
    Blockly.Blocks[type] = {
      init: function() {
        // Create the display text with type label
        const displayText = `${label} (${typeLabel})`;
        this.appendDummyInput().appendField(displayText);

        // Set correct output type based on sensor reading type
        let outputType;
        let blockStyle;

        // Float sensor readings
        if (['read_bme_temp', 'read_bme_humidity', 'read_bme_pressure',
             'read_wind_speed', 'read_uv_index', 'read_uv_intensity',
             'read_gps_latitude', 'read_gps_longitude', 'read_gps_altitude',
             'read_gps_accuracy', 'read_gps_satellites'].includes(type)) {
          outputType = ['float'];
          blockStyle = 'float_blocks';
        }
        // Integer sensor readings
        else if (['read_pms_pm1_0', 'read_pms_pm2_5', 'read_pms_pm10',
                  'read_pms_n0_3', 'read_pms_n0_5', 'read_pms_n1_0',
                  'read_pms_n2_5', 'read_pms_n5_0', 'read_pms_n10_0',
                  'read_mhz_co2', 'read_mhz_min_co2'].includes(type)) {
          outputType = ['int'];
          blockStyle = 'int_blocks';
        }
        // GPS quality (special case - int but from float source)
        else if (type === 'read_gps_quality') {
          outputType = ['int'];
          blockStyle = 'int_blocks';
        }
        // Unsupported blocks (wind direction, GPS speed) - keep as string for compatibility
        else {
          outputType = ['String'];
          blockStyle = 'text_blocks';
        }

        this.setOutput(true, outputType);
        this.setStyle(blockStyle);
        this.setTooltip(tooltip);
        this.setHelpUrl('');
      }
    };
  });



  // Serial Print Block
  Blockly.Blocks['serial_print'] = {
    init: function() {
      this.appendValueInput('VALUE')
        .setCheck(null)
        .appendField(t('programming.serialPrint', 'Serial print'));
      this.setPreviousStatement(true, null);
      this.setNextStatement(true, null);
      this.setStyle('procedure_blocks');
      this.setTooltip(t('programming.serialPrintTooltip', 'Send data to the Arduino serial monitor'));
      this.setHelpUrl('');
    }
  };

  // Send Log to Server Block
  Blockly.Blocks['send_log_to_server'] = {
    init: function() {
      const logTypeDropdown = new Blockly.FieldDropdown([
        [t('programming.logMessage', 'Message'), 'MESSAGE'],
        [t('programming.logAlert', 'Alert'), 'ALERT'],
        [t('programming.logWarning', 'Warning'), 'WARNING']
      ]);
      
      this.appendDummyInput()
        .appendField(t('programming.sendLogToServer', 'Send'))
        .appendField(logTypeDropdown, 'LOG_TYPE')
        .appendField(t('programming.logToServer', 'log to server'));
      
      this.appendValueInput('MESSAGE')
        .setCheck(['String'])
        .appendField(t('programming.logMessage', 'message'));
      
      this.setInputsInline(true);
      this.setPreviousStatement(true, null);
      this.setNextStatement(true, null);
      this.setStyle('procedure_blocks');
      this.setTooltip(t('programming.sendLogToServerTooltip', 'Send log message to server with specified log level'));
      this.setHelpUrl('');
    }
  };


  // Send Grouped Sensor Data to Server Block (New Unified Block)
  Blockly.Blocks['send_grouped_sensor_data'] = {
    init: function() {
      const sensorGroupDropdown = new Blockly.FieldDropdown([
        [t('programming.bme280Sensor', 'BME280 (Temperature & Humidity)'), 'BME280'],
        [t('programming.pms7003Sensor', 'PMS7003 (Air Quality)'), 'PMS7003'],
        [t('programming.mhz19bSensor', 'MHZ19B (CO2)'), 'MHZ19B'],
        [t('programming.windSensor', 'Wind Sensor'), 'WIND'],
        [t('programming.ltr390Sensor', 'LTR390 (UV)'), 'LTR390'],
        [t('programming.gpsSensor', 'GPS'), 'GPS'],
        [t('programming.allSensors', 'All Sensors'), 'ALL']
      ]);

      this.appendDummyInput()
        .appendField(t('programming.sendGroupedData', 'Send'))
        .appendField(sensorGroupDropdown, 'SENSOR_GROUP')
        .appendField(t('programming.dataToServer', 'data to server'));
      this.setPreviousStatement(true, null);
      this.setNextStatement(true, null);
      this.setStyle('procedure_blocks');
      this.setTooltip(t('programming.sendGroupedDataTooltip', 'Send grouped sensor data to server using new structured format'));
      this.setHelpUrl('');
    }
  };

  // Delay Block
  Blockly.Blocks['delay'] = {
    init: function() {
      this.appendValueInput('DELAY_MS')
        .setCheck(['int'])
        .appendField(t('programming.delayLabel', 'Delay'));
      
      this.appendDummyInput()
        .appendField(t('programming.delayMilliseconds', 'milliseconds'));
        
      this.setInputsInline(true);
      this.setPreviousStatement(true, null);
      this.setNextStatement(true, null);
      this.setStyle('loop_blocks');
      this.setTooltip(t('programming.delayTooltip', 'Wait for specified milliseconds'));
      this.setHelpUrl('');
    }
  };

  // Value Blocks
  Blockly.Blocks['int_value'] = {
    init: function() {
      this.appendDummyInput()
        .appendField(t('programming.intLabel', 'int:'))
        .appendField(new Blockly.FieldTextInput('0', function(text) {
          if (text === '' || /^-?\d+$/.test(text)) return text;
          return null;
        }), 'INT');
      this.setOutput(true, ['int']);
      this.setStyle('int_blocks');
      this.setTooltip(t('programming.integerValue', 'Integer value'));
      this.setHelpUrl('');
    }
  };

  Blockly.Blocks['float_value'] = {
    init: function() {
      this.appendDummyInput()
        .appendField(t('programming.floatLabel', 'float:'))
        .appendField(new Blockly.FieldTextInput('0.0', function(text) {
          if (text === '' || /^-?\d*(\.\d*)?$/.test(text)) return text;
          return null;
        }), 'FLOAT');
      this.setOutput(true, ['float']);
      this.setStyle('float_blocks');
      this.setTooltip(t('programming.floatValue', 'Float value'));
      this.setHelpUrl('');
    }
  };

  Blockly.Blocks['string_value'] = {
    init: function() {
      this.appendDummyInput().appendField('"').appendField(new Blockly.FieldTextInput('text'), 'STRING').appendField('"');
      this.setOutput(true, ['String']);
      this.setStyle('text_blocks');
      this.setTooltip(t('programming.stringValue', 'String value'));
      this.setHelpUrl('');
    }
  };



  // Arduino Setup Block
  Blockly.Blocks['arduino_setup'] = {
    init: function() {
      this.appendDummyInput().appendField(t('programming.setupFunction', 'Setup Function'));
      this.appendStatementInput('SETUP_BODY').setCheck('setup_item');
      this.setStyle('arduino_setup_blocks');
      this.setTooltip(t('programming.setupTooltip', 'Arduino setup function'));
      this.setHelpUrl('');
      this.setPreviousStatement(false);
      this.setNextStatement(false);
      this.setShadow(false);
    },
    customContextMenu: function(options) {
      options = options.filter(opt => opt.text !== Blockly.Msg['ADD_TO_TOOLBOX']);
      return options;
    }
  };
  Blockly.Blocks['arduino_setup'].maxInstances = 1;

  // Arduino Loop Block
  Blockly.Blocks['arduino_loop'] = {
    init: function() {
      this.appendDummyInput().appendField(t('programming.loopFunction', 'Loop Function'));
      this.appendStatementInput('LOOP_BODY').setCheck(null);
      this.setStyle('arduino_loop_blocks');
      this.setTooltip(t('programming.loopTooltip', 'Arduino loop function'));
      this.setHelpUrl('');
      this.setPreviousStatement(false);
      this.setNextStatement(false);
      this.setShadow(false);
    },
    customContextMenu: function(options) {
      options = options.filter(opt => opt.text !== Blockly.Msg['ADD_TO_TOOLBOX']);
      return options;
    }
  };
  Blockly.Blocks['arduino_loop'].maxInstances = 1;

  // Logic Compare Block
  Blockly.Blocks['logic_compare'] = {
    init: function() {
      const OP_OPTIONS = [
        ['=', 'EQ'],
        ['!=', 'NEQ'],
        ['<', 'LT'],
        ['<=', 'LTE'],
        ['>', 'GT'],
        ['>=', 'GTE']
      ];
      
      this.appendValueInput('A');
      const opDropdown = new Blockly.FieldDropdown(OP_OPTIONS);
      this.appendDummyInput().appendField(opDropdown, 'OP');
      this.appendValueInput('B');
      this.setInputsInline(true);
      this.setOutput(true, ['Boolean']);
      this.setStyle('logic_blocks');
      this.setTooltip(t('programming.compareTooltip', 'Compare two values'));
      this.setHelpUrl('');
      
      // Always allow all types - keep connections flexible
      this.getInput('A').setCheck(['String', 'int', 'float']);
      this.getInput('B').setCheck(['String', 'int', 'float']);
    },
    onchange: function() {
      if (!this.workspace || this.isInFlyout) return;

      // Simple type detection helper
      const getBlockType = (block) => {
        if (!block) return null;
        if (block.type === 'int_variables_get') return 'int';
        if (block.type === 'float_variables_get') return 'float';
        if (block.type === 'text_variables_get') return 'string';
        if (block.outputConnection && block.outputConnection.check) {
          const types = block.outputConnection.check;
          if (types.includes('int')) return 'int';
          if (types.includes('float')) return 'float';
          if (types.includes('String')) return 'string';
        }
        return null;
      };

      const inputA = this.getInput('A')?.connection?.targetBlock();
      const inputB = this.getInput('B')?.connection?.targetBlock();
      const typeA = getBlockType(inputA);
      const typeB = getBlockType(inputB);
      const operator = this.getFieldValue('OP');
      
      let warning = null;
      
      if (typeA && typeB) {
        const isStringInvolved = typeA === 'string' || typeB === 'string';
        const isUnsupportedStringOp = operator !== 'EQ' && operator !== 'NEQ';

        if (isStringInvolved && isUnsupportedStringOp) {
          warning = t('programming.stringOperatorWarning', 
            'String values can only be compared using = or ≠ operators');
        } else if (typeA !== typeB) {
          warning = t('programming.typeMismatchError', 
            'Type mismatch - comparing different types may produce unexpected results');
        }
      }
      
      if (this.warningText !== warning) {
        this.setWarningText(warning);
      }
    }
  };

    // Array Aggregate Block - Only accepts numeric arrays
  Blockly.Blocks['array_aggregate'] = {
    init: function() {
      const aggregateOptions = [
        [t('programming.arraySum', 'Sum'), 'SUM'],
        [t('programming.arrayAverage', 'Average'), 'AVERAGE'],
        [t('programming.arrayMin', 'Minimum'), 'MIN'],
        [t('programming.arrayMax', 'Maximum'), 'MAX']
      ];
      
      this.appendValueInput('LIST')
        .setCheck(['int[]', 'float[]']) // Only allow numeric arrays
        .appendField(t('programming.arrayGet', 'Get'))
        .appendField(new Blockly.FieldDropdown(aggregateOptions), 'OP')
        .appendField(t('programming.arrayOf', 'of array'));
        
      this.setOutput(true, ['float']);
      this.setStyle('float_blocks');
      this.setTooltip(t('programming.arrayAggregateTooltip', 'Calculate aggregate value from numeric array (int[] or float[])'));
      this.setHelpUrl('');
    }
  };

  // Enhanced Array Creation Block with Type Consistency Validation (Simplified)
  Blockly.Blocks['lists_create_with'] = {
    init: function() {
      this.setStyle('list_blocks');
      this.setOutput(true, ['Array']);
      // Apply the mutator extension
      Blockly.Extensions.apply('lists_create_with_mutator', this, true);
    }
  };

  // Mutator helper blocks for lists_create_with
  Blockly.Blocks['lists_create_with_container'] = {
    init: function() {
      this.setStyle('list_blocks');
      this.appendDummyInput()
          .appendField(t('programming.createList', 'create list'));
      this.appendStatementInput('STACK');
      this.setTooltip(t('programming.createListTooltip', 'Add, remove, or reorder sections to reconfigure this list block.'));
      this.contextMenu = false;
    }
  };

  Blockly.Blocks['lists_create_with_item'] = {
    init: function() {
      this.setStyle('list_blocks');
      this.appendDummyInput()
          .appendField('item');
      this.setPreviousStatement(true);
      this.setNextStatement(true);
      this.setTooltip(t('programming.itemTooltip', 'Add an item to the list.'));
      this.contextMenu = false;
    }
  };

  // Mutator helper blocks for text_join
  Blockly.Blocks['text_join_container'] = {
    init: function() {
      this.setStyle('text_blocks');
      this.appendDummyInput()
          .appendField(t('programming.joinText', 'join text'));
      this.appendStatementInput('STACK');
      this.setTooltip(t('programming.joinTextTooltip', 'Add, remove, or reorder sections to reconfigure this text block.'));
      this.contextMenu = false;
    }
  };

  Blockly.Blocks['text_join_item'] = {
    init: function() {
      this.setStyle('text_blocks');
      this.appendDummyInput()
      this.setPreviousStatement(true);
      this.setNextStatement(true);
      this.setTooltip(t('programming.itemTooltip', 'Add an item to the text.'));
      this.contextMenu = false;
    }
  };

  // Enhanced Text Join Block with String Type Validation (Simplified)
  Blockly.Blocks['text_join'] = {
    init: function() {
      this.setStyle('text_blocks');
      this.setOutput(true, ['String']);
      this.setTooltip(t('programming.joinTextTooltip', 'Join multiple text values together'));
      // Apply the mutator extension
      Blockly.Extensions.apply('text_join_mutator', this, true);
    }
  };

  // Type Conversion Blocks
  Blockly.Blocks['convert_to_string'] = {
    init: function() {
      this.appendValueInput('VALUE')
        .setCheck(['int', 'float'])
        .appendField(t('programming.convertToString', 'Convert to String'));
      this.setOutput(true, ['String']);
      this.setStyle('text_blocks');
      this.setTooltip(t('programming.convertToStringTooltip', 'Convert integer or float to string'));
      this.setHelpUrl('');
    }
  };

  Blockly.Blocks['convert_to_int'] = {
    init: function() {
      this.appendValueInput('VALUE')
        .setCheck(['String', 'float'])
        .appendField(t('programming.convertToInt', 'Convert to Integer'));
      this.setOutput(true, ['int']);
      this.setStyle('int_blocks');
      this.setTooltip(t('programming.convertToIntTooltip', 'Convert string or float to integer'));
      this.setHelpUrl('');
    }
  };

  Blockly.Blocks['convert_to_float'] = {
    init: function() {
      this.appendValueInput('VALUE')
        .setCheck(['String', 'int'])
        .appendField(t('programming.convertToFloat', 'Convert to Float'));
      this.setOutput(true, ['float']);
      this.setStyle('float_blocks');
      this.setTooltip(t('programming.convertToFloatTooltip', 'Convert string or integer to float'));
      this.setHelpUrl('');
    }
  };

  // Integer Variable Blocks
  Blockly.Blocks['int_variables_get'] = {
    init: function() {
      this.appendDummyInput()
        .appendField(t('programming.intLabel', 'int:'))
        .appendField(new Blockly.FieldVariable('my int variable', null, ['int'], 'int'), 'VAR');
      this.setOutput(true, ['int']);
      this.setStyle('int_blocks');
      this.setTooltip(t('programming.getIntVariableTooltip', 'Get integer variable value'));
      this.setHelpUrl('');
    }
  };

  Blockly.Blocks['int_variables_set'] = {
    init: function() {
      this.appendValueInput('VALUE')
        .setCheck(['int'])
        .appendField(t('programming.setIntVariable', 'set int'))
        .appendField(new Blockly.FieldVariable('my int variable', null, ['int'], 'int'), 'VAR')
        .appendField(t('programming.toValue', 'to'));
      this.setPreviousStatement(true, null);
      this.setNextStatement(true, null);
      this.setStyle('int_blocks');
      this.setTooltip(t('programming.setIntVariableTooltip', 'Set integer variable to a value'));
      this.setHelpUrl('');
    }
  };

  // Float Variable Blocks
  Blockly.Blocks['float_variables_get'] = {
    init: function() {
      this.appendDummyInput()
        .appendField(t('programming.floatLabel', 'float:'))
        .appendField(new Blockly.FieldVariable('my float variable', null, ['float'], 'float'), 'VAR');
      this.setOutput(true, ['float']);
      this.setStyle('float_blocks');
      this.setTooltip(t('programming.getFloatVariableTooltip', 'Get float variable value'));
      this.setHelpUrl('');
    }
  };

  Blockly.Blocks['float_variables_set'] = {
    init: function() {
      this.appendValueInput('VALUE')
        .setCheck(['float'])
        .appendField(t('programming.setFloatVariable', 'set float'))
        .appendField(new Blockly.FieldVariable('my float variable', null, ['float'], 'float'), 'VAR')
        .appendField(t('programming.toValue', 'to'));
      this.setPreviousStatement(true, null);
      this.setNextStatement(true, null);
      this.setStyle('float_blocks');
      this.setTooltip(t('programming.setFloatVariableTooltip', 'Set float variable to a value'));
      this.setHelpUrl('');
    }
  };

  // Text Variable Blocks
  Blockly.Blocks['text_variables_get'] = {
    init: function() {
      this.appendDummyInput()
        .appendField(t('programming.textLabel', 'text:'))
        .appendField(new Blockly.FieldVariable('my text variable', null, ['String'], 'String'), 'VAR');
      this.setOutput(true, ['String']);
      this.setStyle('text_blocks');
      this.setTooltip(t('programming.getTextVariableTooltip', 'Get text variable value'));
      this.setHelpUrl('');
    }
  };

  Blockly.Blocks['text_variables_set'] = {
    init: function() {
      this.appendValueInput('VALUE')
        .setCheck(['String'])
        .appendField(t('programming.setTextVariable', 'set text'))
        .appendField(new Blockly.FieldVariable('my text variable', null, ['String'], 'String'), 'VAR')
        .appendField(t('programming.toValue', 'to'));
      this.setPreviousStatement(true, null);
      this.setNextStatement(true, null);
      this.setStyle('text_blocks');
      this.setTooltip(t('programming.setTextVariableTooltip', 'Set text variable to a value'));
      this.setHelpUrl('');
    }
  };

  // Text Append Block
  Blockly.Blocks['text_append'] = {
    init: function() {
      this.appendDummyInput()
        .appendField(t('programming.textAppendTo', 'to'))
        .appendField(new Blockly.FieldVariable('my text variable', null, ['String'], 'String'), 'VAR')
        .appendField(t('programming.textAppendText', 'append text'));
      this.appendValueInput('TEXT')
        .setCheck(['String']);
      this.setInputsInline(true);
      this.setPreviousStatement(true, null);
      this.setNextStatement(true, null);
      this.setStyle('text_blocks');
      this.setTooltip(t('programming.textAppendTooltip', 'Append text to the end of the specified variable'));
      this.setHelpUrl('');
    }
  };

    // Text Length Block (override built-in to ensure correct typing)
  Blockly.Blocks['text_length'] = {
    init: function() {
      this.appendValueInput('VALUE')
        .setCheck(['String'])
        .appendField(t('programming.textLengthLabel', 'length of'));
      this.setOutput(true, ['int']);
      this.setStyle('int_blocks');
      this.setTooltip(t('programming.textLengthTooltip', 'Returns the number of letters (including spaces) in the provided text'));
      this.setHelpUrl('');
    }
  };

  // Lists Length Block (override built-in to ensure correct typing)
  Blockly.Blocks['lists_length'] = {
    init: function() {
      this.appendValueInput('VALUE')
        .setCheck(['Array'])
        .appendField(t('programming.arrayLengthLabel', 'length of'));
      this.setOutput(true, ['int']);
      this.setStyle('int_blocks');
      this.setTooltip(t('programming.arrayLengthTooltip', 'Returns the length of a list'));
      this.setHelpUrl('');
    }
  };

  // Lists Get Index Block (custom version without first/last/random options)
  Blockly.Blocks['lists_getIndex'] = {
    init: function() {
      var WHERE = [
        [t('programming.arrayIndexFromStart', 'from start'), 'FROM_START'],
        [t('programming.arrayIndexFromEnd', 'from end'), 'FROM_END']
      ];
      this.appendValueInput('VALUE')
          .setCheck(['Array'])
          .appendField(t('programming.arrayIndexInList', 'in list'));
      this.appendDummyInput()
          .appendField(t('programming.arrayIndexGet', 'get')); // Replaced dropdown with text
      this.appendValueInput('AT')
          .setCheck(['int'])
          .appendField(new Blockly.FieldDropdown(WHERE), 'WHERE');
      this.setInputsInline(true);
      // Since 'GET' is the only mode, this block always has an output.
      this.setOutput(true, ['int', 'float', 'String']);
      this.setStyle('list_blocks');
      this.setTooltip(t('programming.arrayGetIndexTooltip', 'Returns the item at the specified position in a list'));
      this.setHelpUrl('');
      this.setMutator(null); // Remove mutator functionality
    },
    onchange: function() {
      // Since mode is always GET, we only need to update the output type.
      if (!this.isInFlyout && this.workspace) {
        this.updateOutputTypeFromInput();
      }
    },
    
    // Helper function to infer output type from input array
    updateOutputTypeFromInput: function() {
      let desiredElementType = ['int', 'float', 'String'];
      let desiredStyle = 'list_blocks';

      // Check for a connected block and determine the desired state
      const valueInput = this.getInput('VALUE');
      const connectedBlock = valueInput && valueInput.connection && valueInput.connection.targetBlock();
      
      if (connectedBlock && connectedBlock.outputConnection && connectedBlock.outputConnection.check) {
        const arrayTypes = connectedBlock.outputConnection.check;
        
        if (arrayTypes.includes('int[]')) {
          desiredElementType = ['int'];
          desiredStyle = 'int_blocks';
        } else if (arrayTypes.includes('float[]')) {
          desiredElementType = ['float'];
          desiredStyle = 'float_blocks';
        } else if (arrayTypes.includes('String[]')) {
          desiredElementType = ['String'];
          desiredStyle = 'text_blocks';
        }
      }

      // Compare current state to desired state and update only if necessary
      const currentTypes = this.outputConnection ? this.outputConnection.check : null;
      const typesChanged = !currentTypes || 
                            currentTypes.length !== desiredElementType.length ||
                            !desiredElementType.every(type => currentTypes.includes(type));

      if (typesChanged) {
        this.setOutput(true, desiredElementType);
      }

      if (this.getStyleName() !== desiredStyle) {
        this.setStyle(desiredStyle);
      }
    }
  };

  // Lists Set Index Block (custom version without first/last/random options)
  Blockly.Blocks['lists_setIndex'] = {
    init: function() {
      var MODE = [
        [t('programming.arrayIndexSet', 'set'), 'SET'],
        [t('programming.arrayIndexInsertAt', 'insert at'), 'INSERT']
      ];
      var WHERE = [
        [t('programming.arrayIndexFromStart', 'from start'), 'FROM_START'],
        [t('programming.arrayIndexFromEnd', 'from end'), 'FROM_END']
      ];
      this.appendValueInput('LIST')
          .setCheck(['Array'])
          .appendField(t('programming.arrayIndexInList', 'in list'));
      this.appendDummyInput()
          .appendField(new Blockly.FieldDropdown(MODE), 'MODE');
      this.appendValueInput('AT')
          .setCheck(['int'])
          .appendField(new Blockly.FieldDropdown(WHERE), 'WHERE');
      this.appendValueInput('TO')
          .setCheck(['int', 'float', 'String']) // Default to all possible element types
          .appendField(t('programming.arrayIndexToValue', 'to'));
      this.setInputsInline(true);
      this.setPreviousStatement(true, null);
      this.setNextStatement(true, null);
      this.setStyle('list_blocks');
      this.setTooltip(t('programming.arraySetIndexTooltip', 'Sets the item at the specified position in a list'));
      this.setHelpUrl('');
      this.setMutator(null); // Remove mutator functionality
    },
    onchange: function() {
      // Dynamic type inference based on input array
      if (!this.isInFlyout && this.workspace) {
        this.updateToInputTypeFromArray();
      }
    },
    
    // Helper function to infer TO input type from array
    updateToInputTypeFromArray: function() {
      const listInput = this.getInput('LIST');
      const toInput = this.getInput('TO');
      
      if (listInput && listInput.connection && listInput.connection.targetBlock() && toInput) {
        const connectedBlock = listInput.connection.targetBlock();
        
        // Check if connected block has specific array type in its output
        if (connectedBlock.outputConnection && connectedBlock.outputConnection.check) {
          const arrayTypes = connectedBlock.outputConnection.check;
          
          // Extract element type from array type
          let elementType = ['int', 'float', 'String']; // Default fallback
          
          if (arrayTypes.includes('int[]')) {
            elementType = ['int'];
          } else if (arrayTypes.includes('float[]')) {
            elementType = ['float'];
          } else if (arrayTypes.includes('String[]')) {
            elementType = ['String'];
          }
          
          // Only update if the type actually changed
          const currentTypes = toInput.connection ? toInput.connection.check : null;
          const typesChanged = !currentTypes || 
                              currentTypes.length !== elementType.length ||
                              !elementType.every(type => currentTypes.includes(type));
          
          if (typesChanged) {
            toInput.setCheck(elementType);
          }
        }
      } else if (toInput) {
        // No input connected - reset to default only if needed
        const currentTypes = toInput.connection ? toInput.connection.check : null;
        const defaultTypes = ['int', 'float', 'String'];
        const needsReset = !currentTypes || 
                          currentTypes.length !== defaultTypes.length ||
                          !defaultTypes.every(type => currentTypes.includes(type));
        
        if (needsReset) {
          toInput.setCheck(defaultTypes);
        }
      }
    }
  };

  // Lists Get Sublist Block (custom version with proper label order)
  Blockly.Blocks['lists_getSublist'] = {
    init: function() {
      var WHERE1 = [
        [t('programming.arrayIndexFromStart', 'from start #'), 'FROM_START'],
        [t('programming.arrayIndexFromEnd', 'from end #'), 'FROM_END']
      ];
      var WHERE2 = [
        [t('programming.arrayIndexFromStart', 'from start #'), 'FROM_START'],
        [t('programming.arrayIndexFromEnd', 'from end #'), 'FROM_END']
      ];
      
      this.appendValueInput('LIST')
          .setCheck(['Array'])
          .appendField(t('programming.arraySublistGetSublist', 'get sublist from list'));
      this.appendValueInput('AT1')
          .setCheck(['int'])
          .appendField(new Blockly.FieldDropdown(WHERE1), 'WHERE1');
      this.appendValueInput('AT2')
          .setCheck(['int'])
          .appendField(t('programming.arraySublistTo', 'to'))
          .appendField(new Blockly.FieldDropdown(WHERE2), 'WHERE2');
      this.setInputsInline(true);
      this.setOutput(true, ['Array']);
      this.setStyle('list_blocks');
      this.setTooltip(t('programming.arraySublistTooltip', 'Creates a copy of a portion of a list'));
      this.setHelpUrl('');
      this.setMutator(null); // Remove mutator functionality
    },
    onchange: function() {
      // Dynamic type inference based on input array
      if (!this.isInFlyout && this.workspace) {
        this.updateOutputTypeFromInput();
      }
    },
    
    // Helper function to infer output type from input array
    updateOutputTypeFromInput: function() {
      const listInput = this.getInput('LIST');
      if (listInput && listInput.connection && listInput.connection.targetBlock()) {
        const connectedBlock = listInput.connection.targetBlock();
        
        // Check if connected block has specific array type in its output
        if (connectedBlock.outputConnection && connectedBlock.outputConnection.check) {
          const arrayTypes = connectedBlock.outputConnection.check;
          
          // Only update if the type actually changed
          const currentTypes = this.outputConnection ? this.outputConnection.check : null;
          const typesChanged = !currentTypes || 
                              currentTypes.length !== arrayTypes.length ||
                              !arrayTypes.every(type => currentTypes.includes(type));
          
          if (typesChanged) {
            // Sublist should have same type as input array
            this.setOutput(true, arrayTypes);
          }
        }
      }
    }
  };

  // Math Arithmetic Block (custom version with proper type checking)
  Blockly.Blocks['math_arithmetic'] = {
    init: function() {
      this.appendValueInput('A')
          .setCheck(['int', 'float']);
      this.appendDummyInput()
          .appendField(new Blockly.FieldDropdown([
            ['+', 'ADD'],
            ['-', 'MINUS'],
            ['×', 'MULTIPLY'],
            ['÷', 'DIVIDE'],
            ['^', 'POWER']
          ]), 'OP');
      this.appendValueInput('B')
          .setCheck(['int', 'float']);
      this.setInputsInline(true);
      this.setOutput(true, ['float']); // Always output float for arithmetic operations
      this.setStyle('float_blocks');
      this.setTooltip(t('programming.mathArithmeticTooltip', 'Perform arithmetic operations'));
      this.setHelpUrl('');
    }
  };

  // Math Single Block (custom version with proper type checking)
  Blockly.Blocks['math_single'] = {
    init: function() {
      this.appendValueInput('NUM')
          .setCheck(['int', 'float'])
          .appendField(new Blockly.FieldDropdown([
            [t('programming.mathSquareRoot', 'square root'), 'ROOT'],
            [t('programming.mathAbsolute', 'absolute'), 'ABS'],
            ['-', 'NEG'],
            ['ln', 'LN'],
            ['log10', 'LOG10'],
            ['e^', 'EXP'],
            ['10^', 'POW10']
          ]), 'OP');
      this.setOutput(true, ['float']); // Always output float for single math operations
      this.setStyle('float_blocks');
      this.setTooltip(t('programming.mathSingleTooltip', 'Perform single-value math operations'));
      this.setHelpUrl('');
    }
  };

  // Math Round Block (custom version with proper type checking)
  Blockly.Blocks['math_round'] = {
    init: function() {
      this.appendValueInput('NUM')
          .setCheck(['float'])
          .appendField(new Blockly.FieldDropdown([
            [t('programming.mathRound', 'round'), 'ROUND'],
            [t('programming.mathRoundUp', 'round up'), 'ROUNDUP'],
            [t('programming.mathRoundDown', 'round down'), 'ROUNDDOWN']
          ]), 'OP');
      this.setOutput(true, ['int']); // Round operations output integers
      this.setStyle('int_blocks');
      this.setTooltip(t('programming.mathRoundTooltip', 'Round numbers to integers'));
      this.setHelpUrl('');
    }
  };

  // Register mutators with error handling to avoid duplicate registration
  try {
    Blockly.Extensions.registerMutator(
      'lists_create_with_mutator',
      {
        itemCount_: 3,

        // Block shape update method
        updateShape_: function() {
          // Remove existing inputs
          if (this.itemCount_ == 0) {
            if (this.getInput('EMPTY')) {
              this.removeInput('EMPTY');
            }
          } else {
            if (this.getInput('EMPTY')) {
              this.removeInput('EMPTY');
            }
          }

          // Remove any extra inputs
          for (var i = 0; this.getInput('ADD' + i); i++) {
            this.removeInput('ADD' + i);
          }

          // Add the appropriate inputs
          if (this.itemCount_ == 0) {
            this.appendDummyInput('EMPTY')
                .appendField(t('programming.createEmptyList', 'create empty list'));
          } else {
            for (var m = 0; m < this.itemCount_; m++) {
              var input = this.appendValueInput('ADD' + m)
                  .setAlign(Blockly.ALIGN_RIGHT);
              if (m == 0) {
                input.appendField(t('programming.createListWith', 'create list with'));
              }
            }
          }

          this.validateArrayTypes();
        },

        // Type validation
        onchange: function() {
          if (!this.workspace || this.isInFlyout || this.workspace.isDragging()) return;

          try {
            this.validateArrayTypes();
          } catch (error) {
            console.warn('Array type validation error:', error);
          }
        },

        validateArrayTypes: function() {
          if (!this.workspace || this.isInFlyout) return;

          let hasFloat = false;
          let hasInt = false;
          let hasString = false;
          let hasArray = false; // 检测是否有数组类型

          // Check all connected elements
          for (let i = 0; i < this.itemCount_; i++) {
            const input = this.getInput('ADD' + i);
            if (input && input.connection && input.connection.targetBlock()) {
              const connectedBlock = input.connection.targetBlock();
              const blockType = this.getBlockOutputType(connectedBlock);
              switch (blockType) {
                case 'float':
                  hasFloat = true;
                  break;
                case 'int':
                  hasInt = true;
                  break;
                case 'String':
                  hasString = true;
                  break;
                case 'Array':
                  hasArray = true; // 检测到数组类型
                  break;
              }
            }
          }

          // 检查是否有数组嵌套
          if (hasArray) {
            this.setWarningText(t('programming.arrayNestedError', 'Arrays cannot contain other arrays (sublists)'));
            return; // 直接返回，不设置输出类型
          }

          // Determine final array type and validate compatibility (strict type separation)
          let detectedType = 'int[]'; // Default

          if (hasString && (hasFloat || hasInt)) {
            this.setWarningText(t('programming.typeMismatchError', 'Type mismatch error - all array values should have compatible types'));
            detectedType = 'String[]';
          } else if (hasFloat && hasInt) {
            // Strict type separation - int and float are incompatible
            this.setWarningText(t('programming.typeMismatchError', 'Type mismatch error - all array values should have compatible types'));
            detectedType = 'float[]'; // Default to float for display purposes
          } else if (hasFloat) {
            this.setWarningText(null);
            detectedType = 'float[]';
          } else if (hasInt) {
            this.setWarningText(null);
            detectedType = 'int[]';
          } else if (hasString) {
            this.setWarningText(null);
            detectedType = 'String[]';
          } else {
            this.setWarningText(null);
            detectedType = 'int[]';
          }

          // Update output type
          this.setOutput(true, ['Array', detectedType]);
        },

        getBlockOutputType: function(block) {
          switch (block.type) {
            case 'string_value':
            case 'String':
              return 'String';
            case 'int_value':
              return 'int';
            case 'float_value':
              return 'float';
            case 'logic_boolean':
              return 'Boolean';
            case 'math_number': {
              const numValue = block.getFieldValue('NUM') || '0';
              return numValue.indexOf('.') === -1 && Number.isInteger(parseFloat(numValue)) ? 'int' : 'float';
            }
            case 'math_arithmetic':
            case 'math_single':
              return 'float';
            case 'math_round':
              return 'int'; // Round operations return integers
            case 'text_join':
              return 'String';
            case 'text_length':
            case 'lists_length':
              return 'int';
            case 'lists_create_with':
              return 'Array'; // 数组类型blocks
            case 'lists_getSublist':
              // lists_getSublist returns the same array type as input
              // We need to check its current output type
              if (block.outputConnection && block.outputConnection.check) {
                const outputTypes = block.outputConnection.check;
                if (outputTypes.includes('int[]')) return 'Array'; // Keep as Array for consistency
                if (outputTypes.includes('float[]')) return 'Array';
                if (outputTypes.includes('String[]')) return 'Array';
              }
              return 'Array'; // Default fallback
            case 'logic_operation':
            case 'logic_compare':
            case 'logic_negate':
              return 'Boolean';
            // Variable blocks
            case 'int_variables_get':
              return 'int';
            case 'float_variables_get':
              return 'float';
            case 'text_variables_get':
              return 'String';
            // Sensor reading blocks
            case 'read_bme_temp':
            case 'read_bme_humidity':
            case 'read_bme_pressure':
            case 'read_wind_speed':
            case 'read_uv_index':
            case 'read_uv_intensity':
            case 'read_gps_latitude':
            case 'read_gps_longitude':
            case 'read_gps_altitude':
            case 'read_gps_accuracy':
              return 'float';
            case 'read_pms_pm1_0':
            case 'read_pms_pm2_5':
            case 'read_pms_pm10':
            case 'read_pms_n0_3':
            case 'read_pms_n0_5':
            case 'read_pms_n1_0':
            case 'read_pms_n2_5':
            case 'read_pms_n5_0':
            case 'read_pms_n10_0':
            case 'read_mhz_co2':
            case 'read_mhz_min_co2':
            case 'read_gps_satellites':
            case 'read_gps_quality':
              return 'int';
            case 'get_bme280_status':
            case 'get_pms7003_status':
            case 'get_mhz19b_status':
            case 'get_wind_speed_status':
            case 'get_uv_status':
            case 'get_gps_status':
              return 'Boolean';
            case 'lists_getIndex':
              // lists_getIndex returns the element type, not Array type
              // We need to check its current output type
              if (block.outputConnection && block.outputConnection.check) {
                const outputTypes = block.outputConnection.check;
                if (outputTypes.includes('int')) return 'int';
                if (outputTypes.includes('float')) return 'float';
                if (outputTypes.includes('String')) return 'String';
              }
              return 'Unknown'; // Fallback for unconnected getIndex blocks
            default:
              return 'unknown';
          }
        },

        // Serialization hooks for JSON
        saveExtraState: function() {
          return {
            'itemCount': this.itemCount_,
          };
        },

        loadExtraState: function(state) {
          this.itemCount_ = state['itemCount'];
          this.updateShape_();
        },

        // Serialization hooks for XML (legacy support)
        mutationToDom: function() {
          var container = Blockly.utils.xml.createElement('mutation');
          container.setAttribute('items', this.itemCount_);
          return container;
        },

        domToMutation: function(xmlElement) {
          this.itemCount_ = parseInt(xmlElement.getAttribute('items'), 10);
          this.updateShape_();
        },

        // Mutator UI hooks
        decompose: function(workspace) {
          var topBlock = workspace.newBlock('lists_create_with_container');
          topBlock.initSvg();
          var connection = topBlock.getInput('STACK').connection;
          for (var i = 0; i < this.itemCount_; i++) {
            var itemBlock = workspace.newBlock('lists_create_with_item');
            itemBlock.initSvg();
            connection.connect(itemBlock.previousConnection);
            connection = itemBlock.nextConnection;
          }
          return topBlock;
        },

        compose: function(topBlock) {
          var itemBlock = topBlock.getInputTargetBlock('STACK');
          var connections = [];
          while (itemBlock && !itemBlock.isInsertionMarker()) {
            connections.push(itemBlock.valueConnection_);
            itemBlock = itemBlock.nextConnection &&
                itemBlock.nextConnection.targetBlock();
          }
          // Disconnect blocks that are no longer needed
          for (var j = 0; j < this.itemCount_; j++) {
            var connection = this.getInput('ADD' + j).connection.targetConnection;
            if (connection && connections.indexOf(connection) == -1) {
              connection.disconnect();
            }
          }
          this.itemCount_ = connections.length;
          this.updateShape_();
          // Reconnect blocks
          for (var k = 0; k < this.itemCount_; k++) {
            if (connections[k]) {
              connections[k].reconnect(this, 'ADD' + k);
            }
          }
        },

        saveConnections: function(topBlock) {
          var itemBlock = topBlock.getInputTargetBlock('STACK');
          var index = 0;
          while (itemBlock) {
            var input = this.getInput('ADD' + index);
            itemBlock.valueConnection_ = input && input.connection.targetConnection;
            index++;
            itemBlock = itemBlock.nextConnection &&
                itemBlock.nextConnection.targetBlock();
          }
        }
      },
      undefined,
      ['lists_create_with_item']
    );
  } catch (error) {
    // Ignore error if extension is already registered
    if (!error.message.includes('already registered')) {
      throw error;
    }
  }

  // Text Join Mutator
  try {
    Blockly.Extensions.registerMutator(
      'text_join_mutator',
      {
        itemCount_: 2,

        // Block shape update method
        updateShape_: function() {
          // Remove existing inputs
          for (var r = 0; this.getInput('ADD' + r); r++) {
            this.removeInput('ADD' + r);
          }

          // Add inputs for current item count
          for (var s = 0; s < this.itemCount_; s++) {
            var input = this.appendValueInput('ADD' + s)
                .setCheck(['String'])  // Only allow String types
                .setAlign(Blockly.ALIGN_RIGHT);
            if (s === 0) {
              input.appendField(t('programming.createTextWith', 'create text with'));
            }
          }

          this.validateStringTypes();
        },

        // Type validation
        onchange: function() {
          if (!this.workspace || this.isInFlyout || this.workspace.isDragging()) return;

          try {
            this.validateStringTypes();
          } catch (error) {
            console.warn('Text join type validation error:', error);
          }
        },

        validateStringTypes: function() {
          if (!this.workspace || this.isInFlyout) return;

          let hasNonStringType = false;
          const nonStringBlocks = [];

          // Check all connected elements
          for (let i = 0; i < this.itemCount_; i++) {
            const input = this.getInput('ADD' + i);
            if (input && input.connection && input.connection.targetBlock()) {
              const connectedBlock = input.connection.targetBlock();
              const blockType = this.getBlockOutputType(connectedBlock);

              if (blockType !== 'String') {
                hasNonStringType = true;
                nonStringBlocks.push(connectedBlock.type);
              }
            }
          }

          // Show warning if non-String types are connected
          if (hasNonStringType) {
            this.setWarningText(t('programming.typeMismatchError',
              'Type mismatch error - all values should have compatible types'));
          } else {
            this.setWarningText(null);
          }
        },

        getBlockOutputType: function(block) {
          switch (block.type) {
            case 'string_value':
                      case 'String':
          case 'text_join':
          case 'convert_to_string':  // Allow convert_to_string blocks
          case 'text_variables_get':  // Add text variable support
            return 'String';
            case 'text_length':
            case 'lists_length':
              return 'int';
            case 'lists_create_with':
              return 'Array'; // 数组类型blocks
            case 'lists_getSublist':
              // lists_getSublist returns the same array type as input
              // We need to check its current output type
              if (block.outputConnection && block.outputConnection.check) {
                const outputTypes = block.outputConnection.check;
                if (outputTypes.includes('int[]')) return 'Array'; // Keep as Array for consistency
                if (outputTypes.includes('float[]')) return 'Array';
                if (outputTypes.includes('String[]')) return 'Array';
              }
              return 'Array'; // Default fallback
            case 'int_value':
            case 'int_variables_get':  // Add int variable support
              return 'int';
            case 'float_value':
            case 'float_variables_get':  // Add float variable support
              return 'float';
            case 'math_number': {
              const numValue = block.getFieldValue('NUM') || '0';
              return numValue.indexOf('.') === -1 && Number.isInteger(parseFloat(numValue)) ? 'int' : 'float';
            }
            case 'math_arithmetic':
            case 'math_single':
              return 'float';
            case 'math_round':
              return 'int'; // Round operations return integers
            case 'logic_boolean':
            case 'logic_operation':
            case 'logic_compare':
            case 'logic_negate':
              return 'Boolean';
            // Float sensor reading blocks
            case 'read_bme_temp':
            case 'read_bme_humidity':
            case 'read_bme_pressure':
            case 'read_wind_speed':
            case 'read_uv_index':
            case 'read_uv_intensity':
            case 'read_gps_latitude':
            case 'read_gps_longitude':
            case 'read_gps_altitude':
            case 'read_gps_accuracy':
              return 'float';
            // Integer sensor reading blocks
            case 'read_pms_pm1_0':
            case 'read_pms_pm2_5':
            case 'read_pms_pm10':
            case 'read_pms_n0_3':
            case 'read_pms_n0_5':
            case 'read_pms_n1_0':
            case 'read_pms_n2_5':
            case 'read_pms_n5_0':
            case 'read_pms_n10_0':
            case 'read_mhz_co2':
            case 'read_mhz_min_co2':
            case 'read_gps_satellites':
            case 'read_gps_quality':
              return 'int';
            case 'get_bme280_status':
            case 'get_pms7003_status':
            case 'get_mhz19b_status':
            case 'get_wind_speed_status':
            case 'get_uv_status':
            case 'get_gps_status':
              return 'Boolean';
            case 'lists_getIndex':
              // lists_getIndex returns the element type, not Array type
              // We need to check its current output type
              if (block.outputConnection && block.outputConnection.check) {
                const outputTypes = block.outputConnection.check;
                if (outputTypes.includes('int')) return 'int';
                if (outputTypes.includes('float')) return 'float';
                if (outputTypes.includes('String')) return 'String';
              }
              return 'Unknown'; // Fallback for unconnected getIndex blocks
            default:
              return 'Unknown';
          }
        },

        // Serialization hooks for JSON
        saveExtraState: function() {
          return {
            'itemCount': this.itemCount_,
          };
        },

        loadExtraState: function(state) {
          this.itemCount_ = state['itemCount'];
          this.updateShape_();
        },

        // Serialization hooks for XML (legacy support)
        mutationToDom: function() {
          var container = Blockly.utils.xml.createElement('mutation');
          container.setAttribute('items', this.itemCount_);
          return container;
        },

        domToMutation: function(xmlElement) {
          this.itemCount_ = parseInt(xmlElement.getAttribute('items'), 10);
          this.updateShape_();
        },

        // Mutator UI hooks
        decompose: function(workspace) {
          var topBlock = workspace.newBlock('text_join_container');
          topBlock.initSvg();
          var connection = topBlock.getInput('STACK').connection;
          for (var n = 0; n < this.itemCount_; n++) {
            var itemBlock = workspace.newBlock('text_join_item');
            itemBlock.initSvg();
            connection.connect(itemBlock.previousConnection);
            connection = itemBlock.nextConnection;
          }
          return topBlock;
        },

        compose: function(topBlock) {
          var itemBlock = topBlock.getInputTargetBlock('STACK');
          var connections = [];
          while (itemBlock && !itemBlock.isInsertionMarker()) {
            connections.push(itemBlock.valueConnection_);
            itemBlock = itemBlock.nextConnection &&
                itemBlock.nextConnection.targetBlock();
          }
          // Disconnect blocks that are no longer needed
          for (var p = 0; p < this.itemCount_; p++) {
            var connection = this.getInput('ADD' + p).connection.targetConnection;
            if (connection && connections.indexOf(connection) == -1) {
              connection.disconnect();
            }
          }
          this.itemCount_ = connections.length;
          this.updateShape_();
          // Reconnect blocks
          for (var q = 0; q < this.itemCount_; q++) {
            if (connections[q]) {
              connections[q].reconnect(this, 'ADD' + q);
            }
          }
        },

        saveConnections: function(topBlock) {
          var itemBlock = topBlock.getInputTargetBlock('STACK');
          var idx = 0;
          while (itemBlock) {
            var input = this.getInput('ADD' + idx);
            itemBlock.valueConnection_ = input && input.connection.targetConnection;
            idx++;
            itemBlock = itemBlock.nextConnection &&
                itemBlock.nextConnection.targetBlock();
          }
        }
      },
      undefined,
      ['text_join_item']
    );
  } catch (error) {
    // Ignore error if extension is already registered
    if (!error.message.includes('already registered')) {
      throw error;
    }
  };
};