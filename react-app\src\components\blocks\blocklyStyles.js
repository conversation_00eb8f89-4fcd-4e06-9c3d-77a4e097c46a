/**
 * Blockly programming interface styles
 */
export const blocklyProgrammingStyles = `
/* Hide Blockly scrollbars but keep functionality */
.blocklySvg .blocklyScrollbarVertical,
.blocklySvg .blocklyScrollbarHorizontal {
  opacity: 0 !important;
}

.blocklySvg:hover .blocklyScrollbarVertical,
.blocklySvg:hover .blocklyScrollbarHorizontal {
  opacity: 0.3 !important;
}

/* Fix TailwindCSS conflicts */
svg[display="none"] {
  display: none !important;
}

/* Custom scrollbar for right panel */
.modern-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #dee2e6 transparent;
}

.modern-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.modern-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.modern-scrollbar::-webkit-scrollbar-thumb {
  background-color: #dee2e6;
  border-radius: 3px;
}

.modern-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #adb5bd;
}

/* Enhanced Blockly Toolbox Styling */
.blocklyToolboxDiv {
  border-right: 1px solid #e1e8ed !important;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.08) !important;
  background: #ffffff !important;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
}

/* Toolbox Contents Container */
.blocklyToolboxContents {
  padding: 16px 8px !important;
  background: #ffffff !important;
}

/* Section Divider */
.blocklyTreeRow:not(:first-child)::before,
.blocklyToolboxCategory:not(:first-child)::before {
  content: '';
  position: absolute;
  top: -4px;
  left: 12px;
  right: 12px;
  height: 1px;
  background: linear-gradient(to right, transparent, #e2e8f0 20%, #e2e8f0 80%, transparent);
  display: block;
}

/* FOCUS: blocklyToolboxCategory - Enhanced display */
.blocklyToolboxCategory {
  min-height: 72px !important;
  display: flex !important;
  align-items: center !important;
  cursor: pointer !important;
  font-size: 22px !important;
  font-weight: 800 !important;
  letter-spacing: 0.025em !important;
  text-transform: uppercase !important;
  line-height: 1.2 !important;
  position: relative !important;
}

.blocklyToolboxCategory:hover {
  transform: none !important;
}

.blocklyToolboxCategory.blocklyTreeSelected,
.blocklyToolboxCategory.blocklyTreeRowSelected {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15) !important;
  font-weight: 900 !important;
}

/* Category Row - Make Bigger and More Prominent */
.blocklyTreeRow {
  min-height: 56px !important;
  display: flex !important;
  align-items: center !important;
  cursor: pointer !important;
  position: relative !important;
}

/* Category Label - Bigger and Bolder Text */
.blocklyTreeLabel {
  font-size: 18px !important;
  font-weight: 700 !important;
  letter-spacing: 0.02em !important;
  line-height: 1.3 !important;
  text-transform: uppercase !important;
}

/* blocklyToolboxCategory Label special handling */
.blocklyToolboxCategory .blocklyTreeLabel {
  font-size: 22px !important;
  font-weight: 800 !important;
  letter-spacing: 0.025em !important;
  line-height: 1.2 !important;
  text-transform: uppercase !important;
}

.blocklyToolboxCategory.blocklyTreeSelected .blocklyTreeLabel,
.blocklyToolboxCategory.blocklyTreeRowSelected .blocklyTreeLabel {
  font-weight: 900 !important;
}

/* Selected Category - More Prominent */
.blocklyTreeRowSelected {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  border-color: rgba(0, 0, 0, 0.15) !important;
}

.blocklyTreeRowSelected .blocklyTreeLabel {
  font-weight: 800 !important;
}

/* Category Icons */
.blocklyTreeIcon {
  width: 24px !important;
  height: 24px !important;
  margin-right: 12px !important;
}

.blocklyToolboxCategory .blocklyTreeIcon {
  width: 32px !important;
  height: 32px !important;
  margin-right: 16px !important;
}

/* Workspace styling */
.blocklyWorkspace {
  background: linear-gradient(45deg, #f8f9fa 25%, transparent 25%), 
              linear-gradient(-45deg, #f8f9fa 25%, transparent 25%), 
              linear-gradient(45deg, transparent 75%, #f8f9fa 75%), 
              linear-gradient(-45deg, transparent 75%, #f8f9fa 75%) !important;
  background-size: 20px 20px !important;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px !important;
}

/* Flyout styling */
.blocklyFlyout {
  border-left: 1px solid #e9ecef !important;
  box-shadow: -2px 0 8px rgba(0,0,0,0.1) !important;
}

/* Blockly Toolbox Label Styles */
.categoryMainLabel > .blocklyFlyoutLabelText {
  font-weight: bold !important;
  fill: #2c3e50 !important;
}

.categorySubLabel > .blocklyFlyoutLabelText {
  font-size: 18px !important;
  font-weight: normal !important;
  fill: #7f8c8d !important;
}

 /* Tooltip styles */
.tooltip {
  position: absolute;
  z-index: 50;
}

.tooltip-arrow {
  position: absolute;
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #374151;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
}

/* Show tooltip on hover for disabled buttons */
.relative:hover .tooltip {
  visibility: visible !important;
  opacity: 1 !important;
}

/* Dark mode tooltip arrow */
.dark .tooltip-arrow {
  border-top-color: #374151;
}

`; 