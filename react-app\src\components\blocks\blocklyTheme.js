import { Theme } from 'blockly';

/**
 * Modern Beautiful Theme for Blockly
 * A clean, modern theme with enhanced visual appeal
 * Following Blockly's native behavior-based color classification
 */
export const createModernTheme = () => {
  return Theme.defineTheme('modern_beautiful', {
    'base': Theme.Classic,
    'blockStyles': {
      // Standard Blockly categories - behavior-based classification
      'logic_blocks': {
        'colourPrimary': '#4c7ff9',
        'colourSecondary': '#7ba0ff',
        'colourTertiary': '#3366cc'
      },
      'loop_blocks': {
        'colourPrimary': '#4c7ff9',
        'colourSecondary': '#7ba0ff',
        'colourTertiary': '#3366cc'
      },
      'math_blocks': {
        'colourPrimary': '#ff6b6b',
        'colourSecondary': '#ff8e8e',
        'colourTertiary': '#cc5555'
      },
      // Integer blocks - keep the original red color
      'int_blocks': {
        'colourPrimary': '#ff6b6b',
        'colourSecondary': '#ff8e8e',
        'colourTertiary': '#cc5555'
      },
      // Float blocks - use yellow color
      'float_blocks': {
        'colourPrimary': '#f1c40f',
        'colourSecondary': '#f4d03f',
        'colourTertiary': '#b7950b'
      },
      'text_blocks': {
        'colourPrimary': '#51cf66',
        'colourSecondary': '#74d687',
        'colourTertiary': '#41a653'
      },
      'list_blocks': {
        'colourPrimary': '#5ba58c',
        'colourSecondary': '#7db5a3',
        'colourTertiary': '#4a8470'
      },
      'procedure_blocks': {
        'colourPrimary': '#9775fa',
        'colourSecondary': '#b197fc',
        'colourTertiary': '#7950c8'
      },
      // Special Arduino container blocks - these can have unique colors
      'arduino_setup_blocks': {
        'colourPrimary': '#f39c12',
        'colourSecondary': '#f5b041',
        'colourTertiary': '#d68910'
      },
      'arduino_loop_blocks': {
        'colourPrimary': '#e67e22',
        'colourSecondary': '#eb984e',
        'colourTertiary': '#ca6f1e'
      }
    },
    'componentStyles': {
      'workspaceBackgroundColour': '#f8f9fa',
      'toolboxBackgroundColour': '#ffffff',
      'toolboxForegroundColour': '#2c3e50',
      'flyoutBackgroundColour': '#ffffff',
      'flyoutForegroundColour': '#495057',
      'flyoutOpacity': 0.95,
      'scrollbarColour': '#dee2e6',
      'scrollbarOpacity': 0,
      'markerColour': '#4c7ff9',
      'cursorColour': '#4c7ff9'
    },
    'fontStyle': {
      'family': '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
      'weight': '600',
      'size': 16
    },
    'startHats': false
  });
};

export default createModernTheme; 