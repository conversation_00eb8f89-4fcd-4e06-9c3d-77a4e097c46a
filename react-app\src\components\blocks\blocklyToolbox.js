import * as Blockly from 'blockly';

/**
 * Blockly Toolbox Configuration
 * Provides toolbox structure and configuration for the Arduino programming interface
 */

/**
 * Creates the toolbox XML configuration for Blockly
 * @param {function} getTranslation - Translation function
 * @returns {string} Toolbox XML string
 */
export const createToolboxConfig = (getTranslation) => {
  return `
    <xml xmlns="https://developers.google.com/blockly/xml" id="toolbox" style="display: none">
      <category name="${getTranslation('programming.categorySetup')}" colour="#f39c12">
        <label text="${getTranslation('programming.setupMainLabel')}" web-class="categoryMainLabel"></label>
        <sep></sep>
        <label text="${getTranslation('programming.wifiConfiguration')}" web-class="categorySubLabel"></label>
        <block type="wifi_setup">
          <field name="SSID">SSID</field>
          <field name="PASSWORD">PASSWORD</field>
        </block>
        

      </category>
      <category name="${getTranslation('programming.categorySensors')}" colour="#51cf66">
        <label text="${getTranslation('programming.sensorsMainLabel')}" web-class="categoryMainLabel"></label>
        <sep></sep>
        <label text="${getTranslation('programming.getSensorStatus')}" web-class="categorySubLabel"></label>
        <block type="get_bme280_status"></block>
        <block type="get_pms7003_status"></block>
        <block type="get_mhz19b_status"></block>
        <block type="get_wind_speed_status"></block>
        <block type="get_uv_status"></block>
        <block type="get_gps_status"></block>

        <label text="${getTranslation('programming.getSensorVariables')}" web-class="categoryMainLabel"></label>
        <sep></sep>
        <label text="${getTranslation('programming.bme280SensorVariables')}" web-class="categorySubLabel"></label>
        <block type="read_bme_temp"></block>
        <block type="read_bme_humidity"></block>
        <block type="read_bme_pressure"></block>

        <label text="${getTranslation('programming.pms7003SensorVariables')}" web-class="categorySubLabel"></label>
        <block type="read_pms_pm1_0"></block>
        <block type="read_pms_pm2_5"></block>
        <block type="read_pms_pm10"></block>
        <block type="read_pms_n0_3"></block>
        <block type="read_pms_n0_5"></block>
        <block type="read_pms_n1_0"></block>
        <block type="read_pms_n2_5"></block>
        <block type="read_pms_n5_0"></block>
        <block type="read_pms_n10_0"></block>

        <label text="${getTranslation('programming.mhz19bSensorVariables')}" web-class="categorySubLabel"></label>
        <block type="read_mhz_co2"></block>
        <block type="read_mhz_min_co2"></block>

        <label text="${getTranslation('programming.windSpeedSensorVariables')}" web-class="categorySubLabel"></label>
        <block type="read_wind_speed"></block>

        <label text="${getTranslation('programming.uvSensorVariables')}" web-class="categorySubLabel"></label>
        <block type="read_uv_index"></block>
        <block type="read_uv_intensity"></block>

        <label text="${getTranslation('programming.gpsSensorVariables')}" web-class="categorySubLabel"></label>
        <block type="read_gps_latitude"></block>
        <block type="read_gps_longitude"></block>
        <block type="read_gps_altitude"></block>
        <block type="read_gps_satellites"></block>
      </category>
      <category name="${getTranslation('programming.categoryValuesVariables')}" colour="#5ba58c">
        <label text="${getTranslation('programming.valuesVariablesMainLabel')}" web-class="categoryMainLabel"></label>
        <sep></sep>
        
        <label text="${getTranslation('programming.integerSection')}" web-class="categorySubLabel"></label>
        <block type="int_value"></block>
        <button text="${getTranslation('programming.createIntVariable') || 'Create int variable...'}" callbackKey="CREATE_INT_VARIABLE"></button>
        <block type="int_variables_get"></block>
        <block type="int_variables_set">
          <value name="VALUE">
            <shadow type="int_value">
              <field name="INT">0</field>
            </shadow>
          </value>
        </block>
        
        <label text="${getTranslation('programming.floatSection')}" web-class="categorySubLabel"></label>
        <block type="float_value"></block>
        <button text="${getTranslation('programming.createFloatVariable') || 'Create float variable...'}" callbackKey="CREATE_FLOAT_VARIABLE"></button>
        <block type="float_variables_get"></block>
        <block type="float_variables_set">
          <value name="VALUE">
            <shadow type="float_value">
              <field name="FLOAT">0.0</field>
            </shadow>
          </value>
        </block>
        
        <label text="${getTranslation('programming.textSection')}" web-class="categorySubLabel"></label>
        <block type="string_value"></block>
        <button text="${getTranslation('programming.createTextVariable') || 'Create text variable...'}" callbackKey="CREATE_TEXT_VARIABLE"></button>
        <block type="text_variables_get"></block>
        <block type="text_variables_set">
          <value name="VALUE">
            <shadow type="string_value">
              <field name="STRING">text</field>
            </shadow>
          </value>
        </block>
        <block type="text_join">
          <mutation items="2"></mutation>
          <value name="ADD0">
            <shadow type="string_value">
              <field name="STRING">Hello</field>
            </shadow>
          </value>
          <value name="ADD1">
            <shadow type="string_value">
              <field name="STRING">World</field>
            </shadow>
          </value>
        </block>
        <block type="text_length"></block>
        <block type="text_append">
          <value name="TEXT">
            <shadow type="string_value">
              <field name="STRING">more text</field>
            </shadow>
          </value>
        </block>
        
        <label text="${getTranslation('programming.arraySection')}" web-class="categorySubLabel"></label>
        <block type="lists_create_with">
          <mutation items="3"></mutation>
        </block>
        <block type="lists_getIndex">
          <mutation statement="false" at="true"></mutation>
          <field name="MODE">GET</field>
          <field name="WHERE">FROM_START</field>
          <value name="AT">
            <shadow type="int_value">
              <field name="INT">1</field>
            </shadow>
          </value>
        </block>
        <block type="lists_setIndex">
          <mutation at="true"></mutation>
          <field name="MODE">SET</field>
          <field name="WHERE">FROM_START</field>
          <value name="AT">
            <shadow type="int_value">
              <field name="INT">1</field>
            </shadow>
          </value>
        </block>
        <block type="lists_getSublist">
          <mutation at1="true" at2="true"></mutation>
          <field name="WHERE1">FROM_START</field>
          <field name="WHERE2">FROM_START</field>
          <value name="AT1">
            <shadow type="int_value">
              <field name="INT">0</field>
            </shadow>
          </value>
          <value name="AT2">
            <shadow type="int_value">
              <field name="INT">1</field>
            </shadow>
          </value>
        </block>
        <block type="lists_length"></block>
        <block type="array_aggregate"></block>
      </category>
      <category name="${getTranslation('programming.categoryOperatorsConversions')}" colour="#ff6b6b">
        <label text="${getTranslation('programming.operatorsConversionsMainLabel')}" web-class="categoryMainLabel"></label>
        <sep></sep>
        <label text="${getTranslation('programming.logicalOperators')}" web-class="categorySubLabel"></label>
        <block type="logic_compare">
          <field name="OP">EQ</field>
        </block>
        <block type="logic_operation">
          <field name="OP">AND</field>
        </block>
        <block type="logic_negate"></block>
        
        <label text="${getTranslation('programming.mathOperators')}" web-class="categorySubLabel"></label>
        <block type="math_arithmetic"></block>
        <block type="math_single">
          <value name="NUM">
            <shadow type="float_value">
              <field name="FLOAT">9.0</field>
            </shadow>
          </value>
        </block>
        <block type="math_round"></block>

        <label text="${getTranslation('programming.typeConversions')}" web-class="categorySubLabel"></label>
        <block type="convert_to_string"></block>
        <block type="convert_to_int"></block>
        <block type="convert_to_float"></block>
      </category>
      <category name="${getTranslation('programming.categoryCommunication')}" colour="#9775fa">
        <label text="${getTranslation('programming.communicationMainLabel')}" web-class="categoryMainLabel"></label>
        <sep></sep>
        <label text="${getTranslation('programming.serialCommunication')}" web-class="categorySubLabel"></label>
        <block type="serial_print">
          <value name="VALUE">
            <shadow type="string_value">
              <field name="STRING">Hello World</field>
            </shadow>
          </value>
        </block>
        
        <label text="${getTranslation('programming.networkCommunication')}" web-class="categorySubLabel"></label>
        <block type="send_grouped_sensor_data"></block>
        <block type="send_log_to_server">
          <field name="LOG_TYPE">MESSAGE</field>
          <value name="MESSAGE">
            <shadow type="string_value">
              <field name="STRING">Log message</field>
            </shadow>
          </value>
        </block>
      </category>
      <category name="${getTranslation('programming.categoryControl')}" colour="#4c7ff9">
        <label text="${getTranslation('programming.controlMainLabel')}" web-class="categoryMainLabel"></label>
        <sep></sep>
        <label text="${getTranslation('programming.conditionalControl')}" web-class="categorySubLabel"></label>
        <block type="controls_if"></block>
        
        <label text="${getTranslation('programming.timeControl')}" web-class="categorySubLabel"></label>
        <block type="delay">
          <value name="DELAY_MS">
            <block type="int_value">
              <field name="INT">1000</field>
            </block>
          </value>
        </block>
      </category>
    </xml>
  `;
};

/**
 * Creates Blockly workspace configuration options
 * @param {string} toolbox - Toolbox XML string
 * @param {object} theme - Blockly theme object
 * @returns {object} Blockly options configuration
 */
export const createWorkspaceOptions = (toolbox, theme) => {
  return {
    toolbox: toolbox,
    theme: theme,
    renderer: 'zelos', // Use Zelos renderer (Scratch-3.0 style blocks)
    grid: {
      spacing: 20,
      length: 3,
      colour: '#e9ecef',
      snap: true
    },
    zoom: {
      controls: true,
      wheel: true,
      startScale: 1.0,
      maxScale: 3,
      minScale: 0.3,
      scaleSpeed: 1.2
    },
    trashcan: true,
    sounds: true,
    move: {
      scrollbars: {
        horizontal: true,
        vertical: true
      },
      drag: true,
      wheel: true
    }
  };
};

/**
 * Registers workspace callbacks for toolbox buttons
 * @param {Blockly.Workspace} workspace - The Blockly workspace
 */
export const registerWorkspaceCallbacks = (workspace) => {
  workspace.registerButtonCallback('CREATE_INT_VARIABLE', (button) => {
    Blockly.Variables.createVariableButtonHandler(button.getTargetWorkspace(), null, 'int');
  });
  
  workspace.registerButtonCallback('CREATE_FLOAT_VARIABLE', (button) => {
    Blockly.Variables.createVariableButtonHandler(button.getTargetWorkspace(), null, 'float');
  });
  
  workspace.registerButtonCallback('CREATE_TEXT_VARIABLE', (button) => {
    Blockly.Variables.createVariableButtonHandler(button.getTargetWorkspace(), null, 'String');
  });
};

export default { createToolboxConfig, createWorkspaceOptions, registerWorkspaceCallbacks }; 