import * as Blockly from 'blockly';

/**
 * Ensure setup and loop blocks exist and have correct properties
 */
export const ensureSetupLoopBlocks = (workspace) => {
  if (!workspace) return;

  const processBlock = (type, x, y) => {
    const blocks = workspace.getBlocksByType(type, false);
    
    // Remove extra blocks, keep only one
    if (blocks.length > 1) {
      for (let i = 1; i < blocks.length; i++) {
        blocks[i].dispose(true);
      }
    }
    
    // Create block if none exists
    let block = blocks[0];
    if (!block) {
      block = workspace.newBlock(type);
      block.initSvg();
      block.render();
      block.moveBy(x, y);
    }
    
    // Set properties
    block.setDeletable(false);
    block.setMovable(true);
  };

  processBlock('arduino_setup', 40, 40);
  processBlock('arduino_loop', 300, 40);
};

/**
 * Add listener to prevent multiple setup/loop blocks
 */
export const addBlockCreationListener = (workspace) => {
  if (!workspace) return;
  
  workspace.addChangeListener((event) => {
    if (event.type === Blockly.Events.BLOCK_CREATE) {
      const block = workspace.getBlockById(event.blockId);
      if (block && (block.type === 'arduino_setup' || block.type === 'arduino_loop')) {
        const existing = workspace.getBlocksByType(block.type, false);
        if (existing.length > 1) {
          block.dispose(true);
          Blockly.alert(`Only one ${block.type} block is allowed.`);
        }
      }
    }
  });
};

/**
 * Load workspace from localStorage
 */
export const loadWorkspaceFromStorage = (workspace, setProjectName, setLoadedProjectName) => {
  if (!workspace) return false;
  
  try {
    const savedData = localStorage.getItem('blockly_current_workspace');
    
    if (savedData) {
      const data = JSON.parse(savedData);
      
      // Clear existing blocks
      workspace.clear();
      
      // Load saved workspace
      Blockly.serialization.workspaces.load(data.workspace, workspace);
      
      const projectName = data.projectName || 'MyProject';
      setProjectName(projectName);
      setLoadedProjectName(projectName);
    }
    
    ensureSetupLoopBlocks(workspace);
    return true;
  } catch (error) {
    console.error('Failed to load workspace:', error);
    ensureSetupLoopBlocks(workspace);
    return false;
  }
};

/**
 * Save workspace to localStorage
 */
export const saveWorkspace = (workspace, projectName) => {
  if (!workspace) return false;
  
  try {
    const state = Blockly.serialization.workspaces.save(workspace);
    const name = projectName.trim() || 'MyProject';
    
    const saveData = {
      workspace: state,
      projectName: name,
      timestamp: new Date().toISOString()
    };
    
    localStorage.setItem('blockly_current_workspace', JSON.stringify(saveData));
    return true;
  } catch (error) {
    console.error('Save failed:', error);
    return false;
  }
};

/**
 * Clear workspace
 */
export const clearWorkspace = (workspace, setProjectName, setLoadedProjectName) => {
  if (!workspace) return;
  
  workspace.clear();
  ensureSetupLoopBlocks(workspace);
  
  setProjectName('MyProject');
  setLoadedProjectName('MyProject');
};

/**
 * Simple debounce utility
 */
export const debounce = (func, delay) => {
  let timeoutId;
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

/**
 * Check if event should trigger auto save
 */
export const shouldAutoSave = (event) => {
  return event.type === Blockly.Events.BLOCK_CREATE ||
         event.type === Blockly.Events.BLOCK_DELETE ||
         event.type === Blockly.Events.BLOCK_MOVE ||
         event.type === Blockly.Events.BLOCK_CHANGE;
};

/**
 * Get all saved projects (for future use if needed)
 */
export const getAllProjects = () => {
  try {
    const savedData = localStorage.getItem('blockly_current_workspace');
    if (savedData) {
      const data = JSON.parse(savedData);
      return [{
        projectName: data.projectName,
        timestamp: new Date(data.timestamp),
        data: data
      }];
    }
  } catch (error) {
    console.error('Failed to get projects:', error);
  }
  return [];
}; 