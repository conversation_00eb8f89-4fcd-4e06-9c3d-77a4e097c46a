// 'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Card, LineChart, Table, TableBody, TableCell, TableHead, TableHeaderCell, TableRow } from '@tremor/react';
import { ChevronDownIcon } from '@heroicons/react/20/solid';
import { Menu, MenuButton, MenuItems } from '@headlessui/react';
import {
    sensorConfigs,
    getAllAvailableSensors,
    getSensorI18nKey,
    getSensorGroupI18nKey,
    timeRangeOptions,
    defaultTimeRange
} from '../../config/sensorConfig';
import {
    processHistoryDataForCharts,
    getNumericValue,
    calculateSensorStatistics,
    filterHistoryDataByTimeRange
} from '../../utils/sensorDataUtils';
import { useLanguage } from '../../contexts/LanguageContext';

function classNames(...classes) {
    return classes.filter(Boolean).join(' ');
}

export default function CompositionLineChart({
    title = "Sensor Data Composition",
    description = "Multi-sensor data comparison and analysis",
    currentData = null,
    historyData = []
}) {
    const { t } = useLanguage();

    // Default selected sensors: CO2, minCO2, temperature, humidity
    const getCustomDefaultSensors = () => {
        const allSensors = getAllAvailableSensors();
        const defaultFields = ['mhz19b_co2', 'mhz19b_min_co2', 'bme280_temperature', 'bme280_humidity'];
        return allSensors.filter(sensor => defaultFields.includes(sensor.field));
    };

    const [selectedSensors, setSelectedSensors] = useState(getCustomDefaultSensors());
    const [searchTerm, setSearchTerm] = useState('');
    const [selectedTimeRange, setSelectedTimeRange] = useState(defaultTimeRange);
    const dropdownRef = useRef(null);

    const allAvailableSensors = getAllAvailableSensors();

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setSearchTerm('');
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    // Filter sensors based on search term (using translated names)
    const filteredSensors = allAvailableSensors.filter(sensor => {
        const translatedName = t(getSensorI18nKey(sensor.field));
        return translatedName.toLowerCase().includes(searchTerm.toLowerCase()) ||
            sensor.groupTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
            sensor.field.toLowerCase().includes(searchTerm.toLowerCase());
    });

    // Handle sensor selection with 6 sensor limit
    const handleSensorToggle = (sensor) => {
        setSelectedSensors(prev => {
            const isSelected = prev.some(s => s.field === sensor.field);
            if (isSelected) {
                return prev.filter(s => s.field !== sensor.field);
            } else {
                // Limit to maximum 6 sensors
                if (prev.length >= 6) {
                    return prev; // Don't add more if already at limit
                }
                return [...prev, sensor];
            }
        });
    };

    // Handle group-level selection
    const handleGroupToggle = (config) => {
        const groupSensors = allAvailableSensors.filter(sensor => sensor.groupTitle === config.title);
        const selectedGroupSensors = selectedSensors.filter(sensor => 
            groupSensors.some(groupSensor => groupSensor.field === sensor.field)
        );
        
        // Check if all sensors in this group are selected
        const allGroupSelected = selectedGroupSensors.length === groupSensors.length;
        
        if (allGroupSelected) {
            // Deselect all sensors in this group
            setSelectedSensors(prev => 
                prev.filter(sensor => !groupSensors.some(groupSensor => groupSensor.field === sensor.field))
            );
        } else {
            // Select sensors in this group (respecting the 6 sensor limit)
            const sensorsToAdd = groupSensors.filter(sensor => 
                !selectedSensors.some(selectedSensor => selectedSensor.field === sensor.field)
            );
            
            setSelectedSensors(prev => {
                const availableSlots = 6 - prev.length;
                const sensorsToActuallyAdd = sensorsToAdd.slice(0, availableSlots);
                return [...prev, ...sensorsToActuallyAdd];
            });
        }
    };

    // Get group selection state (none, partial, all)
    const getGroupSelectionState = (config) => {
        const groupSensors = allAvailableSensors.filter(sensor => sensor.groupTitle === config.title);
        const selectedGroupSensors = selectedSensors.filter(sensor => 
            groupSensors.some(groupSensor => groupSensor.field === sensor.field)
        );
        
        if (selectedGroupSensors.length === 0) {
            return 'none';
        } else if (selectedGroupSensors.length === groupSensors.length) {
            return 'all';
        } else {
            return 'partial';
        }
    };

    // Filter history data based on selected time range using shared utility
    const getFilteredHistoryData = () => {
        return filterHistoryDataByTimeRange(historyData, selectedTimeRange);
    };

    // Process history data for charts using standardized utilities - limit to 200 data points
    // Create custom field mappings with translated names for chart fields
    const fieldMappings = selectedSensors.map(sensor => ({
        chartField: t(getSensorI18nKey(sensor.field)),
        dataField: sensor.field
    }));

    const filteredHistoryData = getFilteredHistoryData();
    const processedChartData = processHistoryDataForCharts(
        filteredHistoryData,
        fieldMappings,
        200,
        selectedTimeRange
    );

    // Generate summary data from current readings (using translated names)
    const summary = selectedSensors.map(sensor => {
        const currentValue = currentData ? getNumericValue(currentData[sensor.field]) : null;
        const formattedValue = currentValue !== null ? `${currentValue}${sensor.unit}` : '--';

        return {
            name: t(getSensorI18nKey(sensor.field)),
            value: formattedValue,
            bgColor: sensor.bgColor,
        };
    });

    // Extract categories and colors for LineChart (using translated names to match chart fields)
    const categories = selectedSensors.map(sensor => t(getSensorI18nKey(sensor.field)));
    const colors = selectedSensors.map(sensor => sensor.color);

    // Calculate statistics for selected sensors using filtered data
    const statistics = calculateSensorStatistics(processedChartData, selectedSensors, currentData, t, getSensorI18nKey);

    // Generic value formatter for chart
    const chartValueFormatter = (number) => {
        if (number === null || number === undefined) return '--';
        return number.toFixed(1);
    };

    return (
        <>
            <div className="flex items-center justify-between mb-4">
                <div>
                    <h3 className="font-medium text-tremor-content-strong">
                        {title}
                    </h3>
                    <p className="mt-1 text-tremor-default leading-6 text-tremor-content">
                        {description}
                    </p>
                </div>

                {/* Sensor Selection Dropdown */}
                <Menu as="div" className="relative inline-block text-left" ref={dropdownRef}>
                    <div>
                        <MenuButton className="inline-flex w-full justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
                            {t('dashboard.selectSensors') || 'Select Sensors'} ({selectedSensors.length})
                            <ChevronDownIcon aria-hidden="true" className="-mr-1 size-5 text-gray-400" />
                        </MenuButton>
                    </div>

                    <MenuItems
                        transition
                        className="absolute right-0 z-10 mt-2 w-[32rem] origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in"
                    >
                        <div className="p-6">
                            <div className="flex items-center justify-between mb-3">
                                <span className="text-sm font-medium text-gray-700">
                                    {t('dashboard.selectSensorsLabel') || 'Select Sensors (max 6)'}
                                </span>
                                <span className="text-xs text-gray-500">
                                    {selectedSensors.length}/6
                                </span>
                            </div>

                            {/* Search input */}
                            <div className="mb-3">
                                <input
                                    type="text"
                                    placeholder={t('dashboard.searchSensors') || 'Search sensors...'}
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                                />
                            </div>

                            <div className="space-y-2 text-sm text-gray-700 max-h-96 overflow-y-auto">
                                {/* Group sensors by category */}
                                                                {sensorConfigs.map(config => {
                                    const groupSensors = filteredSensors.filter(sensor => sensor.groupTitle === config.title);
                                    if (groupSensors.length === 0) return null;

                                    const groupSelectionState = getGroupSelectionState(config);

                                    return (
                                        <div key={config.title} className="mb-3">
                                            <div className="flex items-center justify-between mb-2 border-b border-gray-200 pb-1">
                                                <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide">
                                                    {t(getSensorGroupI18nKey(config.title))}
                                                </div>
                                                <button
                                                    onClick={() => handleGroupToggle(config)}
                                                    className="flex items-center text-xs text-blue-600 hover:text-blue-800 font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                                                    disabled={selectedSensors.length >= 6 && groupSelectionState === 'none'}
                                                >
                                                    <div className="relative mr-1">
                                                        <input
                                                            type="checkbox"
                                                            checked={groupSelectionState === 'all'}
                                                            ref={(el) => {
                                                                if (el) el.indeterminate = groupSelectionState === 'partial';
                                                            }}
                                                            onChange={() => {}} // Handled by button click
                                                            className="w-3 h-3 text-blue-600 border-gray-300 rounded focus:ring-blue-500 pointer-events-none"
                                                        />
                                                    </div>
                                                    {groupSelectionState === 'all' ? 
                                                        (t('dashboard.deselectGroup') || 'Deselect All') : 
                                                        (t('dashboard.selectGroup') || 'Select All')
                                                    }
                                                </button>
                                            </div>
                                            {groupSensors.map(sensor => {
                                                const isSelected = selectedSensors.some(s => s.field === sensor.field);
                                                const isMaxReached = selectedSensors.length >= 6 && !isSelected;
                                                const translatedName = t(getSensorI18nKey(sensor.field));
                                                return (
                                                    <div key={sensor.field} className={`flex items-center ${isMaxReached ? 'opacity-50' : ''}`}>
                                                        <input
                                                            id={`sensor-${sensor.field}`}
                                                            type="checkbox"
                                                            checked={isSelected}
                                                            onChange={() => handleSensorToggle(sensor)}
                                                            disabled={isMaxReached}
                                                            className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 disabled:opacity-50"
                                                        />
                                                        <label htmlFor={`sensor-${sensor.field}`} className={`ml-2 text-sm font-medium flex items-center ${isMaxReached ? 'text-gray-400' : 'text-gray-900'}`}>
                                                            <div className={`w-3 h-3 rounded mr-2 ${sensor.bgColor} ${isMaxReached ? 'opacity-50' : ''}`}></div>
                                                            {translatedName}
                                                            <span className="text-gray-500 ml-1">({sensor.unit})</span>
                                                        </label>
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    );
                                })}

                                {filteredSensors.length === 0 && (
                                    <div className="text-center text-gray-500 py-4">
                                        {t('dashboard.noSensorsFound') || `No sensors found matching "${searchTerm}"`}
                                    </div>
                                )}
                            </div>
                        </div>
                    </MenuItems>
                </Menu>
            </div>

            {/* Divider with Tabs */}
            <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8" aria-label="Tabs">
                    {timeRangeOptions.map((option) => (
                        <button
                            key={option.value}
                            onClick={() => setSelectedTimeRange(option.value)}
                            className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${selectedTimeRange === option.value
                                ? 'border-blue-500 text-blue-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                }`}
                        >
                            {t(option.i18nKey) || option.label}
                        </button>
                    ))}
                </nav>
            </div>

            <div className="bg-white rounded-lg">
                <div className="mt-8 grid grid-cols-1 gap-8 md:grid-cols-4">
                    <div className="md:col-span-3">
                        {processedChartData.length > 0 && selectedSensors.length > 0 ? (
                            <>
                                <LineChart
                                    data={processedChartData}
                                    index="date"
                                    categories={categories}
                                    colors={colors}
                                    valueFormatter={chartValueFormatter}
                                    yAxisWidth={55}
                                    showLegend={false}
                                    className="hidden h-72 sm:block"
                                />
                                <LineChart
                                    data={processedChartData}
                                    index="date"
                                    categories={categories}
                                    colors={colors}
                                    valueFormatter={chartValueFormatter}
                                    showYAxis={false}
                                    showLegend={false}
                                    startEndOnly={true}
                                    className="h-72 sm:hidden"
                                />
                            </>
                        ) : (
                            <div className="flex items-center justify-center h-72 text-gray-500">
                                <div className="text-center">
                                    {selectedSensors.length === 0 ? (
                                        <>
                                            <p className="text-lg font-medium">{t('dashboard.noSensorsSelected') || 'No sensors selected'}</p>
                                            <p className="text-sm">{t('dashboard.selectSensorsToView') || 'Please select sensors to view data'}</p>
                                        </>
                                    ) : (
                                        <>
                                            <p className="text-lg font-medium">{t('dashboard.noDataAvailable') || 'No data available'}</p>
                                            <p className="text-sm">{t('dashboard.selectDeviceToView') || 'Please select a device to view sensor data'}</p>
                                        </>
                                    )}
                                </div>
                            </div>
                        )}
                    </div>
                    <div className="md:col-span-1">
                        {selectedSensors.length > 0 ? (
                            <>
                                <ul role="list" className="space-y-6">
                                    {summary.map((item) => (
                                        <li key={item.name} className="flex space-x-3">
                                            <span
                                                className={classNames(item.bgColor, 'w-1 shrink-0 rounded')}
                                                aria-hidden={true}
                                            />
                                            <div className="flex w-full items-center justify-between space-x-4 truncate">
                                                <p className="truncate text-tremor-default text-tremor-content">
                                                    {item.name}
                                                </p>
                                                <p className="font-medium text-tremor-content-strong">
                                                    {item.value}
                                                </p>
                                            </div>

                                        </li>
                                    ))}
                                </ul>
                            </>
                        ) : (
                            <div className="flex items-center justify-center h-32 text-gray-500">
                                <div className="text-center">
                                    <p className="text-sm">{t('dashboard.noSensorsSelected') || 'No sensors selected'}</p>
                                    <p className="text-xs">{t('dashboard.useDropdownToSelect') || 'Use the dropdown to select sensors'}</p>
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                {/* Statistics Table */}
                {statistics.length > 0 && (
                    <div className="mt-8">
                        <Table className="mt-4">
                            <TableHead>
                                <TableRow className="border-b border-tremor-border">
                                    <TableHeaderCell className="text-tremor-content-strong">
                                        {t('dashboard.sensor') || 'Sensor'}
                                    </TableHeaderCell>
                                    <TableHeaderCell className="text-right text-tremor-content-strong">
                                        {t('dashboard.current') || 'Current'}
                                    </TableHeaderCell>
                                    <TableHeaderCell className="text-right text-tremor-content-strong">
                                        {t('dashboard.min') || 'Min'}
                                    </TableHeaderCell>
                                    <TableHeaderCell className="text-right text-tremor-content-strong">
                                        {t('dashboard.max') || 'Max'}
                                    </TableHeaderCell>
                                    <TableHeaderCell className="text-right text-tremor-content-strong">
                                        {t('dashboard.avg') || 'Avg'}
                                    </TableHeaderCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {statistics.map((stat, index) => (
                                    <TableRow key={index}>
                                        <TableCell className="font-medium text-tremor-content-strong">
                                            <div className="flex space-x-3">
                                                <span
                                                    className={classNames(stat.color, 'w-1 shrink-0 rounded')}
                                                    aria-hidden={true}
                                                />
                                                <span>
                                                    {stat.name}
                                                    <span className="text-gray-400 font-normal ml-1">
                                                        ({t(getSensorGroupI18nKey(stat.groupTitle))})
                                                    </span>
                                                </span>
                                            </div>
                                        </TableCell>
                                        <TableCell className="text-right">
                                            {stat.current}{stat.unit}
                                        </TableCell>
                                        <TableCell className="text-right">
                                            {stat.min}{stat.unit}
                                        </TableCell>
                                        <TableCell className="text-right">
                                            {stat.max}{stat.unit}
                                        </TableCell>
                                        <TableCell className="text-right">
                                            {stat.avg}{stat.unit}
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </div>
                )}
            </div>
        </>
    );
} 