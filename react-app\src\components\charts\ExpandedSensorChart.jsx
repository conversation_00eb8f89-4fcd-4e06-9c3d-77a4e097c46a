// 'use client';

import React, { useState } from 'react';
import { Ri<PERSON>ddFill, RiExpandDiagonalLine, RiCloseLine } from '@remixicon/react';
import {
  Card,
  LineChart,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeaderCell,
  TableRow
} from '@tremor/react';
import { ChevronDownIcon } from '@heroicons/react/20/solid';
import { Menu, MenuButton, MenuItems } from '@headlessui/react';
import { getSensorI18nKey, getAllAvailableSensors, timeRangeOptions, defaultTimeRange } from '../../config/sensorConfig';
import { processHistoryDataForCharts, calculateSensorStatistics, filterHistoryDataByTimeRange } from '../../utils/sensorDataUtils';
import { useLanguage } from '../../contexts/LanguageContext';

function classNames(...classes) {
  return classes.filter(Boolean).join(' ');
}

const valueFormatter = (number) => {
  if (number === null || number === undefined) return '--';
  return `${Intl.NumberFormat('us').format(Number(number.toFixed(1)))}`;
};

export default function ExpandedSensorChart({
  title = "Expanded Sensor Analysis",
  description = "Comprehensive sensor data analysis with detailed visualization and comparison tools",
  historyData = [],
  currentData = {},
  isExpanded = false,
  onToggleExpand = null,
  sensorGroup = null, // The specific sensor group/config to display
  initialSelectedSensors = null // Initial selected sensors from the card view
}) {
  const { t } = useLanguage();

  // Get sensors for the specific group only
  const getGroupSensors = () => {
    if (!sensorGroup) return [];
    const allSensors = getAllAvailableSensors();
    return allSensors.filter(sensor => sensor.groupTitle === sensorGroup.title);
  };

  const [selectedSensors, setSelectedSensors] = useState(() => {
    // Use initial selected sensors if provided, otherwise use all group sensors
    return initialSelectedSensors || getGroupSensors();
  });

  // Time range state for filtering
  const [selectedTimeRange, setSelectedTimeRange] = useState(defaultTimeRange);

  // Only sensors from the current group
  const availableGroupSensors = getGroupSensors();

  // Handle sensor selection toggle (within the group)
  const handleSensorToggle = (sensor) => {
    setSelectedSensors(prev => {
      const isSelected = prev.some(s => s.field === sensor.field);
      if (isSelected) {
        return prev.filter(s => s.field !== sensor.field);
      } else {
        // No limit since we're only showing sensors from one group
        return [...prev, sensor];
      }
    });
  };

  // Filter history data based on selected time range using shared utility
  const getFilteredHistoryData = () => {
    return filterHistoryDataByTimeRange(historyData, selectedTimeRange);
  };

  // Process history data for charts
  const fieldMappings = selectedSensors.map(sensor => ({
    chartField: t(getSensorI18nKey(sensor.field)),
    dataField: sensor.field
  }));

  const filteredHistoryData = getFilteredHistoryData();
  const processedChartData = processHistoryDataForCharts(
    filteredHistoryData,
    fieldMappings,
    200,
    selectedTimeRange
  );

  // Extract categories and colors for LineChart
  const categories = selectedSensors.map(sensor => t(getSensorI18nKey(sensor.field)));
  const colors = selectedSensors.map(sensor => sensor.color);

  // Calculate statistics for selected sensors using filtered data
  const statistics = calculateSensorStatistics(processedChartData, selectedSensors, currentData, t, getSensorI18nKey);

  // Don't render anything if not expanded
  if (!isExpanded) return null;

  return (
    <>
      {/* Modal Overlay */}
      <div className="fixed inset-0 z-40 bg-black bg-opacity-50" onClick={onToggleExpand} />

      {/* Modal Content */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-7xl max-h-full overflow-auto">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-tremor-content-strong">
                {title}
              </h3>
              <p className="mt-1 text-tremor-default leading-6 text-tremor-content">
                {description}
              </p>
            </div>

            <div className="flex items-center space-x-2">
              {/* Sensor Selection */}
              <Menu as="div" className="relative">
                <div>
                  <MenuButton className="inline-flex items-center justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
                    <RiAddFill className="size-4 shrink-0" aria-hidden={true} />
                    {t('dashboard.manageSensors') || 'Manage Sensors'}
                    <span className="ml-1 text-xs text-gray-500">({selectedSensors.length}/{availableGroupSensors.length})</span>
                    <ChevronDownIcon aria-hidden="true" className="ml-auto size-4 text-gray-400" />
                  </MenuButton>
                </div>

                <MenuItems
                  transition
                  className="absolute right-0 z-10 mt-2 w-64 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in"
                >
                  <div className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm font-medium text-gray-700">
                        {t('dashboard.selectSensors') || 'Select Sensors'}
                      </span>
                      <span className="text-xs text-gray-500">
                        {selectedSensors.length}/{availableGroupSensors.length}
                      </span>
                    </div>

                    <div className="space-y-2 text-sm">
                      {availableGroupSensors.map(sensor => {
                        const isSelected = selectedSensors.some(s => s.field === sensor.field);
                        const translatedName = t(getSensorI18nKey(sensor.field));

                        return (
                          <div key={sensor.field} className="flex items-center">
                            <input
                              id={`expanded-sensor-${sensor.field}`}
                              type="checkbox"
                              checked={isSelected}
                              onChange={() => handleSensorToggle(sensor)}
                              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                            <label
                              htmlFor={`expanded-sensor-${sensor.field}`}
                              className="ml-2 text-sm font-medium flex items-center text-gray-900"
                            >
                              <div className={`w-3 h-3 rounded mr-2 ${sensor.bgColor}`}></div>
                              {translatedName}
                              <span className="text-gray-500 ml-1">({sensor.unit})</span>
                            </label>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </MenuItems>
              </Menu>

              {/* Expand/Collapse Toggle */}
              {onToggleExpand && (
                <button
                  onClick={onToggleExpand}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
                >
                  {isExpanded ? (
                    <RiCloseLine className="size-5" />
                  ) : (
                    <RiExpandDiagonalLine className="size-5" />
                  )}
                </button>
              )}
            </div>
          </div>

          {/* Time Range Tabs */}
          <div className="mt-6 border-b border-gray-200">
            <nav className="-mb-px flex space-x-6 overflow-x-auto" aria-label="Tabs">
              {timeRangeOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => setSelectedTimeRange(option.value)}
                  className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm flex-shrink-0 ${selectedTimeRange === option.value
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                >
                  {t(option.i18nKey) || option.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="mt-8">
            {/* Chart Section - Full Width */}
            <div>
              {processedChartData.length > 0 && selectedSensors.length > 0 ? (
                <>
                  {/* Desktop Chart */}
                  <LineChart
                    data={processedChartData}
                    index="date"
                    categories={categories}
                    colors={colors}
                    valueFormatter={valueFormatter}
                    yAxisWidth={60}
                    showLegend={false}
                    className={`hidden ${isExpanded ? 'h-96' : 'h-72'} sm:block`}
                    enableLegendSlider={true}
                    rotateLabelX={{
                      angle: -45,
                      verticalShift: 10,
                      xAxisHeight: 50
                    }}
                  />

                  {/* Mobile Chart */}
                  <LineChart
                    data={processedChartData}
                    index="date"
                    categories={categories}
                    colors={colors}
                    valueFormatter={valueFormatter}
                    showYAxis={false}
                    showLegend={false}
                    startEndOnly={true}
                    className={`${isExpanded ? 'h-96' : 'h-72'} sm:hidden`}
                  />
                </>
              ) : (
                <div className={`${isExpanded ? 'h-96' : 'h-72'} flex items-center justify-center text-tremor-default text-tremor-content`}>
                  {selectedSensors.length === 0
                    ? t('dashboard.noSensorsSelected') || 'No sensors selected'
                    : t('dashboard.noDataAvailable') || 'No chart data available'
                  }
                </div>
              )}

              {/* Statistics Table (only in expanded mode) */}
              {isExpanded && statistics.length > 0 && (
                <div className="mt-6">
                  <Table className="mt-4">
                    <TableHead>
                      <TableRow className="border-b border-tremor-border">
                        <TableHeaderCell className="text-tremor-content-strong">
                          {t('dashboard.sensor') || 'Sensor'}
                        </TableHeaderCell>
                        <TableHeaderCell className="text-right text-tremor-content-strong">
                          {t('dashboard.current') || 'Current'}
                        </TableHeaderCell>
                        <TableHeaderCell className="text-right text-tremor-content-strong">
                          {t('dashboard.min') || 'Min'}
                        </TableHeaderCell>
                        <TableHeaderCell className="text-right text-tremor-content-strong">
                          {t('dashboard.max') || 'Max'}
                        </TableHeaderCell>
                        <TableHeaderCell className="text-right text-tremor-content-strong">
                          {t('dashboard.avg') || 'Avg'}
                        </TableHeaderCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {statistics.map((stat, index) => (
                        <TableRow key={index}>
                          <TableCell className="font-medium text-tremor-content-strong">
                            <div className="flex space-x-3">
                              <span
                                className={classNames(stat.color, 'w-1 shrink-0 rounded')}
                                aria-hidden={true}
                              />
                              <span>{stat.name}</span>
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            {stat.current}{stat.unit}
                          </TableCell>
                          <TableCell className="text-right">
                            {stat.min}{stat.unit}
                          </TableCell>
                          <TableCell className="text-right">
                            {stat.max}{stat.unit}
                          </TableCell>
                          <TableCell className="text-right">
                            {stat.avg}{stat.unit}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </div>
          </div>
        </Card>
      </div>
    </>
  );
} 