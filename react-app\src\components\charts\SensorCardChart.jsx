// 'use client';

import React, { useState } from 'react';
import { <PERSON>, LineChart, List, ListItem, Divider } from '@tremor/react';
import { AdjustmentsVerticalIcon } from '@heroicons/react/20/solid';
import { RiExpandDiagonalLine } from '@remixicon/react';
import { Menu, MenuButton, MenuItems } from '@headlessui/react';
import { 
  sensorConfigs, 
  getSensorI18nKey, 
  getSensorGroupI18nKey,
  getAllAvailableSensors,
  defaultTimeRange
} from '../../config/sensorConfig';
import { processHistoryDataForCharts, getNumericValue } from '../../utils/sensorDataUtils';
import { useLanguage } from '../../contexts/LanguageContext';
import ExpandedSensorChart from './ExpandedSensorChart';

function classNames(...classes) {
  return classes.filter(Boolean).join(' ');
}

const valueFormatter = (number) =>
  `${Intl.NumberFormat('us').format(number).toString()}`;

export default function SensorCardChart({
  title = "Sensor Data Overview",
  description = "Real-time sensor data monitoring and analysis",
  historyData = [],
  currentData = {}
}) {
  const { t } = useLanguage();
  
  // Get all available sensors with proper color configuration
  const allAvailableSensors = getAllAvailableSensors();
  
  // Initialize selected sensors state - each config starts with all sensors selected
  const [selectedSensorsByConfig, setSelectedSensorsByConfig] = useState(() => {
    const initialState = {};
    sensorConfigs.forEach((config, index) => {
      // Find sensors from allAvailableSensors that belong to this config
      const configSensors = allAvailableSensors.filter(sensor => 
        sensor.groupTitle === config.title
      );
      initialState[index] = configSensors; // Start with all sensors selected
    });
    return initialState;
  });

  // State for expanded chart modal
  const [expandedConfigIndex, setExpandedConfigIndex] = useState(null);

  // Handle sensor selection toggle for a specific config
  const handleSensorToggle = (configIndex, sensor) => {
    setSelectedSensorsByConfig(prev => {
      const currentSelected = prev[configIndex] || [];
      const isSelected = currentSelected.some(s => s.field === sensor.field);
      
      if (isSelected) {
        // Remove sensor if already selected
        return {
          ...prev,
          [configIndex]: currentSelected.filter(s => s.field !== sensor.field)
        };
      } else {
        // Add sensor if not selected
        return {
          ...prev,
          [configIndex]: [...currentSelected, sensor]
        };
      }
    });
  };

  // Toggle expanded view for a specific config
  const toggleExpandedView = (configIndex) => {
    setExpandedConfigIndex(expandedConfigIndex === configIndex ? null : configIndex);
  };

  return (
    <>
      <h3 className="font-medium text-tremor-content-strong">
        {title}
      </h3>
      <p className="mt-1 text-tremor-default leading-6 text-tremor-content">
        {description}
      </p>
      <Divider className="my-6" />
      <div className="mt-6 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {sensorConfigs.map((config, configIndex) => {
          const selectedSensors = selectedSensorsByConfig[configIndex] || [];
          const availableConfigSensors = allAvailableSensors.filter(sensor => 
            sensor.groupTitle === config.title
          );
          
          // Create field mappings for selected sensors using translated names (like CompositionLineChart)
          const fieldMappings = selectedSensors.map(sensor => ({
            chartField: t(getSensorI18nKey(sensor.field)),
            dataField: sensor.field
          }));
          
          // Process chart data for this specific config
          const processedChartData = processHistoryDataForCharts(
            historyData, 
            fieldMappings, 
            50,
            defaultTimeRange // Use default time range for card charts
          );

          return (
            <Card key={configIndex} className="sm:mx-auto sm:max-w-md">
              <div className="flex items-center justify-between">
                <h3 className="text-tremor-default font-medium text-black">
                  {t(getSensorGroupI18nKey(config.title))}
                </h3>
                
                {/* Sensor Selection Dropdown */}
                <Menu as="div" className="relative inline-block text-left">
                  <div>
                    <MenuButton className="inline-flex items-center justify-center rounded-md bg-white p-1.5 text-gray-400 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500">
                      <AdjustmentsVerticalIcon aria-hidden="true" className="size-4" />
                    </MenuButton>
                  </div>

                  <MenuItems
                    transition
                    className="absolute right-0 z-10 mt-2 w-64 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in"
                  >
                    <div className="p-4">
                      <div className="flex items-center justify-between mb-3">
                        <span className="text-sm font-medium text-gray-700">
                          {t('dashboard.selectSensors') || 'Select Sensors'}
                        </span>
                        <span className="text-xs text-gray-500">
                          {selectedSensors.length}/{availableConfigSensors.length}
                        </span>
                      </div>

                      <div className="space-y-2 text-sm">
                        {availableConfigSensors.map(sensor => {
                          const isSelected = selectedSensors.some(s => s.field === sensor.field);
                          const translatedName = t(getSensorI18nKey(sensor.field));
                          
                          return (
                            <div key={sensor.field} className="flex items-center">
                              <input
                                id={`sensor-${configIndex}-${sensor.field}`}
                                type="checkbox"
                                checked={isSelected}
                                onChange={() => handleSensorToggle(configIndex, sensor)}
                                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                              />
                              <label 
                                htmlFor={`sensor-${configIndex}-${sensor.field}`} 
                                className="ml-2 text-sm font-medium flex items-center text-gray-900"
                              >
                                <div className={`w-3 h-3 rounded mr-2 ${sensor.bgColor}`}></div>
                                {translatedName}
                                <span className="text-gray-500 ml-1">({sensor.unit})</span>
                              </label>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </MenuItems>
                </Menu>
              </div>

              {/* Chart */}
              {processedChartData.length > 0 && selectedSensors.length > 0 ? (
                <LineChart
                  data={processedChartData}
                  index="date"
                  categories={selectedSensors.map(sensor => t(getSensorI18nKey(sensor.field)))}
                  colors={selectedSensors.map(sensor => sensor.color)}
                  valueFormatter={valueFormatter}
                  showLegend={false}
                  showYAxis={false}
                  className="mt-6 h-40"
                />
              ) : (
                <div className="mt-6 h-32 flex items-center justify-center text-tremor-default text-tremor-content">
                  {selectedSensors.length === 0 
                    ? t('dashboard.noSensorsSelected') || 'No sensors selected'
                    : t('dashboard.noDataAvailable') || 'No chart data available'
                  }
                </div>
              )}

              {/* Sensor Values List */}
              <List className="mt-2">
                {selectedSensors.map((sensor) => (
                  <ListItem key={sensor.name}>
                    <div className="flex items-center space-x-2">
                      <span
                        className={classNames(sensor.bgColor, 'h-0.5 w-3')}
                        aria-hidden={true}
                      />
                      <span className="text-black">{t(getSensorI18nKey(sensor.field))}</span>
                    </div>
                    <span className="font-medium text-tremor-content-strong">
                      {currentData[sensor.field] !== undefined 
                        ? `${getNumericValue(currentData[sensor.field])}${sensor.unit}` 
                        : '--'}
                    </span>
                  </ListItem>
                ))}
              </List>
              
              {selectedSensors.length === 0 && (
                <div className="mt-2 text-center text-sm text-gray-500">
                  {t('dashboard.selectSensorsToView') || 'Select sensors to view data'}
                </div>
              )}

              {/* Bottom Buttons */}
              <div className="mt-4 flex justify-between items-center">
                {/* Google Maps Button - only for GPS Data */}
                {config.title === "GPS Data" && 
                 currentData.gps_latitude && currentData.gps_longitude && 
                 currentData.gps_latitude !== null && currentData.gps_longitude !== null && 
                 currentData.gps_latitude !== 0 && currentData.gps_longitude !== 0 ? (
                  <button
                    type="button"
                    onClick={() => {
                      const lat = currentData.gps_latitude;
                      const lng = currentData.gps_longitude;
                      const googleMapsUrl = `https://www.google.com/maps?q=${lat},${lng}`;
                      window.open(googleMapsUrl, '_blank');
                    }}
                    className="inline-flex items-center gap-1.5 whitespace-nowrap py-2 px-3 text-tremor-default font-medium text-gray-500 hover:text-gray-800 transition-colors rounded-md hover:bg-gray-50"
                  >
                    <svg className="size-4 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                    </svg>
                    {t('dashboard.openInMaps') || 'Open in Maps'}
                  </button>
                ) : (
                  <div></div>
                )}

                {/* Expand Button */}
                <button
                  type="button"
                  onClick={() => toggleExpandedView(configIndex)}
                  className="inline-flex items-center gap-1.5 whitespace-nowrap py-2 px-3 text-tremor-default font-medium text-gray-500 hover:text-gray-800 transition-colors rounded-md hover:bg-gray-50"
                >
                  <RiExpandDiagonalLine className="size-4 shrink-0" aria-hidden={true} />
                  {t('dashboard.expandView') || 'Expand View'}
                </button>
              </div>
            </Card>
          );
        })}
      </div>

      {/* Use ExpandedSensorChart component */}
      {expandedConfigIndex !== null && (
        <ExpandedSensorChart
          title={`${t('dashboard.expandedAnalysis') || 'Expanded Analysis'} - ${t(getSensorGroupI18nKey(sensorConfigs[expandedConfigIndex].title))}`}
          description={`${t('dashboard.detailedView') || 'Detailed view and analysis of'} ${sensorConfigs[expandedConfigIndex].title.toLowerCase()} ${t('dashboard.sensorData') || 'sensor data'}`}
          historyData={historyData}
          currentData={currentData}
          isExpanded={true}
          onToggleExpand={() => toggleExpandedView(expandedConfigIndex)}
          sensorGroup={sensorConfigs[expandedConfigIndex]}
          initialSelectedSensors={selectedSensorsByConfig[expandedConfigIndex] || []}
        />
      )}
    </>
  );
} 