// API configuration for different environments
const API_CONFIG = {
  development: 'http://localhost:8086',
  production: 'http://**********:8086'
};

// Get the current environment
const environment = import.meta.env.MODE || 'development';

// Export the base API URL
export const API_BASE_URL = API_CONFIG[environment];

// Helper function to construct full API URLs
export const getApiUrl = (endpoint) => {
  return `${API_BASE_URL}/api${endpoint}`;
}; 