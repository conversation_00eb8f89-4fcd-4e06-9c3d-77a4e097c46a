// Sensor configuration for charts and data visualization
export const sensorConfigs = [
  {
    title: "MH-Z19B CO2 Sensor",
    sensors: [
      { name: 'CO2', field: 'mhz19b_co2', unit: ' ppm', color: 'bg-red-600' },
      { name: 'MinCO2', field: 'mhz19b_min_co2', unit: ' ppm', color: 'bg-blue-600' }
    ],
    colors: ['red', 'blue']
  },
  {
    title: "BME280 Environmental",
    sensors: [
      { name: 'Temperature', field: 'bme280_temperature', unit: '°C', color: 'bg-red-600' },
      { name: 'Humidity', field: 'bme280_humidity', unit: '%', color: 'bg-cyan-600' },
      { name: 'Pressure', field: 'bme280_pressure', unit: ' Pa', color: 'bg-purple-600' }
    ],
    colors: ['red', 'cyan', 'purple']
  },
  {
    title: "PMS7003 Concentration",
    sensors: [
      { name: 'PM01', field: 'pms7003_pm01', unit: ' μg/m³', color: 'bg-emerald-600' },
      { name: 'PM25', field: 'pms7003_pm25', unit: ' μg/m³', color: 'bg-orange-600' },
      { name: 'PM10', field: 'pms7003_pm10', unit: ' μg/m³', color: 'bg-violet-600' }
    ],
    colors: ['emerald', 'orange', 'violet']
  },
  {
    title: "PMS7003 Particle Count",
    sensors: [
      { name: 'N0p3', field: 'pms7003_n0p3', unit: ' #/0.1L', color: 'bg-red-600' },
      { name: 'N0p5', field: 'pms7003_n0p5', unit: ' #/0.1L', color: 'bg-blue-600' },
      { name: 'N1p0', field: 'pms7003_n1p0', unit: ' #/0.1L', color: 'bg-green-600' },
      { name: 'N2p5', field: 'pms7003_n2p5', unit: ' #/0.1L', color: 'bg-yellow-600' },
      { name: 'N5p0', field: 'pms7003_n5p0', unit: ' #/0.1L', color: 'bg-purple-600' },
      { name: 'N10p0', field: 'pms7003_n10p0', unit: ' #/0.1L', color: 'bg-pink-600' }
    ],
    colors: ['red', 'blue', 'green', 'yellow', 'purple', 'pink']
  },
  {
    title: "Wind Sensor",
    sensors: [
      { name: 'WindSpeed', field: 'wind_speed', unit: ' m/s', color: 'bg-teal-600' }
      // Note: Wind direction removed as it's not supported by ESP32
    ],
    colors: ['teal']
  },
  {
    title: "UV Sensor (LTR390)",
    sensors: [
      { name: 'UVIndex', field: 'ltr390_uvi', unit: '', color: 'bg-yellow-600' },
      { name: 'Lux', field: 'ltr390_lux', unit: ' lux', color: 'bg-purple-600' }
    ],
    colors: ['yellow', 'purple']
  },
  {
    title: "GPS Data",
    sensors: [
      { name: 'GPSLatitude', field: 'gps_latitude', unit: '°', color: 'bg-red-600' },
      { name: 'GPSLongitude', field: 'gps_longitude', unit: '°', color: 'bg-blue-600' },
      { name: 'GPSAltitude', field: 'gps_altitude', unit: ' m', color: 'bg-green-600' },
      { name: 'GPSQuality', field: 'gps_quality', unit: '', color: 'bg-cyan-600' },
      { name: 'GPSSatellites', field: 'gps_satellites', unit: '', color: 'bg-purple-600' },
      { name: 'GPSAccuracy', field: 'gps_accuracy', unit: ' m', color: 'bg-orange-600' }
      // Note: GPS speed removed as it's not supported by ESP32
    ],
    colors: ['red', 'blue', 'green', 'cyan', 'purple', 'orange']
  }
];

// Composition chart configuration - selected key sensors for comparison
export const compositionSensors = [
  {
    name: 'CO2',
    field: 'mhz19b_co2',
    unit: 'ppm',
    bgColor: 'bg-red-500',
    color: 'red'
  },
  {
    name: 'Temperature',
    field: 'bme280_temperature',
    unit: '°C',
    bgColor: 'bg-blue-500',
    color: 'blue'
  },
  {
    name: 'Humidity',
    field: 'bme280_humidity',
    unit: '%',
    bgColor: 'bg-green-500',
    color: 'green'
  },
  {
    name: 'PM2.5',
    field: 'pms7003_pm25',
    unit: 'μg/m³',
    bgColor: 'bg-orange-500',
    color: 'orange'
  },
  {
    name: 'Wind Speed',
    field: 'wind_speed',
    unit: 'm/s',
    bgColor: 'bg-purple-500',
    color: 'purple'
  }
];

// Color palette for dynamic sensor color assignment
export const colorPalette = [
  { chart: 'red', bgClass: 'bg-red-500' },
  { chart: 'blue', bgClass: 'bg-blue-500' },
  { chart: 'green', bgClass: 'bg-green-500' },
  { chart: 'orange', bgClass: 'bg-orange-500' },
  { chart: 'purple', bgClass: 'bg-purple-500' },
  { chart: 'cyan', bgClass: 'bg-cyan-500' },
  { chart: 'yellow', bgClass: 'bg-yellow-500' },
  { chart: 'emerald', bgClass: 'bg-emerald-500' },
  { chart: 'violet', bgClass: 'bg-violet-500' },
  { chart: 'pink', bgClass: 'bg-pink-500' },
  { chart: 'teal', bgClass: 'bg-teal-500' },
  { chart: 'indigo', bgClass: 'bg-indigo-500' }
];

// Mapping from sensor group titles to i18n keys
export const sensorGroupToI18nKey = {
    'MH-Z19B CO2 Sensor': 'sensors.mhzCo2',
    'BME280 Environmental': 'sensors.bme280',
    'PMS7003 Concentration': 'sensors.pms7003',
    'PMS7003 Particle Count': 'sensors.particleCount',
    'Wind Sensor': 'sensors.windSpeedSensor',
    'UV Sensor': 'sensors.uvSensor',
    'GPS Data': 'sensors.gpsSensor'
  };


// Mapping from sensor field names to i18n keys (updated for prefixed format)
export const sensorFieldToI18nKey = {
  // MH-Z19B data (prefixed)
  'mhz19b_co2': 'sensors.co2',
  'mhz19b_min_co2': 'sensors.minCo2',
  // BME280 data (prefixed)
  'bme280_temperature': 'sensors.temperature',
  'bme280_humidity': 'sensors.humidity',
  'bme280_pressure': 'sensors.pressure',
  // PMS7003 concentration data (prefixed)
  'pms7003_pm01': 'sensors.pm1',
  'pms7003_pm25': 'sensors.pm25',
  'pms7003_pm10': 'sensors.pm10',
  // PMS7003 particle count data (prefixed)
  'pms7003_n0p3': 'sensors.n0p3',
  'pms7003_n0p5': 'sensors.n0p5',
  'pms7003_n1p0': 'sensors.n1p0',
  'pms7003_n2p5': 'sensors.n2p5',
  'pms7003_n5p0': 'sensors.n5p0',
  'pms7003_n10p0': 'sensors.n10p0',
  // Wind data (prefixed)
  'wind_speed': 'sensors.windSpeed',
  // UV data (prefixed)
  'ltr390_uvi': 'sensors.uvIndex',
  'ltr390_lux': 'sensors.uvIntensity',
  // GPS data (prefixed)
  'gps_latitude': 'sensors.gpsLatitude',
  'gps_longitude': 'sensors.gpsLongitude',
  'gps_altitude': 'sensors.gpsAltitude',
  'gps_quality': 'sensors.gpsQuality',
  'gps_satellites': 'sensors.gpsSatellites',
  'gps_accuracy': 'sensors.gpsAccuracy',
};

// Helper function to get all available sensor fields
export const getAllSensorFields = () => {
  const allFields = [];
  sensorConfigs.forEach(config => {
    config.sensors.forEach(sensor => {
      if (!allFields.includes(sensor.field)) {
        allFields.push(sensor.field);
      }
    });
  });
  return allFields;
};

// Helper function to find sensor config by field name
export const getSensorConfigByField = (fieldName) => {
  for (const config of sensorConfigs) {
    const sensor = config.sensors.find(s => s.field === fieldName);
    if (sensor) {
      return { ...sensor, groupTitle: config.title };
    }
  }
  return null;
};

// Helper function to get all available sensors with standardized color assignment
export const getAllAvailableSensors = () => {
  const allSensors = [];
  let colorIndex = 0;

  sensorConfigs.forEach(config => {
    config.sensors.forEach(sensor => {
      const colorConfig = colorPalette[colorIndex % colorPalette.length];
      allSensors.push({
        ...sensor,
        groupTitle: config.title,
        color: colorConfig.chart,
        bgColor: colorConfig.bgClass
      });
      colorIndex++;
    });
  });

  return allSensors;
};

// Helper function to get default selected sensors for composition chart
export const getDefaultCompositionSensors = () => {
  return [...compositionSensors];
};

// Helper function to get i18n key for sensor field
export const getSensorI18nKey = (fieldName) => {
  return sensorFieldToI18nKey[fieldName] || fieldName;
};

// Helper function to get i18n key for sensor group title
export const getSensorGroupI18nKey = (groupTitle) => {
  return sensorGroupToI18nKey[groupTitle] || groupTitle;
};

// Helper function to get all sensor field mappings (replacing hardcoded version from sensorDataUtils)
export const getAllSensorFieldMappings = () => {
  const mappings = [];
  sensorConfigs.forEach(config => {
    config.sensors.forEach(sensor => {
      mappings.push({
        chartField: sensor.name,
        dataField: sensor.field
      });
    });
  });
  return mappings;
};

// Helper function to get sensor field mappings for selected sensors
export const getSensorFieldMappings = (selectedSensors) => {
  return selectedSensors.map(sensor => ({
    chartField: sensor.name,
    dataField: sensor.field
  }));
};

// Helper function to get composition field mappings
export const getCompositionFieldMappings = (compositionSensors) => {
  return compositionSensors.map(sensor => ({
    chartField: sensor.name,
    dataField: sensor.field
  }));
};

// Time range configuration for data filtering
export const timeRangeOptions = [
  { value: '1min', label: '1 Min', i18nKey: 'timeRange.oneMin', milliseconds: 1 * 60 * 1000 },
  { value: '3mins', label: '3 Mins', i18nKey: 'timeRange.threeMins', milliseconds: 3 * 60 * 1000 },
  { value: '5mins', label: '5 Mins', i18nKey: 'timeRange.fiveMins', milliseconds: 5 * 60 * 1000 },
  { value: '15mins', label: '15 Mins', i18nKey: 'timeRange.fifteenMins', milliseconds: 15 * 60 * 1000 },
  { value: '1hour', label: '1 Hour', i18nKey: 'timeRange.oneHour', milliseconds: 1 * 60 * 60 * 1000 },
  { value: '4hours', label: '4 Hours', i18nKey: 'timeRange.fourHours', milliseconds: 4 * 60 * 60 * 1000 },
  { value: '1day', label: '1 Day', i18nKey: 'timeRange.oneDay', milliseconds: 24 * 60 * 60 * 1000 }
];

// Default time range
export const defaultTimeRange = '15mins';

// Helper function to get time range by value
export const getTimeRangeByValue = (value) => {
  return timeRangeOptions.find(option => option.value === value);
};

// Helper function to get i18n key for time range
export const getTimeRangeI18nKey = (value) => {
  const timeRange = getTimeRangeByValue(value);
  return timeRange ? timeRange.i18nKey : null;
}; 