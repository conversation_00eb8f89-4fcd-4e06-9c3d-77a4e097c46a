import React, { createContext, useContext, useState, useEffect } from 'react';
import { translations, languageData } from '../i18n';

// Create context
const LanguageContext = createContext();

// Language provider component
export const LanguageProvider = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState(() => {
    return localStorage.getItem('preferredLanguage') || 'zh-HK';
  });

  // Switch language function
  const switchLanguage = (lang) => {
    setCurrentLanguage(lang);
    localStorage.setItem('preferredLanguage', lang);
    document.documentElement.lang = lang;
  };

  // Get translation by key (t function for proper i18n pattern)
  const t = (key) => {
    const keys = key.split('.');
    let translation = translations[currentLanguage];
    for (const k of keys) {
      translation = translation?.[k];
    }
    return translation || key;
  };

  // Legacy function for backward compatibility
  const getTranslation = t;

  useEffect(() => {
    document.documentElement.lang = currentLanguage;
  }, [currentLanguage]);

  const value = {
    currentLanguage,
    switchLanguage,
    t,
    getTranslation, // Keep for backward compatibility
    languageData,
    translations
  };

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
};

// Custom hook to use language context
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}; 