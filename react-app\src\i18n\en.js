export default {
  nav: {
    dashboard: 'Dashboard',
    programming: 'Visual Programming',
    report: 'Data Report',
  },
  dashboard: {
    title: 'Sensor Data Dashboard',
    subtitle: 'Real-time sensor data monitoring and visualization',
    selectDevice: 'Select Device',
    selectDevicePlaceholder: 'Please select a device...',
    searchDevice: 'Search devices...',
    clearSelection: 'Clear Selection',
    noDevicesFound: 'No devices found',
    noDeviceSelected: 'No device selected',
    selectDeviceToStart: 'Please select a device from the dropdown menu above to start monitoring sensor data.',
    loadingDeviceData: 'Loading device data and establishing connection...',
    selectedDevice: 'Selected Device',
    deleteData: 'Delete Data',
    deleteDeviceData: 'Delete all data for this device',
    confirmDelete: 'Confirm Delete',
    confirmDeleteMessage: 'Are you sure you want to delete all data for device "{device}"? This action cannot be undone.',
    cancel: 'Cancel',
    deleteConfirm: 'Delete',
    dataTab: 'Sensor Data',
    chartsTab: 'Charts & Analytics',
    compositionTab: 'Composition',
    chartsTitle: 'Sensor Data Analytics',
    chartsDescription: 'Interactive charts and real-time sensor data visualization.',
    selectSensors: 'Select Sensors',
    selectSensorsLabel: 'Select Sensors (max 6)',
    searchSensors: 'Search sensors...',
    noSensorsFound: 'No sensors found',
    noSensorsSelected: 'No sensors selected',
    selectSensorsToView: 'Please select sensors to view data',
    noDataAvailable: 'No data available',
    selectDeviceToView: 'Please select a device to view sensor data',
    useDropdownToSelect: 'Use the dropdown to select sensors',
    compositionTitle: 'Sensor Data Composition',
    compositionDescription: 'Multi-sensor data comparison and analysis',
    expandView: 'Expand View',
    expandedAnalysis: 'Expanded Analysis',
    detailedView: 'Detailed view and analysis',
    sensorData: 'Sensor Data',
    manageSensors: 'Manage Sensors',
    statisticalSummary: 'Statistical Summary',
    sensor: 'Sensor',
    current: 'Current',
    min: 'Min',
    max: 'Max',
    avg: 'Average',
    selectGroup: 'Select All',
    deselectGroup: 'Deselect All',
    openInMaps: 'Open in Maps'
  },
  status: {
    connection: 'Connection Status',
    connecting: 'Connecting...',
    online: 'Online',
    offline: 'Offline',
    records: 'Data Records',
    lastUpdate: 'Last Update',
    mockData: 'Mock Data',
    streaming: 'Streaming',
    stopped: 'Stopped'
  },
  sensors: {
    temperature: 'Temperature',
    humidity: 'Humidity',
    pressure: 'Pressure',
    pm1: 'PM1.0',
    pm25: 'PM2.5',
    pm10: 'PM10',
    n0p3: 'N0.3μm',
    n0p5: 'N0.5μm',
    n1p0: 'N1.0μm',
    n2p5: 'N2.5μm',
    n5p0: 'N5.0μm',
    n10p0: 'N10.0μm',
    co2: 'CO2 Concentration',
    mhzCo2: 'MH-Z19B CO2 Sensor',
    pms7003: 'PMS7003 Particle Matter Sensor',
    bme280: 'BME280 Environmental Sensor',
    minCo2: 'Minimum CO2',
    pmConcentration: 'Particle Matter Concentration',
    particleCount: 'Particle Count',
    windSpeedSensor: 'Wind Speed Sensor',
    windSpeed: 'Wind Speed',
    windDirection: 'Wind Direction',
    uvSensor: 'UV Sensor',
    uvIndex: 'UV Index',
    uvIntensity: 'UV Intensity',
    gpsSensor: 'GPS Sensor',
    gpsLatitude: 'GPS Latitude',
    gpsLongitude: 'GPS Longitude',
    gpsAltitude: 'GPS Altitude',
    gpsSpeed: 'GPS Speed',
    gpsSatellites: 'GPS Satellites',
    gpsQuality: 'GPS Signal Quality',
    gpsAccuracy: 'GPS Accuracy',
    lux: 'Illuminance'
  },
  buttons: {
    refresh: 'Refresh Data',
    clear: 'Clear Charts',
    export: 'Export Data'
  },
  charts: {
    temperature: 'Temperature Trend',
    humidity: 'Humidity Trend'
  },
  programming: {
    title: 'Programming Interface',
    // Project controls
    projectName: 'Project Name:',
    saveAsIno: 'Export as .ino',
    saveAsJson: 'Export as JSON',
    importJson: 'Import JSON',
    
    // Tooltips
    downloadInoTooltip: 'Download Arduino .ino file',
    downloadJsonTooltip: 'Download Blockly project as JSON',
    importJsonTooltip: 'Import Blockly project from JSON',
    exportDisabledWarning: 'Cannot export with warnings in workspace',
    
    // Error messages
    fileTypeNotSupported: 'Unsupported file type. Please select a .json file.',
    failedToImportProject: 'Failed to import project:',
    
    // Category names
    categorySetup: 'Setup',
    categorySensors: 'Sensors',
    categoryValuesVariables: 'Values & Variables',
    categoryCommunication: 'Communication',
    categoryControl: 'Control',
    categoryOperatorsConversions: 'Operators & Conversions',
    logicalOperators: 'Logical Operators',
    mathOperators: 'Math Operators',
    typeConversions: 'Type Conversions',
    convertToString: 'Convert to String',
    convertToStringTooltip: 'Convert integer or float to string',
    convertToInt: 'Convert to Integer',
    convertToIntTooltip: 'Convert string or float value to integer',
    convertToFloat: 'Convert to Float',
    convertToFloatTooltip: 'Convert string or integer value to float',
    

    
    dragHint: 'Drag blocks here to start programming',
    dragSubHint: 'Select blocks from the toolbox and drag them to the workspace to build your program',
    generatedCode: 'Generated Code',
    jsCodeOutput: 'Arduino C Code Output',
    loadingInterface: 'Loading programming interface...',
    loadingFailed: 'Loading failed',
    
    // Block labels and tooltips
    // WiFi Setup
    setWifiLabel: 'Set WiFi SSID',
    wifiPassword: 'Password',
    setWifiTooltip: 'Set WiFi credentials and call setWiFi()',
    
    // Sensor blocks
    bme280Sensor: 'BME280 (Temperature & Humidity Sensor)',
    pms7003Sensor: 'PMS7003 (Air Quality Sensor)',
    mhz19bSensor: 'MHZ19B (CO2 Sensor)',
    windSensor: 'Wind Speed Sensor',
    ltr390Sensor: 'LTR390 (UV Sensor)',
    gpsSensor: 'GPS Sensor',
    
    // Sensor blocks
    bmeTemperature: 'BME280 Temperature',
    bmeHumidity: 'BME280 Humidity',
    bmePressure: 'BME280 Pressure',
    pmsPm1: 'PMS7003 PM1.0',
    pmsPm25: 'PMS7003 PM2.5',
    pmsPm10: 'PMS7003 PM10',
    pmsN03: 'PMS7003 N0.3',
    pmsN05: 'PMS7003 N0.5',
    pmsN10: 'PMS7003 N1.0',
    pmsN25: 'PMS7003 N2.5',
    pmsN50: 'PMS7003 N5.0',
    pmsN100: 'PMS7003 N10.0',
    mhzCo2: 'MHZ19B CO2',
    mhzMinCo2: 'MHZ19B Minimum CO2',

    
    // Sensor tooltips
    readBmeTemperatureTooltip: 'Read temperature from BME280 sensor',
    readBmeHumidityTooltip: 'Read humidity from BME280 sensor',
    readBmePressureTooltip: 'Read pressure from BME280 sensor',
    readPm1Tooltip: 'Read PM1.0 from PMS7003',
    readPm25Tooltip: 'Read PM2.5 from PMS7003',
    readPm10Tooltip: 'Read PM10 from PMS7003',
    readN03Tooltip: 'Read N0.3 from PMS7003',
    readN05Tooltip: 'Read N0.5 from PMS7003',
    readN10Tooltip: 'Read N1.0 from PMS7003',
    readN25Tooltip: 'Read N2.5 from PMS7003',
    readN50Tooltip: 'Read N5.0 from PMS7003',
    readN100Tooltip: 'Read N10.0 from PMS7003',
    readCo2Tooltip: 'Read CO2 from MHZ19B',
    readMinCo2Tooltip: 'Read minimum CO2 from MHZ19B',

    
    // Sensor Status blocks
    bme280Status: 'BME280 (Temperature & Humidity Sensor) Status',
    pms7003Status: 'PMS7003 (PM2.5 Sensor) Status',
    mhz19bStatus: 'MHZ19B (CO2 Sensor) Status',
    windSpeedStatus: 'Wind Speed Sensor Status',
    uvStatus: 'UV Sensor Status',
    gpsStatus: 'GPS Sensor Status',
    getBme280StatusTooltip: 'Check if BME280 sensor is connected and working properly',
    getPms7003StatusTooltip: 'Check if PMS7003 sensor is connected and working properly',
    getMhz19bStatusTooltip: 'Check if MHZ19B sensor is connected and working properly',
    getWindSpeedStatusTooltip: 'Check if wind speed sensor is connected and working properly',
    getUvStatusTooltip: 'Check if UV sensor is connected and working properly',
    getGpsStatusTooltip: 'Check if GPS sensor is connected and working properly',
    
    // Wind Speed Sensor Variables
    windSpeed: 'Wind Speed',
    windDirection: 'Wind Direction',
    readWindSpeedTooltip: 'Read wind speed value',
    readWindDirectionTooltip: 'Read wind direction value',
    
    // UV Sensor Variables
    uvIndex: 'UV Index',
    uvIntensity: 'UV Intensity',
    readUvIndexTooltip: 'Read UV index value',
    readUvIntensityTooltip: 'Read UV intensity value',
    
    // GPS Sensor Variables
    gpsLatitude: 'GPS Latitude',
    gpsLongitude: 'GPS Longitude',
    gpsAltitude: 'GPS Altitude',
    gpsSpeed: 'GPS Speed',
    gpsSatellites: 'GPS Satellites',
    readGpsLatitudeTooltip: 'Read GPS latitude coordinate',
    readGpsLongitudeTooltip: 'Read GPS longitude coordinate',
    readGpsAltitudeTooltip: 'Read GPS altitude value',
    readGpsSpeedTooltip: 'Read GPS speed value',
    readGpsSatellitesTooltip: 'Read GPS satellite count',

    
    // Main category labels
    setupMainLabel: 'System Setup & Configuration',
    sensorsMainLabel: 'Sensor Status',
    valuesVariablesMainLabel: 'Values Variables & Data Operations',
    operatorsConversionsMainLabel: 'Operators & Type Conversions',
    communicationMainLabel: 'Data Communication',
    controlMainLabel: 'Program Control',
    
    // Setup section labels
    wifiConfiguration: 'WiFi Configuration',
    sensorConfiguration: 'Sensor Configuration',

    // Sensor section labels
    getSensorStatus: 'Get Sensor Status',
    getSensorVariables: 'Get Sensor Variables',
      
    // Values & Variables section labels
    // Typed Sections
    integerSection: 'Integer',
    floatSection: 'Float',
    textSection: 'Text',
    createIntVariable: 'Create int variable...',
    createFloatVariable: 'Create float variable...',
    createTextVariable: 'Create text variable...',
    
    // Variable operations
    getIntVariableTooltip: 'Get value of integer variable',
    getFloatVariableTooltip: 'Get value of float variable',
    getTextVariableTooltip: 'Get value of text variable',
    setIntVariable: 'set int',
    setFloatVariable: 'set float',
    setTextVariable: 'set text',
    setIntVariableTooltip: 'Set integer variable to a value',
    setFloatVariableTooltip: 'Set float variable to a value',
    setTextVariableTooltip: 'Set text variable to a value',
    toValue: 'to',
    textLabel: 'text',
    

    
    // Communication section labels
    serialCommunication: 'Serial Communication',
    networkCommunication: 'Network Communication',
    
    // Control section labels
    conditionalControl: 'Conditional Control',
    timeControl: 'Time Control',
    
    // Arrays section
    arraySection: 'Array',
    arraySum: 'Sum',
    arrayAverage: 'Average',
    arrayMin: 'Minimum',
    arrayMax: 'Maximum',
    arrayGet: 'Get',
    arrayOf: 'of array',
    arrayAggregateTooltip: 'Calculate aggregate value of array',
    

    bme280SensorVariables: 'BME280 Sensor Variables',
    pms7003SensorVariables: 'PMS7003 Sensor Variables',
    mhz19bSensorVariables: 'MHZ19B Sensor Variables',
    windSpeedSensorVariables: 'Wind Speed Sensor Variables',
    uvSensorVariables: 'UV Sensor Variables',
    gpsSensorVariables: 'GPS Sensor Variables',
    

    
    // Communication blocks
    serialPrint: 'Serial Print',
    serialPrintTooltip: 'Send data to Arduino serial monitor',
    sendToServer: 'Send',
    sendToServerTooltip: 'Send selected sensor data to server via ESP32',
    sensorData: 'data to server',
    allSensors: 'All Sensors',
    allData: 'All Data',
    
    // Grouped sensor data communication
    sendGroupedData: 'Send',
    dataToServer: 'data to server',
    sendGroupedDataTooltip: 'Send grouped sensor data to server using new structured format',
    
    // Log blocks
    sendLogToServer: 'Send',
    logToServer: 'log to server',
    logMessage: 'Message',
    logAlert: 'Alert',
    logWarning: 'Warning',
    sendLogToServerTooltip: 'Send log message with specified level to server',
    
    // Control blocks
    delayLabel: 'Delay',
    delayMilliseconds: 'milliseconds',
    delayTooltip: 'Wait for specified number of milliseconds',
    
    // Value blocks
    integerValue: 'Integer Value',
    floatValue: 'Float Value',
    stringValue: 'String Value',
    intLabel: 'int',
    floatLabel: 'float',
    textDefault: 'text',
    trueLabel: 'true',
    falseLabel: 'false',
    
    // Text and List creation blocks
    createTextWith: 'Create text with',
    createListWith: 'Create list with',
    createEmptyList: 'Create empty list',
    createList: 'Create list',
    createListTooltip: 'Add, remove, or reorder sections to reconfigure this list block.',
    joinText: 'Join text',
    joinTextTooltip: 'Join multiple text values together',
    item: 'item',
    itemTooltip: 'Add an item to the list.',
    
    // Array validation messages
    arrayStringMixError: 'Arrays cannot mix string values with numbers',
    arrayIntFloatMixError: 'Arrays cannot mix integer and float values. Please use convert_to_float or convert_to_int blocks for type conversion',
    
    // Arduino functions
    setupFunction: 'setup function()',
    setupTooltip: 'Arduino setup() function',
    loopFunction: 'loop function()',
    loopTooltip: 'Arduino loop() function',
    
    // Logic comparison
    compareTooltip: 'Compare two values. For strings, only == and != are allowed, and both sides must be strings.',
    stringComparisonError: 'String values can only be compared using = or ≠ operators',
    stringComparisonWarning: 'Both sides must be strings for string comparison.',
    typeError: 'Only int, float or bool types are allowed.',
    typeMismatchError: 'Type mismatch error - all values should have compatible types',
    arrayNestedError: 'Arrays cannot contain other arrays (sublists)',
    
    // Error messages
    blocklyContainerNotFound: 'Blockly container not found',
    noWorkspaceError: 'No workspace available',
    
    // Project related
    enterProjectName: 'Enter project name',

    // Relative time
    justNow: 'just now',
    secondsAgo: (count) => `${count} seconds ago`,
    oneMinuteAgo: '1 minute ago',
    minutesAgo: (count) => `${count} minutes ago`,
    oneHourAgo: '1 hour ago',
    hoursAgo: (count) => `${count} hours ago`,
    oneDayAgo: '1 day ago',
    daysAgo: (count) => `${count} days ago`,
    
    // Manual save and clear workspace
    manualSave: 'Save',
    clearWorkspace: 'Clear',
    manualSaveTooltip: 'Save workspace to local storage',
    clearWorkspaceTooltip: 'Clear workspace',
    saving: 'Saving...',
    savedAt: 'Saved at',
    manuallySavedAt: 'Manually saved at',
    autoSavedAt: 'Auto saved at',
    
    // Text Append Block
    textAppendTo: 'Append to variable',
    textAppendText: 'text',
    textAppendTooltip: 'Append text to the end of specified variable',
    
    // Code Panel Collapse
    collapseCodePanel: 'Collapse Code Panel',
    expandCodePanel: 'Expand Code Panel',
    generatedCodeCollapsed: 'Generated Code',
    
    // Code Panel Resize
    dragToResize: 'Drag to resize',
    
    // Length blocks (custom overrides of built-in blocks)
    textLengthLabel: 'length of',
    textLengthTooltip: 'Returns the number of letters (including spaces) in the provided text',
    arrayLengthLabel: 'length of',
    arrayLengthTooltip: 'Returns the length of a list',
    
    // Array index blocks (custom overrides of built-in blocks)
    arrayIndexGet: 'get',
    arrayIndexSet: 'set',
    arrayIndexInsertAt: 'insert at',
    arrayIndexFromStart: 'from first #',
    arrayIndexFromEnd: 'from end #',
    arrayIndexInList: 'in list',
    arrayIndexToValue: 'to',
    arrayGetIndexTooltip: 'Returns the item at the specified position in a list',
    arraySetIndexTooltip: 'Sets the item at the specified position in a list',

    // Array sublist blocks
    arraySublistGetSublist: 'get sublist from list',
    arraySublistTo: 'to',
    arraySublistTooltip: 'Creates a copy of a portion of a list',

    // Math blocks (custom overrides of built-in blocks)
    mathArithmeticTooltip: 'Perform arithmetic operations on numbers',
    mathSingleTooltip: 'Perform single-value math operations',
    mathRoundTooltip: 'Round numbers to integers',
    mathSquareRoot: 'square root',
    mathAbsolute: 'absolute',
    mathRound: 'round',
    mathRoundUp: 'round up',
    mathRoundDown: 'round down',
    stringOperatorWarning: 'String values can only be compared using = or ≠ operators'
  },
  log: {
    title: 'System Log',
    noLogs: 'No log entries available',
    devicesLoaded: 'Available devices loaded from server',
    devicesLoadError: 'Failed to load available devices',
    deviceSelected: 'Device selected for monitoring',
    deviceDeselected: 'Device selection cleared',
    serverError: 'Server communication error',
    noSensorData: 'No sensor data available for selected device',
    historyError: 'Failed to load historical data',
    deletingDevice: 'Deleting device data...',
    deviceDeleted: 'Device data deleted successfully',
    deleteError: 'Failed to delete device data',
    deviceLogsError: 'Failed to load device logs'
  },
  // Blockly Blocks Translation
  blockly: {
    // Math blocks
    MATH_NUMBER_HELPURL: 'https://en.wikipedia.org/wiki/Number',
    MATH_NUMBER_TOOLTIP: 'A number.',
    MATH_ARITHMETIC_TOOLTIP_ADD: 'Return the sum of the two numbers.',
    MATH_ARITHMETIC_TOOLTIP_MINUS: 'Return the difference of the two numbers.',
    MATH_ARITHMETIC_TOOLTIP_MULTIPLY: 'Return the product of the two numbers.',
    MATH_ARITHMETIC_TOOLTIP_DIVIDE: 'Return the quotient of the two numbers.',
    MATH_ARITHMETIC_TOOLTIP_POWER: 'Return the first number raised to the power of the second number.',
    MATH_SINGLE_TITLE: '%1 %2',
    MATH_SINGLE_OP_ROOT: 'square root',
    MATH_SINGLE_OP_ABSOLUTE: 'absolute',
    MATH_SINGLE_OP_NEG: '-',
    MATH_SINGLE_OP_LN: 'ln',
    MATH_SINGLE_OP_LOG10: 'log10',
    MATH_SINGLE_OP_EXP: 'e^',
    MATH_SINGLE_OP_POW10: '10^',
    MATH_SINGLE_TOOLTIP_ROOT: 'Return the square root of a number.',
    MATH_SINGLE_TOOLTIP_ABS: 'Return the absolute value of a number.',
    MATH_SINGLE_TOOLTIP_NEG: 'Return the negation of a number.',
    MATH_SINGLE_TOOLTIP_LN: 'Return the natural logarithm of a number.',
    MATH_SINGLE_TOOLTIP_LOG10: 'Return the base 10 logarithm of a number.',
    MATH_SINGLE_TOOLTIP_EXP: 'Return e to the power of a number.',
    MATH_SINGLE_TOOLTIP_POW10: 'Return 10 to the power of a number.',
    MATH_ROUND_TITLE: 'round %1',
    MATH_ROUND_TOOLTIP: 'Round a number up or down.',
    MATH_ROUND_OPERATOR_ROUND: 'round',
    MATH_ROUND_OPERATOR_ROUNDUP: 'round up',
    MATH_ROUND_OPERATOR_ROUNDDOWN: 'round down',
    
    // Logic blocks
    LOGIC_BOOLEAN_TRUE: 'true',
    LOGIC_BOOLEAN_FALSE: 'false',
    LOGIC_BOOLEAN_TOOLTIP: 'Returns either true or false.',
    LOGIC_COMPARE_TOOLTIP_EQ: 'Return true if both inputs are equal.',
    LOGIC_COMPARE_TOOLTIP_NEQ: 'Return true if both inputs are not equal.',
    LOGIC_COMPARE_TOOLTIP_LT: 'Return true if the first input is smaller than the second input.',
    LOGIC_COMPARE_TOOLTIP_LTE: 'Return true if the first input is smaller than or equal to the second input.',
    LOGIC_COMPARE_TOOLTIP_GT: 'Return true if the first input is greater than the second input.',
    LOGIC_COMPARE_TOOLTIP_GTE: 'Return true if the first input is greater than or equal to the second input.',
    LOGIC_OPERATION_TOOLTIP_AND: 'Return true if both inputs are true.',
    LOGIC_OPERATION_TOOLTIP_OR: 'Return true if at least one of the inputs is true.',
    LOGIC_OPERATION_AND: 'and',
    LOGIC_OPERATION_OR: 'or',
    LOGIC_NEGATE_TITLE: 'not %1',
    LOGIC_NEGATE_TOOLTIP: 'Returns true if the input is false. Returns false if the input is true.',
    LOGIC_NULL: 'null',
    LOGIC_NULL_TOOLTIP: 'Returns null.',

    // Control blocks
    CONTROLS_IF_MSG_IF: 'if',
    CONTROLS_IF_MSG_THEN: 'do',
    CONTROLS_IF_MSG_ELSE: 'else',
    CONTROLS_IF_MSG_ELSEIF: 'else if',
    CONTROLS_IF_TOOLTIP_1: 'If a value is true, then do some statements.',
    CONTROLS_IF_TOOLTIP_2: 'If a value is true, then do the first block of statements. Otherwise, do the second block of statements.',
    CONTROLS_IF_TOOLTIP_3: 'If the first value is true, then do the first block of statements. Otherwise, if the second value is true, do the second block of statements.',
    CONTROLS_IF_TOOLTIP_4: 'If the first value is true, then do the first block of statements. Otherwise, if the second value is true, do the second block of statements. If none of the values are true, do the last block of statements.',
    CONTROLS_IF_IF_TITLE_IF: 'if',
    CONTROLS_IF_ELSEIF_TITLE_ELSEIF: 'else if',
    CONTROLS_IF_ELSE_TITLE_ELSE: 'else',
    
    // Variables blocks
    VARIABLES_GET_TOOLTIP: 'Returns the value of this variable.',
    VARIABLES_GET_CREATE_SET: 'Create "set %1"',
    VARIABLES_SET: 'set %1 to %2',
    VARIABLES_SET_TOOLTIP: 'Sets this variable to be equal to the input.',
    VARIABLES_SET_CREATE_GET: 'Create "get %1"',
    NEW_VARIABLE: 'Create variable...',
    NEW_VARIABLE_TITLE: 'New variable name:',
    RENAME_VARIABLE: 'Rename variable...',
    RENAME_VARIABLE_TITLE: 'Rename all "%1" variables to:',
    DELETE_VARIABLE: 'Delete the "%1" variable',
    DELETE_VARIABLE_CONFIRMATION: 'Delete %1 uses of the "%2" variable?',
    VARIABLE_ALREADY_EXISTS: 'A variable named "%1" already exists.',
    VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE: 'A variable named "%1" already exists for another type: "%2".',
    
    // Text blocks
    TEXT_JOIN_TITLE_CREATEWITH: 'create text with',
    TEXT_JOIN_TOOLTIP: 'Create a piece of text by joining together any number of items.',
    TEXT_CREATE_JOIN_TITLE_JOIN: 'join',

    
    // Lists blocks
    LISTS_CREATE_WITH_TOOLTIP: 'Create a list with any number of items.',
    LISTS_CREATE_WITH_INPUT_WITH: 'create list with',
    LISTS_CREATE_WITH_CONTAINER_TITLE_ADD: 'list',
    LISTS_CREATE_WITH_CONTAINER_TOOLTIP: 'Add, remove, or reorder sections to reconfigure this list block.',
    LISTS_CREATE_WITH_ITEM_TOOLTIP: 'Add an item to the list.',


    LISTS_INDEX_FROM_END_TOOLTIP: '%1 is the last item, %2 is the second-last item, and so on.',
    LISTS_GET_SUBLIST_TOOLTIP: 'Creates a copy of the specified portion of a list.',
    LISTS_GET_SUBLIST_INPUT_IN_LIST: 'in list',
    LISTS_GET_SUBLIST_START_FROM_START: 'get sub-list from #',
    LISTS_GET_SUBLIST_START_FROM_END: 'get sub-list from # from end',
    LISTS_GET_SUBLIST_START_FIRST: 'get sub-list from first',
    LISTS_GET_SUBLIST_END_FROM_START: 'to #',
    LISTS_GET_SUBLIST_END_FROM_END: 'to # from end',
    LISTS_GET_SUBLIST_END_LAST: 'to last',
    LISTS_GET_SUBLIST_TAIL: '',
    
    // Error messages
    blocklyContainerNotFound: 'Blockly container not found'
  },
  timeRange: {
    oneMin: '1 Min',
    threeMins: '3 Mins',
    fiveMins: '5 Mins',
    fifteenMins: '15 Mins',
    oneHour: '1 Hour',
    fourHours: '4 Hours',
    oneDay: '1 Day'
  },
  report: {
    title: 'Sensor Data Report',
    subtitle: 'Historical sensor data analysis and export',
    loading: 'Loading report data...',
    deviceFilter: 'Device Filter',
    selectDevices: 'Select Devices',
    searchDevices: 'Search devices...',
    noDevicesFound: 'No devices found',
    selectAll: 'Select All',
    deselectAll: 'Deselect All',
    columnFilter: 'Select Columns',
    selectColumns: 'Select Columns',
    searchColumns: 'Search columns...',
    timeWindow: 'Time Window',
    noAggregation: '0ms (No Data Merging)',
    noColumnsFound: 'No columns found',
    selectGroup: 'Select All',
    deselectGroup: 'Deselect All',
    exportCsv: 'Export CSV',
    refresh: 'Refresh',
    loadingOriginalData: 'Loading original data...',
    loadingAggregatedData: 'Loading aggregated data...',
    table: {
      basicInfo: 'Basic Information',
      timeDevice: 'Device / Time',
      mhz19bSensor: 'MH-Z19B CO2 Sensor',
      pms7003Sensor: 'PMS7003 Particle Matter Sensor',
      pms7003ParticleCount: 'PMS7003 Particle Count',
      bme280Sensor: 'BME280 Environmental Sensor',
      co2Ppm: 'CO2 (ppm)',
      minCo2Ppm: 'Min CO2 (ppm)',
      pm1: 'PM1.0 (μg/m³)',
      pm25: 'PM2.5 (μg/m³)',
      pm10: 'PM10 (μg/m³)',
      n0p3: 'N0.3μm (#/0.1L)',
      n0p5: 'N0.5μm (#/0.1L)',
      n1p0: 'N1.0μm (#/0.1L)',
      n2p5: 'N2.5μm (#/0.1L)',
      n5p0: 'N5.0μm (#/0.1L)',
      n10p0: 'N10.0μm (#/0.1L)',
      temperature: 'Temperature (°C)',
      humidity: 'Humidity (%)',
      pressure: 'Pressure (Pa)',
      windSensor: 'Wind Speed Sensor',
      windSpeed: 'Wind Speed (m/s)',
      uvSensor: 'LTR390 UV Sensor',
      uvIndex: 'UV Index',
      uvLux: 'UV Illuminance (lux)',
      gpsSensor: 'GPS Location Sensor',
      gpsLatitude: 'GPS Latitude',
      gpsLongitude: 'GPS Longitude',
      gpsAltitude: 'GPS Altitude (m)',
      gpsQuality: 'GPS Signal Quality',
      gpsSatellites: 'GPS Satellites',
      gpsAccuracy: 'GPS Accuracy (m)'
    },
    messages: {
      noDeviceSelected: 'Please select at least one device to view data',
      noDataAvailable: 'No data available for selected devices',
      showingRecords: 'Showing',
      recordsFrom: 'records from',
      devices: 'device(s)'
    },
    loadMore: 'Load More',
    loadingMore: 'Loading...'
  }
};