import zhCN from './zh-CN.js';
import zhHK from './zh-HK.js';
import en from './en.js';

// Language translations
export const translations = {
  'zh-CN': zhCN,
  'zh-HK': zhHK,
  'en': en
};

// Language data for dropdown
export const languageData = {
  'zh-HK': { flag: '🇭🇰', text: '繁體中文' },
  'zh-CN': { flag: '🇨🇳', text: '简体中文' },
  'en': { flag: '🇺🇸', text: 'English' }
};

export default translations; 