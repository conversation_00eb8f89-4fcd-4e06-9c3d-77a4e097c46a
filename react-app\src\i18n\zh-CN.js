export default {
  nav: {
    dashboard: '仪表板',
    programming: '图形化编程界面',
    report: '数据库报告',
  },
  dashboard: {
    title: '传感器数据仪表板',
    subtitle: '实时传感器数据监控和可视化',
    selectDevice: '选择设备',
    selectDevicePlaceholder: '请选择设备...',
    searchDevice: '搜索设备...',
    clearSelection: '清除选择',
    noDevicesFound: '未找到设备',
    noDeviceSelected: '未选择设备',
    selectDeviceToStart: '请从上方下拉菜单中选择一个设备以开始监控传感器数据。',
    loadingDeviceData: '正在加载设备数据并建立连接...',
    selectedDevice: '已选设备',
    deleteData: '删除数据',
    deleteDeviceData: '删除此设备的所有数据',
    confirmDelete: '确认删除',
    confirmDeleteMessage: '您确定要删除设备"{device}"的所有数据吗？此操作无法撤销。',
    cancel: '取消',
    deleteConfirm: '删除',
    dataTab: '传感器数据',
    chartsTab: '数据图表',
    compositionTab: '组合分析',
    chartsTitle: '传感器数据分析',
    chartsDescription: '交互式图表和实时传感器数据可视化。',
    selectSensors: '选择传感器',
    selectSensorsLabel: '选择传感器 (最多6个)',
    searchSensors: '搜索传感器...',
    noSensorsFound: '未找到传感器',
    noSensorsSelected: '未选择传感器',
    selectSensorsToView: '请选择传感器查看数据',
    noDataAvailable: '无数据可用',
    selectDeviceToView: '请选择设备查看传感器数据',
    useDropdownToSelect: '使用下拉菜单选择传感器',
    compositionTitle: '传感器数据组合',
    compositionDescription: '多传感器数据对比和分析',
    expandView: '展开视图',
    expandedAnalysis: '展开分析',
    detailedView: '详细查看和分析',
    sensorData: '传感器数据',
    manageSensors: '管理传感器',
    statisticalSummary: '统计摘要',
    sensor: '传感器',
    current: '当前值',
    min: '最小值',
    max: '最大值',
    avg: '平均值',
    selectGroup: '全选',
    deselectGroup: '取消全选',
    openInMaps: '在地图中打开'
  },
  status: {
    connection: '连接状态',
    connecting: '连接中...',
    online: '在线',
    offline: '离线',
    records: '数据记录',
    lastUpdate: '最后更新',
    mockData: '模拟数据',
    streaming: '流式传输',
    stopped: '已停止'
  },
  sensors: {
    temperature: '温度',
    humidity: '湿度',
    pressure: '气压',
    pm1: 'PM1.0',
    pm25: 'PM2.5',
    pm10: 'PM10',
    n0p3: 'N0.3μm',
    n0p5: 'N0.5μm',
    n1p0: 'N1.0μm',
    n2p5: 'N2.5μm',
    n5p0: 'N5.0μm',
    n10p0: 'N10.0μm',
    co2: 'CO2浓度',
    mhzCo2: 'MH-Z19B CO2传感器',
    pms7003: 'PMS7003颗粒物传感器',
    bme280: 'BME280环境传感器',
    minCo2: '最小CO2',
    pmConcentration: '颗粒物浓度',
    particleCount: '颗粒数量',
    windSpeedSensor: '风速传感器',
    windSpeed: '风速',
    windDirection: '风向',
    uvSensor: 'UV传感器',
    uvIndex: 'UV指数',
    uvIntensity: 'UV强度',
    gpsSensor: 'GPS传感器',
    gpsLatitude: 'GPS纬度',
    gpsLongitude: 'GPS经度',
    gpsAltitude: 'GPS海拔',
    gpsSpeed: 'GPS速度',
    gpsSatellites: 'GPS卫星数',
    gpsQuality: 'GPS信号质量',
    gpsAccuracy: 'GPS精度',
    lux: '照度'
  },
  buttons: {
    refresh: '刷新数据',
    clear: '清空图表',
    export: '导出数据'
  },
  charts: {
    temperature: '温度趋势',
    humidity: '湿度趋势'
  },
  programming: {
    title: '编程界面',
    // Project controls
    projectName: '项目名称：',
    saveAsIno: '导出为 .ino',
    saveAsJson: '导出为 JSON',
    importJson: '导入 JSON',
    
    // Tooltips
    downloadInoTooltip: '下载 Arduino .ino 文件',
    downloadJsonTooltip: '下载 Blockly 项目为 JSON',
    importJsonTooltip: '从 JSON 导入 Blockly 项目',
    exportDisabledWarning: '工作区存在警告时无法导出',
    
    // Error messages
    fileTypeNotSupported: '不支持的文件类型。请选择 .json 文件。',
    failedToImportProject: '导入项目失败：',
    
    // Category names
    categorySetup: '设置',
    categorySensors: '传感器',
    categoryValuesVariables: '数值与变量',
    categoryCommunication: '通信',
    categoryControl: '控制',
    categoryOperatorsConversions: '运算符与类型转换',
    logicalOperators: '逻辑运算符',
    mathOperators: '数学运算符',
    typeConversions: '类型转换',
    convertToString: '转换为字符串',
    convertToStringTooltip: '将整数或浮点数转换为字符串',
    convertToInt: '转换为整数',
    convertToIntTooltip: '将字符串或浮点数转换为整数',
    convertToFloat: '转换为浮点数',
    convertToFloatTooltip: '将字符串或整数转换为浮点数',
    
    textOperations: '文本操作',
    
    dragHint: '将积木拖拽到这里开始编程',
    dragSubHint: '从工具箱中选择积木并拖拽到工作区来创建程序',
    generatedCode: '生成的代码',
    jsCodeOutput: 'Arduino C 代码输出',
    loadingInterface: '正在加载编程界面...',
    loadingFailed: '加载失败',
    
    // Block labels and tooltips
    // WiFi Setup
    setWifiLabel: '设置WiFi SSID',
    wifiPassword: '密码',
    setWifiTooltip: '设置WiFi凭据并调用setWiFi()',
    
    // Sensor blocks
    bme280Sensor: 'BME280（温湿度传感器）',
    pms7003Sensor: 'PMS7003（空气质量传感器）',
    mhz19bSensor: 'MHZ19B（CO2传感器）',
    windSensor: '风速传感器',
    ltr390Sensor: 'LTR390（UV传感器）',
    gpsSensor: 'GPS传感器',
    
    // Sensor blocks
    bmeTemperature: 'BME280 温度',
    bmeHumidity: 'BME280 湿度',
    bmePressure: 'BME280 气压',
    pmsPm1: 'PMS7003 PM1.0',
    pmsPm25: 'PMS7003 PM2.5',
    pmsPm10: 'PMS7003 PM10',
    pmsN03: 'PMS7003 N0.3',
    pmsN05: 'PMS7003 N0.5',
    pmsN10: 'PMS7003 N1.0',
    pmsN25: 'PMS7003 N2.5',
    pmsN50: 'PMS7003 N5.0',
    pmsN100: 'PMS7003 N10.0',
    mhzCo2: 'MHZ19B CO2',
    mhzMinCo2: 'MHZ19B 最小CO2',

    
    // Sensor tooltips
    readBmeTemperatureTooltip: '从BME280传感器读取温度',
    readBmeHumidityTooltip: '从BME280传感器读取湿度',
    readBmePressureTooltip: '从BME280传感器读取气压',
    readPm1Tooltip: '从PMS7003读取PM1.0',
    readPm25Tooltip: '从PMS7003读取PM2.5',
    readPm10Tooltip: '从PMS7003读取PM10',
    readN03Tooltip: '从PMS7003读取N0.3',
    readN05Tooltip: '从PMS7003读取N0.5',
    readN10Tooltip: '从PMS7003读取N1.0',
    readN25Tooltip: '从PMS7003读取N2.5',
    readN50Tooltip: '从PMS7003读取N5.0',
    readN100Tooltip: '从PMS7003读取N10.0',
    readCo2Tooltip: '从MHZ19B读取CO2',
    readMinCo2Tooltip: '从MHZ19B读取最小CO2',

    
    // Sensor Status blocks
    bme280Status: 'BME280（温湿度传感器）状态',
    pms7003Status: 'PMS7003（PM2.5传感器）状态',
    mhz19bStatus: 'MHZ19B（CO2传感器）状态',
    windSpeedStatus: '风速传感器状态',
    uvStatus: 'UV传感器状态',
    gpsStatus: 'GPS传感器状态',
    getBme280StatusTooltip: '检查BME280传感器是否连接并正常工作',
    getPms7003StatusTooltip: '检查PMS7003传感器是否连接并正常工作',
    getMhz19bStatusTooltip: '检查MHZ19B传感器是否连接并正常工作',
    getWindSpeedStatusTooltip: '检查风速传感器是否连接并正常工作',
    getUvStatusTooltip: '检查UV传感器是否连接并正常工作',
    getGpsStatusTooltip: '检查GPS传感器是否连接并正常工作',
    
    // Wind Speed Sensor Variables
    windSpeed: '风速',
    windDirection: '风向',
    readWindSpeedTooltip: '读取风速数值',
    readWindDirectionTooltip: '读取风向数值',
    
    // UV Sensor Variables
    uvIndex: 'UV指数',
    uvIntensity: 'UV强度',
    readUvIndexTooltip: '读取UV指数数值',
    readUvIntensityTooltip: '读取UV强度数值',
    
    // GPS Sensor Variables
    gpsLatitude: 'GPS纬度',
    gpsLongitude: 'GPS经度',
    gpsAltitude: 'GPS海拔',
    gpsSpeed: 'GPS速度',
    gpsSatellites: 'GPS卫星数',
    readGpsLatitudeTooltip: '读取GPS纬度坐标',
    readGpsLongitudeTooltip: '读取GPS经度坐标',
    readGpsAltitudeTooltip: '读取GPS海拔数值',
    readGpsSpeedTooltip: '读取GPS速度数值',
    readGpsSatellitesTooltip: '读取GPS卫星数量',
    
    // Main category labels
    setupMainLabel: '系统设置与配置',
    sensorsMainLabel: '传感器状态',
    valuesVariablesMainLabel: '数值变量与数据操作',
    operatorsConversionsMainLabel: '运算符与类型转换',
    communicationMainLabel: '数据通信',
    controlMainLabel: '程序控制',
    
    // Setup section labels
    wifiConfiguration: 'WiFi配置',
    sensorConfiguration: '传感器配置',

    // Sensor section labels
    getSensorStatus: '获取传感器状态',
    getSensorVariables: '获取传感器变量',
      
    // Values & Variables section labels
    // Typed Sections
    integerSection: '整数',
    floatSection: '浮点数',
    textSection: '文本',
    createIntVariable: '创建整数变量...',
    createFloatVariable: '创建浮点数变量...',
    createTextVariable: '创建文本变量...',
    
    // Variable operations
    getIntVariableTooltip: '获取整数变量的值',
    getFloatVariableTooltip: '获取浮点数变量的值',
    getTextVariableTooltip: '获取文本变量的值',
    setIntVariable: '设置整数',
    setFloatVariable: '设置浮点数',
    setTextVariable: '设置文本',
    setIntVariableTooltip: '将整数变量设置为一个值',
    setFloatVariableTooltip: '将浮点数变量设置为一个值',
    setTextVariableTooltip: '将文本变量设置为一个值',
    toValue: '为',
    textLabel: '文本',
    

    
    // Communication section labels
    serialCommunication: '串口通信',
    networkCommunication: '网络通信',
    
    // Control section labels
    conditionalControl: '条件控制',
    timeControl: '时间控制',
    
    // Arrays section
    arraySection: '数组',
    arraySum: '求和',
    arrayAverage: '平均值',
    arrayMin: '最小值',
    arrayMax: '最大值',
    arrayGet: '获取数组的',
    arrayOf: ' ',
    arrayAggregateTooltip: '计算数组的聚合值',
    

    bme280SensorVariables: 'BME280传感器变量',
    pms7003SensorVariables: 'PMS7003传感器变量',
    mhz19bSensorVariables: 'MHZ19B传感器变量',
    windSpeedSensorVariables: '风速传感器变量',
    uvSensorVariables: 'UV传感器变量',
    gpsSensorVariables: 'GPS传感器变量',
    

    
    // Communication blocks
    serialPrint: '串口输出',
    serialPrintTooltip: '向Arduino串口监视器发送数据',
    sendToServer: '发送',
    sendToServerTooltip: '通过ESP32发送选定的传感器数据到服务器',
    sensorData: '数据到服务器',
    allSensors: '所有传感器',
    allData: '所有数据',
    
    // Grouped sensor data communication
    sendGroupedData: '发送',
    dataToServer: '数据到服务器',
    sendGroupedDataTooltip: '使用新的结构化格式发送分组传感器数据到服务器',
    
    // Log blocks
    sendLogToServer: '发送',
    logToServer: '日志到服务器',
    logMessage: '消息',
    logAlert: '警报',
    logWarning: '警告',
    sendLogToServerTooltip: '发送指定级别的日志消息到服务器',
    
    // Control blocks
    delayLabel: '延时',
    delayMilliseconds: '毫秒',
    delayTooltip: '等待指定的毫秒数',
    
    // Value blocks
    integerValue: '整数值',
    floatValue: '浮点数值',
    stringValue: '字符串值',
    intLabel: '整数',
    floatLabel: '浮点数',
    textDefault: '文本',
    trueLabel: '真',
    falseLabel: '假',
    
    // Text and List creation blocks
    createTextWith: '创建文本使用',
    createListWith: '创建列表使用',
    createEmptyList: '创建空列表',
    createList: '创建列表',
    createListTooltip: '添加、删除或重新排列项目来重新配置此列表块。',
    joinText: '连接文本',
    joinTextTooltip: '将多个文本值连接在一起',
    item: '项目',
    itemTooltip: '向列表添加一个项目。',
    
    // Array validation messages
    arrayStringMixError: '数组不能混合字符串值与数字',
    arrayIntFloatMixError: '数组不能混合整数和浮点数值。请使用 convert_to_float 或 convert_to_int 块进行类型转换',
    
    // Arduino functions
    setupFunction: 'setup函数()',
    setupTooltip: 'Arduino setup()函数',
    loopFunction: 'loop函数()',
    loopTooltip: 'Arduino loop()函数',
    
    // 逻辑比较
    compareTooltip: '比较两个值。对于字符串，只允许==和!=，且两边都必须是字符串。',
    stringComparisonError: '字符串类型只能使用==或!=，且两边都必须是字符串。',
    stringComparisonWarning: '字符串比较时两边都必须是字符串。',
    typeError: '只允许int、float或bool类型。',
    typeMismatchError: '类型不匹配错误 - 所有值应具有相同的类型',
    arrayNestedError: '数组不能包含其他数组（子列表）',
    
    // 错误信息
    blocklyContainerNotFound: '找不到Blockly容器',
    noWorkspaceError: '没有可用的工作区',
    
    // 项目相关
    enterProjectName: '输入项目名称',
    
    // 相对时间
    justNow: '刚刚',
    secondsAgo: (count) => `${count}秒前`,
    oneMinuteAgo: '1分钟前',
    minutesAgo: (count) => `${count}分钟前`,
    oneHourAgo: '1小时前',
    hoursAgo: (count) => `${count}小时前`,
    oneDayAgo: '1天前',
    daysAgo: (count) => `${count}天前`,
    
        
    // 手动保存和清空工作区
    manualSave: '保存',
    clearWorkspace: '清除',
    manualSaveTooltip: '将工作区保存到本地存储',
    clearWorkspaceTooltip: '清除工作区',
    saving: '保存中...',
    savedAt: '保存于',
    manuallySavedAt: '手动保存于',
    autoSavedAt: '自动保存于',
    
    // Text Append Block
    textAppendTo: '向变量',
    textAppendText: '追加文本',
    textAppendTooltip: '将文本追加到指定变量的末尾',
    
    // Code Panel Collapse
    collapseCodePanel: '收起代码面板',
    expandCodePanel: '展开代码面板',
    generatedCodeCollapsed: '生成代码',
    
    // Code Panel Resize
    dragToResize: '拖拽调整大小',
    
    // Length blocks (custom overrides of built-in blocks)
    textLengthLabel: '文本长度',
    textLengthTooltip: '返回提供的文本中的字符数（包括空格）',
    arrayLengthLabel: '数组长度',
    arrayLengthTooltip: '返回数组的长度',
    
    // Array index blocks (custom overrides of built-in blocks)
    arrayIndexGet: '获取',
    arrayIndexSet: '设置',
    arrayIndexInsertAt: '插入到',
    arrayIndexFromStart: '第 #',
    arrayIndexFromEnd: '倒数第 #',
    arrayIndexInList: '在数组',
    arrayIndexToValue: '为',
    arrayGetIndexTooltip: '返回数组中指定位置的项目',
    arraySetIndexTooltip: '设置数组中指定位置的项目',

    // Array sublist blocks
    arraySublistGetSublist: '获取子数组，从数组',
    arraySublistTo: '到',
    arraySublistTooltip: '创建数组某部分的副本',

    // Math blocks (custom overrides of built-in blocks)
    mathArithmeticTooltip: '对数字执行算术运算',
    mathSingleTooltip: '执行单值数学运算',
    mathRoundTooltip: '将数字四舍五入为整数',
    mathSquareRoot: '平方根',
    mathAbsolute: '绝对值',
    mathRound: '四舍五入',
    mathRoundUp: '向上舍入',
    mathRoundDown: '向下舍入',
    stringOperatorWarning: '字符串值只能使用 = 或 ≠ 运算符进行比较'
  },
  log: {
    title: '系统日志',
    noLogs: '没有可用的日志条目',
    devicesLoaded: '从服务器加载可用设备',
    devicesLoadError: '无法加载可用设备',
    deviceSelected: '已选择设备进行监控',
    deviceDeselected: '已清除设备选择',
    serverError: '服务器通信错误',
    noSensorData: '所选设备没有传感器数据',
    historyError: '无法加载历史数据',
    deletingDevice: '正在删除设备数据...',
    deviceDeleted: '设备数据删除成功',
    deleteError: '无法删除设备数据',
    deviceLogsError: '无法加载设备日志'
  },
  // Blockly Blocks Translation
  blockly: {
    // Math blocks - 数学块
    MATH_NUMBER_HELPURL: 'https://zh.wikipedia.org/wiki/数字',
    MATH_NUMBER_TOOLTIP: '一个数字。',
    MATH_ARITHMETIC_TOOLTIP_ADD: '返回两个数字的和。',
    MATH_ARITHMETIC_TOOLTIP_MINUS: '返回两个数字的差。',
    MATH_ARITHMETIC_TOOLTIP_MULTIPLY: '返回两个数字的积。',
    MATH_ARITHMETIC_TOOLTIP_DIVIDE: '返回两个数字的商。',
    MATH_ARITHMETIC_TOOLTIP_POWER: '返回第一个数字的第二个数字次幂。',
    MATH_SINGLE_TITLE: '%1 %2',
    MATH_SINGLE_OP_ROOT: '平方根',
    MATH_SINGLE_OP_ABSOLUTE: '绝对值',
    MATH_SINGLE_OP_NEG: '-',
    MATH_SINGLE_OP_LN: 'ln',
    MATH_SINGLE_OP_LOG10: 'log10',
    MATH_SINGLE_OP_EXP: 'e^',
    MATH_SINGLE_OP_POW10: '10^',
    MATH_SINGLE_TOOLTIP_ROOT: '返回数字的平方根。',
    MATH_SINGLE_TOOLTIP_ABS: '返回数字的绝对值。',
    MATH_SINGLE_TOOLTIP_NEG: '返回数字的负值。',
    MATH_SINGLE_TOOLTIP_LN: '返回数字的自然对数。',
    MATH_SINGLE_TOOLTIP_LOG10: '返回数字的以10为底的对数。',
    MATH_SINGLE_TOOLTIP_EXP: '返回e的数字次幂。',
    MATH_SINGLE_TOOLTIP_POW10: '返回10的数字次幂。',
    MATH_ROUND_TITLE: '四舍五入 %1',
    MATH_ROUND_TOOLTIP: '将数字向上或向下舍入。',
    MATH_ROUND_OPERATOR_ROUND: '四舍五入',
    MATH_ROUND_OPERATOR_ROUNDUP: '向上舍入',
    MATH_ROUND_OPERATOR_ROUNDDOWN: '向下舍入',
    
    // Logic blocks - 逻辑块
    LOGIC_BOOLEAN_TRUE: '真',
    LOGIC_BOOLEAN_FALSE: '假',
    LOGIC_BOOLEAN_TOOLTIP: '返回真或假。',
    LOGIC_COMPARE_TOOLTIP_EQ: '如果两个输入相等，返回真。',
    LOGIC_COMPARE_TOOLTIP_NEQ: '如果两个输入不相等，返回真。',
    LOGIC_COMPARE_TOOLTIP_LT: '如果第一个输入小于第二个输入，返回真。',
    LOGIC_COMPARE_TOOLTIP_LTE: '如果第一个输入小于或等于第二个输入，返回真。',
    LOGIC_COMPARE_TOOLTIP_GT: '如果第一个输入大于第二个输入，返回真。',
    LOGIC_COMPARE_TOOLTIP_GTE: '如果第一个输入大于或等于第二个输入，返回真。',
    LOGIC_OPERATION_TOOLTIP_AND: '如果两个输入都为真，返回真。',
    LOGIC_OPERATION_TOOLTIP_OR: '如果至少有一个输入为真，返回真。',
    LOGIC_OPERATION_AND: '且',
    LOGIC_OPERATION_OR: '或',
    LOGIC_NEGATE_TITLE: '非 %1',
    LOGIC_NEGATE_TOOLTIP: '如果输入为假，返回真。如果输入为真，返回假。',
    LOGIC_NULL: '空值',
    LOGIC_NULL_TOOLTIP: '返回空值。',

    // Control blocks - 控制块
    CONTROLS_IF_MSG_IF: '如果',
    CONTROLS_IF_MSG_THEN: '执行',
    CONTROLS_IF_MSG_ELSE: '否则',
    CONTROLS_IF_MSG_ELSEIF: '否则如果',
    CONTROLS_IF_TOOLTIP_1: '如果条件为真，则执行某些语句。',
    CONTROLS_IF_TOOLTIP_2: '如果条件为真，则执行第一组语句。否则执行第二组语句。',
    CONTROLS_IF_TOOLTIP_3: '如果第一个条件为真，则执行第一组语句。否则，如果第二个条件为真，执行第二组语句。',
    CONTROLS_IF_TOOLTIP_4: '如果第一个条件为真，则执行第一组语句。否则，如果第二个条件为真，执行第二组语句。如果所有条件都为假，执行最后一组语句。',
    CONTROLS_IF_IF_TITLE_IF: '如果',
    CONTROLS_IF_ELSEIF_TITLE_ELSEIF: '否则如果',
    CONTROLS_IF_ELSE_TITLE_ELSE: '否则',
    
    // Variables blocks - 变量块
    VARIABLES_GET_TOOLTIP: '返回此变量的值。',
    VARIABLES_GET_CREATE_SET: '创建"设置 %1"',
    VARIABLES_SET: '设置 %1 为 %2',
    VARIABLES_SET_TOOLTIP: '将此变量设置为等于输入。',
    VARIABLES_SET_CREATE_GET: '创建"获取 %1"',
    NEW_VARIABLE: '创建变量...',
    NEW_VARIABLE_TITLE: '新变量名称：',
    RENAME_VARIABLE: '重命名变量...',
    RENAME_VARIABLE_TITLE: '将所有"%1"变量重命名为：',
    DELETE_VARIABLE: '删除"%1"变量',
    DELETE_VARIABLE_CONFIRMATION: '删除"%2"变量的 %1 个使用？',
    VARIABLE_ALREADY_EXISTS: '名为"%1"的变量已存在。',
    VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE: '名为"%1"的变量已存在于另一种类型："%2"。',
    
    // Text blocks - 文本块
    TEXT_JOIN_TITLE_CREATEWITH: '创建文本使用',
    TEXT_JOIN_TOOLTIP: '通过连接任意数量的项目来创建一段文本。',
    TEXT_CREATE_JOIN_TITLE_JOIN: '连接',

    
    // Lists blocks - 数组块
    LISTS_CREATE_WITH_TOOLTIP: '创建包含任意数量项目的数组。',
    LISTS_CREATE_WITH_INPUT_WITH: '创建数组包含',
    LISTS_CREATE_WITH_CONTAINER_TITLE_ADD: '数组',
    LISTS_CREATE_WITH_CONTAINER_TOOLTIP: '添加、删除或重新排列部分来重新配置此数组块。',
    LISTS_CREATE_WITH_ITEM_TOOLTIP: '向数组添加一个项目。',


    LISTS_INDEX_FROM_END_TOOLTIP: '%1 是最后一个项目，%2 是倒数第二个项目，以此类推。',
    LISTS_GET_SUBLIST_TOOLTIP: '创建数组指定部分的副本。',
    LISTS_GET_SUBLIST_INPUT_IN_LIST: '在数组',
    LISTS_GET_SUBLIST_START_FROM_START: '获取子数组从第',
    LISTS_GET_SUBLIST_START_FROM_END: '获取子数组从倒数第',
    LISTS_GET_SUBLIST_START_FIRST: '获取子数组从第一个',
    LISTS_GET_SUBLIST_END_FROM_START: '到第',
    LISTS_GET_SUBLIST_END_FROM_END: '到倒数第',
    LISTS_GET_SUBLIST_END_LAST: '到最后一个',
    LISTS_GET_SUBLIST_TAIL: '',
    
    // 错误信息
    blocklyContainerNotFound: '找不到Blockly容器'
  },
  timeRange: {
    oneMin: '近1分钟',
    threeMins: '近3分钟',
    fiveMins: '近5分钟',
    fifteenMins: '近15分钟',
    oneHour: '近1小时',
    fourHours: '近4小时',
    oneDay: '近1天'
  },
  report: {
    title: '传感器数据报告',
    subtitle: '历史传感器数据分析和导出',
    loading: '正在加载报告数据...',
    deviceFilter: '设备筛选',
    selectDevices: '选择设备',
    searchDevices: '搜索设备...',
    noDevicesFound: '未找到设备',
    selectAll: '全选',
    deselectAll: '全不选',
    columnFilter: '选择列',
    selectColumns: '选择列',
    searchColumns: '搜索列...',
    timeWindow: '时间窗口',
    noAggregation: '0ms（不合并数据）',
    noColumnsFound: '未找到列',
    selectGroup: '全选',
    deselectGroup: '取消全选',
    exportCsv: '导出CSV',
    refresh: '刷新',
    loadingOriginalData: '正在加载原始数据...',
    loadingAggregatedData: '正在加载聚合数据...',
    table: {
      basicInfo: '基本信息',
      timeDevice: '设备 / 时间',
      mhz19bSensor: 'MH-Z19B CO2传感器',
      pms7003Sensor: 'PMS7003颗粒物传感器',
      pms7003ParticleCount: 'PMS7003颗粒物计数',
      bme280Sensor: 'BME280环境传感器',
      co2Ppm: 'CO2 (ppm)',
      minCo2Ppm: '最小CO2 (ppm)',
      pm1: 'PM1.0 (μg/m³)',
      pm25: 'PM2.5 (μg/m³)',
      pm10: 'PM10 (μg/m³)',
      n0p3: 'N0.3μm (#/0.1L)',
      n0p5: 'N0.5μm (#/0.1L)',
      n1p0: 'N1.0μm (#/0.1L)',
      n2p5: 'N2.5μm (#/0.1L)',
      n5p0: 'N5.0μm (#/0.1L)',
      n10p0: 'N10.0μm (#/0.1L)',
      temperature: '温度 (°C)',
      humidity: '湿度 (%)',
      pressure: '压力 (Pa)',
      windSensor: '风速传感器',
      windSpeed: '风速 (m/s)',
      uvSensor: 'LTR390 紫外线传感器',
      uvIndex: 'UV指数',
      uvLux: 'UV照度 (lux)',
      gpsSensor: 'GPS定位传感器',
      gpsLatitude: 'GPS纬度',
      gpsLongitude: 'GPS经度',
      gpsAltitude: 'GPS海拔 (m)',
      gpsQuality: 'GPS信号质量',
      gpsSatellites: 'GPS卫星数',
      gpsAccuracy: 'GPS精度 (m)'
    },
    messages: {
      noDeviceSelected: '请至少选择一台设备以查看数据',
      noDataAvailable: '所选设备暂无可用数据',
      showingRecords: '显示',
      recordsFrom: '条记录来自',
      devices: '台设备'
    },
    loadMore: '加载更多',
    loadingMore: '加载中...'
  }
}; 