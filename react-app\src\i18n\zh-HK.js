export default {
  nav: {
    dashboard: '儀表板',
    programming: '圖形化編程界面',
    report: '數據庫報告'
  },
  dashboard: {
    title: '傳感器數據儀表板',
    subtitle: '實時傳感器數據監控和可視化',
    selectDevice: '選擇設備',
    selectDevicePlaceholder: '請選擇設備...',
    searchDevice: '搜索設備...',
    clearSelection: '清除選擇',
    noDevicesFound: '未找到設備',
    noDeviceSelected: '未選擇設備',
    selectDeviceToStart: '請從上方下拉菜單中選擇一個設備以開始監控傳感器數據。',
    loadingDeviceData: '正在加載設備數據並建立連接...',
    selectedDevice: '已選設備',
    deleteData: '刪除數據',
    deleteDeviceData: '刪除此設備的所有數據',
    confirmDelete: '確認刪除',
    confirmDeleteMessage: '您確定要刪除設備"{device}"的所有數據嗎？此操作無法撤銷。',
    cancel: '取消',
    deleteConfirm: '刪除',
    dataTab: '傳感器數據',
    chartsTab: '數據圖表',
    compositionTab: '組合分析',
    chartsTitle: '傳感器數據分析',
    chartsDescription: '互動式圖表和實時傳感器數據可視化。',
    selectSensors: '選擇傳感器',
    selectSensorsLabel: '選擇傳感器 (最多6個)',
    searchSensors: '搜尋傳感器...',
    noSensorsFound: '未找到傳感器',
    noSensorsSelected: '未選擇傳感器',
    selectSensorsToView: '請選擇傳感器查看數據',
    noDataAvailable: '無數據可用',
    selectDeviceToView: '請選擇設備查看傳感器數據',
    useDropdownToSelect: '使用下拉選單選擇傳感器',
    compositionTitle: '傳感器數據組合',
    compositionDescription: '多傳感器數據對比和分析',
    expandView: '展開視圖',
    expandedAnalysis: '展開分析',
    detailedView: '詳細查看和分析',
    sensorData: '傳感器數據',
    manageSensors: '管理傳感器',
    statisticalSummary: '統計摘要',
    sensor: '傳感器',
    current: '當前值',
    min: '最小值',
    max: '最大值',
    avg: '平均值',
    selectGroup: '全選',
    deselectGroup: '取消全選',
    openInMaps: '在地圖中打開'
  },
  status: {
    connection: '連接狀態',
    connecting: '連接中...',
    online: '在線',
    offline: '離線',
    records: '數據記錄',
    lastUpdate: '最後更新',
    mockData: '模擬數據',
    streaming: '串流傳輸',
    stopped: '已停止'
  },
  sensors: {
    temperature: '溫度',
    humidity: '濕度',
    pressure: '氣壓',
    pm1: 'PM1.0',
    pm25: 'PM2.5',
    pm10: 'PM10',
    n0p3: 'N0.3μm',
    n0p5: 'N0.5μm',
    n1p0: 'N1.0μm',
    n2p5: 'N2.5μm',
    n5p0: 'N5.0μm',
    n10p0: 'N10.0μm',
    co2: 'CO2濃度',
    mhzCo2: 'MH-Z19B CO2傳感器',
    pms7003: 'PMS7003顆粒物傳感器',
    bme280: 'BME280環境傳感器',
    minCo2: '最小CO2',
    pmConcentration: '顆粒物濃度',
    particleCount: '顆粒數量',
    windSpeedSensor: '風速傳感器',
    windSpeed: '風速',
    windDirection: '風向',
    uvSensor: 'UV傳感器',
    uvIndex: 'UV指數',
    uvIntensity: 'UV強度',
    gpsSensor: 'GPS傳感器',
    gpsLatitude: 'GPS緯度',
    gpsLongitude: 'GPS經度',
    gpsAltitude: 'GPS海拔',
    gpsSpeed: 'GPS速度',
    gpsSatellites: 'GPS衛星數',
    gpsQuality: 'GPS信號質量',
    gpsAccuracy: 'GPS精度',
    lux: '光照度'
  },
  buttons: {
    refresh: '刷新數據',
    clear: '清空圖表',
    export: '導出數據'
  },
  charts: {
    temperature: '溫度趨勢',
    humidity: '濕度趨勢'
  },
  programming: {
    title: '編程界面',
    // Project controls
    projectName: '項目名稱：',
    saveAsIno: '導出為 .ino',
    saveAsJson: '導出為 JSON',
    importJson: '匯入 JSON',

    // Tooltips
    downloadInoTooltip: '下載 Arduino .ino 檔案',
    downloadJsonTooltip: '下載 Blockly 項目為 JSON',
    importJsonTooltip: '從 JSON 匯入 Blockly 項目',
    exportDisabledWarning: '工作區存在警告時無法匯出',

    // Error messages
    fileTypeNotSupported: '不支援的檔案類型。請選擇 .json 檔案。',
    failedToImportProject: '匯入項目失敗：',

    // Category names
    categorySetup: '設定',
    categorySensors: '傳感器',
    categoryValuesVariables: '數值與變數',
    categoryCommunication: '通訊',
    categoryControl: '控制',
    categoryOperatorsConversions: '運算符與類型轉換',
    logicalOperators: '邏輯運算符',
    mathOperators: '數學運算符',
    typeConversions: '類型轉換',
    convertToString: '轉換為字符串',
    convertToStringTooltip: '將整數或浮點數轉換為字符串',
    convertToInt: '轉換為整數',
    convertToIntTooltip: '將字符串或浮點數轉換為整數',
    convertToFloat: '轉換為浮點數',
    convertToFloatTooltip: '將字符串或整數轉換為浮點數',

    textOperations: '文本操作',

    dragHint: '將積木拖拽到這裡開始程式設計',
    dragSubHint: '從工具箱中選擇積木並拖拽到工作區來建立程式',
    generatedCode: '產生的程式碼',
    jsCodeOutput: 'Arduino C 程式碼輸出',
    loadingInterface: '正在載入程式設計介面...',
    loadingFailed: '載入失敗',

    // Block labels and tooltips
    // WiFi Setup
    setWifiLabel: '設定WiFi SSID',
    wifiPassword: '密碼',
    setWifiTooltip: '設定WiFi憑證並調用setWiFi()',

    // Sensor blocks
    bme280Sensor: 'BME280（溫濕度傳感器）',
    pms7003Sensor: 'PMS7003（空氣質量傳感器）',
    mhz19bSensor: 'MHZ19B（CO2傳感器）',
    windSensor: '風速傳感器',
    ltr390Sensor: 'LTR390（UV傳感器）',
    gpsSensor: 'GPS傳感器',

    // Sensor blocks
    bmeTemperature: 'BME280 溫度',
    bmeHumidity: 'BME280 濕度',
    bmePressure: 'BME280 氣壓',
    pmsPm1: 'PMS7003 PM1.0',
    pmsPm25: 'PMS7003 PM2.5',
    pmsPm10: 'PMS7003 PM10',
    pmsN03: 'PMS7003 N0.3',
    pmsN05: 'PMS7003 N0.5',
    pmsN10: 'PMS7003 N1.0',
    pmsN25: 'PMS7003 N2.5',
    pmsN50: 'PMS7003 N5.0',
    pmsN100: 'PMS7003 N10.0',
    mhzCo2: 'MHZ19B CO2',
    mhzMinCo2: 'MHZ19B 最小CO2',


    // Sensor tooltips
    readBmeTemperatureTooltip: '從BME280傳感器讀取溫度',
    readBmeHumidityTooltip: '從BME280傳感器讀取濕度',
    readBmePressureTooltip: '從BME280傳感器讀取氣壓',
    readPm1Tooltip: '從PMS7003讀取PM1.0',
    readPm25Tooltip: '從PMS7003讀取PM2.5',
    readPm10Tooltip: '從PMS7003讀取PM10',
    readN03Tooltip: '從PMS7003讀取N0.3',
    readN05Tooltip: '從PMS7003讀取N0.5',
    readN10Tooltip: '從PMS7003讀取N1.0',
    readN25Tooltip: '從PMS7003讀取N2.5',
    readN50Tooltip: '從PMS7003讀取N5.0',
    readN100Tooltip: '從PMS7003讀取N10.0',
    readCo2Tooltip: '從MHZ19B讀取CO2',
    readMinCo2Tooltip: '從MHZ19B讀取最小CO2',


    // Sensor Status blocks
    bme280Status: 'BME280（溫濕度傳感器）狀態',
    pms7003Status: 'PMS7003（PM2.5傳感器）狀態',
    mhz19bStatus: 'MHZ19B（CO2傳感器）狀態',
    windSpeedStatus: '風速傳感器狀態',
    uvStatus: 'UV傳感器狀態',
    gpsStatus: 'GPS傳感器狀態',
    getBme280StatusTooltip: '檢查BME280傳感器是否連接並正常工作',
    getPms7003StatusTooltip: '檢查PMS7003傳感器是否連接並正常工作',
    getMhz19bStatusTooltip: '檢查MHZ19B傳感器是否連接並正常工作',
    getWindSpeedStatusTooltip: '檢查風速傳感器是否連接並正常工作',
    getUvStatusTooltip: '檢查UV傳感器是否連接並正常工作',
    getGpsStatusTooltip: '檢查GPS傳感器是否連接並正常工作',

    // Wind Speed Sensor Variables
    windSpeed: '風速',
    windDirection: '風向',
    readWindSpeedTooltip: '讀取風速數值',
    readWindDirectionTooltip: '讀取風向數值',

    // UV Sensor Variables
    uvIndex: 'UV指數',
    uvIntensity: 'UV強度',
    readUvIndexTooltip: '讀取UV指數數值',
    readUvIntensityTooltip: '讀取UV強度數值',

    // GPS Sensor Variables
    gpsLatitude: 'GPS緯度',
    gpsLongitude: 'GPS經度',
    gpsAltitude: 'GPS海拔',
    gpsSpeed: 'GPS速度',
    gpsSatellites: 'GPS衛星數',
    readGpsLatitudeTooltip: '讀取GPS緯度坐標',
    readGpsLongitudeTooltip: '讀取GPS經度坐標',
    readGpsAltitudeTooltip: '讀取GPS海拔數值',
    readGpsSpeedTooltip: '讀取GPS速度數值',
    readGpsSatellitesTooltip: '讀取GPS衛星數量',


    // Main category labels
    setupMainLabel: '系統設定與配置',
    sensorsMainLabel: '傳感器狀態',
    valuesVariablesMainLabel: '數值變數與資料操作',
    operatorsConversionsMainLabel: '運算符與類型轉換',
    communicationMainLabel: '資料通訊',
    controlMainLabel: '程式控制',

    // Setup section labels
    wifiConfiguration: 'WiFi配置',
    sensorConfiguration: '傳感器配置',

    // Sensor section labels
    getSensorStatus: '獲取傳感器狀態',
    getSensorVariables: '獲取傳感器變量',

    // Values & Variables section labels
    // Typed Variables
    integerSection: '整數',
    floatSection: '浮點數',
    textSection: '文字',
    createIntVariable: '創建整數變數...',
    createFloatVariable: '創建浮點數變數...',
    createTextVariable: '創建文字變數...',
    
    // Variable operations
    getIntVariableTooltip: '獲取整數變數的值',
    getFloatVariableTooltip: '獲取浮點數變數的值',
    getTextVariableTooltip: '獲取文字變數的值',
    setIntVariable: '設定整數',
    setFloatVariable: '設定浮點數',
    setTextVariable: '設定文字',
    setIntVariableTooltip: '將整數變數設定為一個值',
    setFloatVariableTooltip: '將浮點數變數設定為一個值',
    setTextVariableTooltip: '將文字變數設定為一個值',
    toValue: '為',
    textLabel: '文字',
    


    // Communication section labels
    serialCommunication: '串列通訊',
    networkCommunication: '網絡通訊',

    // Control section labels
    conditionalControl: '條件控制',
    timeControl: '時間控制',

    // Arrays section
    arraySection: '陣列',
    arraySum: '求和',
    arrayAverage: '平均值',
    arrayMin: '最小值',
    arrayMax: '最大值',
    arrayGet: '獲取陣列的',
    arrayOf: ' ',
    arrayAggregateTooltip: '計算陣列的聚合值',


    bme280SensorVariables: 'BME280傳感器變量',
    pms7003SensorVariables: 'PMS7003傳感器變量',
    mhz19bSensorVariables: 'MHZ19B傳感器變量',
    windSpeedSensorVariables: '風速傳感器變量',
    uvSensorVariables: 'UV傳感器變量',
    gpsSensorVariables: 'GPS傳感器變量',



    // Communication blocks
    serialPrint: '串列輸出',
    serialPrintTooltip: '向Arduino串列監視器發送數據',
    sendToServer: '發送',
    sendToServerTooltip: '通過ESP32發送選定的傳感器數據到伺服器',
    sensorData: '數據到伺服器',
    allSensors: '所有傳感器',
    allData: '所有數據',

    // Grouped sensor data communication
    sendGroupedData: '發送',
    dataToServer: '數據到伺服器',
    sendGroupedDataTooltip: '使用新的結構化格式發送分組傳感器數據到伺服器',

    // Log blocks
    sendLogToServer: '發送',
    logToServer: '日誌到伺服器',
    logMessage: '消息',
    logAlert: '警報',
    logWarning: '警告',
    sendLogToServerTooltip: '發送指定級別的日誌消息到伺服器',

    // Control blocks
    delayLabel: '延時',
    delayMilliseconds: '毫秒',
    delayTooltip: '等待指定的毫秒數',

    // Value blocks
    integerValue: '整數值',
    floatValue: '浮點數值',
    stringValue: '字串值',
    intLabel: '整數',
    floatLabel: '浮點數',
    textDefault: '文字',
    trueLabel: '真',
    falseLabel: '假',

    // Text and List creation blocks
    createTextWith: '創建文本使用',
    createListWith: '創建列表使用',
    createEmptyList: '創建空列表',
    createList: '創建列表',
    createListTooltip: '添加、刪除或重新排列項目來重新配置此列表塊。',
    joinText: '連接文本',
    joinTextTooltip: '將多個文本值連接在一起',
    item: '項目',
    itemTooltip: '向列表添加一個項目。',

    // Array validation messages
    arrayStringMixError: '陣列不能混合字符串值與數字',
    arrayIntFloatMixError: '陣列不能混合整數和浮點數值。請使用 convert_to_float 或 convert_to_int 塊進行類型轉換',

    // Arduino functions
    setupFunction: 'setup函數()',
    setupTooltip: 'Arduino setup()函數',
    loopFunction: 'loop函數()',
    loopTooltip: 'Arduino loop()函數',

    // 邏輯比較
    compareTooltip: '比較兩個值。對於字串，只允許==和!=，且兩邊都必須是字串。',
    stringComparisonError: '字串類型只能使用==或!=，且兩邊都必須是字串。',
    stringComparisonWarning: '字串比較時兩邊都必須是字串。',
    typeError: '只允許int、float或bool類型。',
    typeMismatchError: '類型不匹配錯誤 - 所有值應具有相同的類型',

    // 錯誤信息
    blocklyContainerNotFound: '找不到Blockly容器',
    noWorkspaceError: '沒有可用的工作區',

    // 項目相關
    enterProjectName: '輸入項目名稱',

    // Relative time
    justNow: '剛剛',
    secondsAgo: (count) => `${count}秒前`,
    oneMinuteAgo: '1分鐘前',
    minutesAgo: (count) => `${count}分鐘前`,
    oneHourAgo: '1小時前',
    hoursAgo: (count) => `${count}小時前`,
    oneDayAgo: '1天前',
    daysAgo: (count) => `${count}天前`,

    // 手動儲存和清空工作區
    manualSave: '儲存',
    clearWorkspace: '清除',
    manualSaveTooltip: '將工作區儲存到本機存儲',
    clearWorkspaceTooltip: '清除工作區',
    saving: '儲存中...',
    savedAt: '儲存於',
    manuallySavedAt: '手動儲存於',
    autoSavedAt: '自動儲存於',

    // Text Append Block
    textAppendTo: '向變數',
    textAppendText: '追加文字',
    textAppendTooltip: '將文字追加到指定變數的末尾',

    // Code Panel Collapse
    collapseCodePanel: '收起代碼面板',
    expandCodePanel: '展開代碼面板',
    generatedCodeCollapsed: '產生代碼',

    // Code Panel Resize
    dragToResize: '拖拽調整大小',
    
    // Length blocks (custom overrides of built-in blocks)
    textLengthLabel: '文字長度',
    textLengthTooltip: '返回提供的文字中的字符數（包括空格）',
    arrayLengthLabel: '陣列長度',
    arrayLengthTooltip: '返回陣列的長度',
    
    // Array index blocks (custom overrides of built-in blocks)
    arrayIndexGet: '獲取',
    arrayIndexSet: '設定',
    arrayIndexInsertAt: '插入到',
    arrayIndexFromStart: '第 #',
    arrayIndexFromEnd: '倒數第 #',
    arrayIndexInList: '在陣列',
    arrayIndexToValue: '為',
    arrayGetIndexTooltip: '返回陣列中指定位置的項目',
    arraySetIndexTooltip: '設定陣列中指定位置的項目',

    // Array sublist blocks
    arraySublistGetSublist: '獲取子陣列，從陣列',
    arraySublistTo: '到',
    arraySublistTooltip: '創建陣列某部分的副本',

    // Math blocks (custom overrides of built-in blocks)
    mathArithmeticTooltip: '對數字執行算術運算',
    mathSingleTooltip: '執行單值數學運算',
    mathRoundTooltip: '將數字四捨五入為整數',
    mathSquareRoot: '平方根',
    mathAbsolute: '絕對值',
    mathRound: '四捨五入',
    mathRoundUp: '向上舍入',
    mathRoundDown: '向下舍入',

    // Error messages
    arrayNestedError: '陣列不能包含其他陣列（子列表）',
    stringOperatorWarning: '字符串值只能使用 = 或 ≠ 運算符進行比較'
  },
  log: {
    title: '系統日誌',
    noLogs: '沒有可用的日誌條目',
    devicesLoaded: '從服務器加載可用設備',
    devicesLoadError: '無法加載可用設備',
    deviceSelected: '已選擇設備進行監控',
    deviceDeselected: '已清除設備選擇',
    serverError: '服務器通信錯誤',
    noSensorData: '所選設備沒有傳感器數據',
    historyError: '無法加載歷史數據',
    deletingDevice: '正在刪除設備數據...',
    deviceDeleted: '設備數據刪除成功',
    deleteError: '無法刪除設備數據',
    deviceLogsError: '無法加載設備日誌'
  },
  // Blockly Blocks Translation
  blockly: {
    // Math blocks - 數學塊
    MATH_NUMBER_HELPURL: 'https://zh.wikipedia.org/wiki/数字',
    MATH_NUMBER_TOOLTIP: '一個數字。',
    MATH_ARITHMETIC_TOOLTIP_ADD: '返回兩個數字的和。',
    MATH_ARITHMETIC_TOOLTIP_MINUS: '返回兩個數字的差。',
    MATH_ARITHMETIC_TOOLTIP_MULTIPLY: '返回兩個數字的積。',
    MATH_ARITHMETIC_TOOLTIP_DIVIDE: '返回兩個數字的商。',
    MATH_ARITHMETIC_TOOLTIP_POWER: '返回第一個數字的第二個數字次幂。',
    MATH_SINGLE_TITLE: '%1 %2',
    MATH_SINGLE_OP_ROOT: '平方根',
    MATH_SINGLE_OP_ABSOLUTE: '絕對值',
    MATH_SINGLE_OP_NEG: '-',
    MATH_SINGLE_OP_LN: 'ln',
    MATH_SINGLE_OP_LOG10: 'log10',
    MATH_SINGLE_OP_EXP: 'e^',
    MATH_SINGLE_OP_POW10: '10^',
    MATH_SINGLE_TOOLTIP_ROOT: '返回數字的平方根。',
    MATH_SINGLE_TOOLTIP_ABS: '返回數字的絕對值。',
    MATH_SINGLE_TOOLTIP_NEG: '返回數字的負值。',
    MATH_SINGLE_TOOLTIP_LN: '返回數字的自然對數。',
    MATH_SINGLE_TOOLTIP_LOG10: '返回數字的以10為底的對數。',
    MATH_SINGLE_TOOLTIP_EXP: '返回e的數字次幂。',
    MATH_SINGLE_TOOLTIP_POW10: '返回10的數字次幂。',
    MATH_ROUND_TITLE: '四捨五入 %1',
    MATH_ROUND_TOOLTIP: '將數字向上或向下舍入。',
    MATH_ROUND_OPERATOR_ROUND: '四捨五入',
    MATH_ROUND_OPERATOR_ROUNDUP: '向上舍入',
    MATH_ROUND_OPERATOR_ROUNDDOWN: '向下舍入',

    // Logic blocks - 邏輯塊
    LOGIC_BOOLEAN_TRUE: '真',
    LOGIC_BOOLEAN_FALSE: '假',
    LOGIC_BOOLEAN_TOOLTIP: '返回真或假。',
    LOGIC_COMPARE_TOOLTIP_EQ: '如果兩個輸入相等，返回真。',
    LOGIC_COMPARE_TOOLTIP_NEQ: '如果兩個輸入不相等，返回真。',
    LOGIC_COMPARE_TOOLTIP_LT: '如果第一個輸入小於第二個輸入，返回真。',
    LOGIC_COMPARE_TOOLTIP_LTE: '如果第一個輸入小於或等於第二個輸入，返回真。',
    LOGIC_COMPARE_TOOLTIP_GT: '如果第一個輸入大於第二個輸入，返回真。',
    LOGIC_COMPARE_TOOLTIP_GTE: '如果第一個輸入大於或等於第二個輸入，返回真。',
    LOGIC_OPERATION_TOOLTIP_AND: '如果兩個輸入都為真，返回真。',
    LOGIC_OPERATION_TOOLTIP_OR: '如果至少有一個輸入為真，返回真。',
    LOGIC_OPERATION_AND: '且',
    LOGIC_OPERATION_OR: '或',
    LOGIC_NEGATE_TITLE: '非 %1',
    LOGIC_NEGATE_TOOLTIP: '如果輸入為假，返回真。如果輸入為真，返回假。',
    LOGIC_NULL: '空值',
    LOGIC_NULL_TOOLTIP: '返回空值。',

    // Control blocks - 控制塊
    CONTROLS_IF_MSG_IF: '如果',
    CONTROLS_IF_MSG_THEN: '執行',
    CONTROLS_IF_MSG_ELSE: '否則',
    CONTROLS_IF_MSG_ELSEIF: '否則如果',
    CONTROLS_IF_TOOLTIP_1: '如果條件為真，則執行某些語句。',
    CONTROLS_IF_TOOLTIP_2: '如果條件為真，則執行第一組語句。否則執行第二組語句。',
    CONTROLS_IF_TOOLTIP_3: '如果第一個條件為真，則執行第一組語句。否則，如果第二個條件為真，執行第二組語句。',
    CONTROLS_IF_TOOLTIP_4: '如果第一個條件為真，則執行第一組語句。否則，如果第二個條件為真，執行第二組語句。如果所有條件都為假，執行最後一組語句。',
    CONTROLS_IF_IF_TITLE_IF: '如果',
    CONTROLS_IF_ELSEIF_TITLE_ELSEIF: '否則如果',
    CONTROLS_IF_ELSE_TITLE_ELSE: '否則',

    // Variables blocks - 變量塊
    VARIABLES_GET_TOOLTIP: '返回此變量的值。',
    VARIABLES_GET_CREATE_SET: '創建"設置 %1"',
    VARIABLES_SET: '設置 %1 為 %2',
    VARIABLES_SET_TOOLTIP: '將此變量設置為等於輸入。',
    VARIABLES_SET_CREATE_GET: '創建"獲取 %1"',
    NEW_VARIABLE: '創建變數...',
    NEW_VARIABLE_TITLE: '新變數名稱：',
    RENAME_VARIABLE: '重命名變數...',
    RENAME_VARIABLE_TITLE: '將所有"%1"變量重命名為：',
    DELETE_VARIABLE: '刪除"%1"變量',
    DELETE_VARIABLE_CONFIRMATION: '刪除"%2"變量的 %1 個使用？',
    VARIABLE_ALREADY_EXISTS: '名為"%1"的變量已存在。',
    VARIABLE_ALREADY_EXISTS_FOR_ANOTHER_TYPE: '名為"%1"的變量已存在於另一種類型："%2"。',

    // Text blocks - 文本塊
    TEXT_JOIN_TITLE_CREATEWITH: '創建文本使用',
    TEXT_JOIN_TOOLTIP: '通過連接任意數量的項目來創建一段文本。',
    TEXT_CREATE_JOIN_TITLE_JOIN: '連接',


    // Lists blocks - 陣列塊
    LISTS_CREATE_WITH_TOOLTIP: '建立包含任意數量項目的陣列。',
    LISTS_CREATE_WITH_INPUT_WITH: '建立陣列包含',
    LISTS_CREATE_WITH_CONTAINER_TITLE_ADD: '陣列',
    LISTS_CREATE_WITH_CONTAINER_TOOLTIP: '新增、刪除或重新排列部分來重新配置此陣列塊。',
    LISTS_CREATE_WITH_ITEM_TOOLTIP: '向陣列新增一個項目。',


    LISTS_INDEX_FROM_END_TOOLTIP: '%1 是最後一個項目，%2 是倒數第二個項目，以此類推。',
    LISTS_GET_SUBLIST_TOOLTIP: '建立陣列指定部分的副本。',
    LISTS_GET_SUBLIST_INPUT_IN_LIST: '在陣列',
    LISTS_GET_SUBLIST_START_FROM_START: '獲取子陣列從第',
    LISTS_GET_SUBLIST_START_FROM_END: '獲取子陣列從倒數第',
    LISTS_GET_SUBLIST_START_FIRST: '獲取子陣列從第一個',
    LISTS_GET_SUBLIST_END_FROM_START: '到第',
    LISTS_GET_SUBLIST_END_FROM_END: '到倒數第',
    LISTS_GET_SUBLIST_END_LAST: '到最後一個',
    LISTS_GET_SUBLIST_TAIL: '',

    // 錯誤信息
    blocklyContainerNotFound: '找不到Blockly容器'
  },
  timeRange: {
    oneMin: '近1分鐘',
    threeMins: '近3分鐘',
    fiveMins: '近5分鐘',
    fifteenMins: '近15分鐘',
    oneHour: '近1小時',
    fourHours: '近4小時',
    oneDay: '近1天'
  },
  report: {
    title: '傳感器數據報告',
    subtitle: '歷史傳感器數據分析和導出',
    loading: '正在載入報告數據...',
    deviceFilter: '設備篩選',
    selectDevices: '選擇設備',
    searchDevices: '搜索設備...',
    noDevicesFound: '未找到設備',
    selectAll: '全選',
    deselectAll: '全不選',
    columnFilter: '選擇列',
    selectColumns: '選擇列',
    searchColumns: '搜索列...',
    timeWindow: '時間窗口',
    noAggregation: '0ms（不合併數據）',
    noColumnsFound: '未找到列',
    selectGroup: '全選',
    deselectGroup: '取消全選',
    exportCsv: '導出CSV',
    refresh: '刷新',
    loadingOriginalData: '正在加載原始數據...',
    loadingAggregatedData: '正在加載聚合數據...',
    table: {
      basicInfo: '基本信息',
      timeDevice: '設備 / 時間',
      mhz19bSensor: 'MH-Z19B CO2傳感器',
      pms7003Sensor: 'PMS7003顆粒物傳感器',
      pms7003ParticleCount: 'PMS7003顆粒物計數',
      bme280Sensor: 'BME280環境傳感器',
      co2Ppm: 'CO2 (ppm)',
      minCo2Ppm: '最小CO2 (ppm)',
      pm1: 'PM1.0 (μg/m³)',
      pm25: 'PM2.5 (μg/m³)',
      pm10: 'PM10 (μg/m³)',
      n0p3: 'N0.3μm (#/0.1L)',
      n0p5: 'N0.5μm (#/0.1L)',
      n1p0: 'N1.0μm (#/0.1L)',
      n2p5: 'N2.5μm (#/0.1L)',
      n5p0: 'N5.0μm (#/0.1L)',
      n10p0: 'N10.0μm (#/0.1L)',
      temperature: '溫度 (°C)',
      humidity: '濕度 (%)',
      pressure: '壓力 (Pa)',
      windSensor: '風速傳感器',
      windSpeed: '風速 (m/s)',
      uvSensor: 'LTR390 紫外線傳感器',
      uvIndex: 'UV指數',
      uvLux: 'UV光照度 (lux)',
      gpsSensor: 'GPS定位傳感器',
      gpsLatitude: 'GPS緯度',
      gpsLongitude: 'GPS經度',
      gpsAltitude: 'GPS海拔 (m)',
      gpsQuality: 'GPS信號質量',
      gpsSatellites: 'GPS衛星數',
      gpsAccuracy: 'GPS精度 (m)'
    },
    messages: {
      noDeviceSelected: '請至少選擇一台設備以查看數據',
      noDataAvailable: '所選設備暫無可用數據',
      showingRecords: '顯示',
      recordsFrom: '條記錄來自',
      devices: '台設備'
    },
    loadMore: '載入更多',
    loadingMore: '載入中...'
  }
}; 