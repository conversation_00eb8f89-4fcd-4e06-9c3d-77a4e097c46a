import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import Layout from '../components/Layout';
import axios from 'axios';
import { getApiUrl } from '../config/api';
import SensorCardChart from '../components/charts/SensorCardChart';
import CompositionLine<PERSON>hart from '../components/charts/CompositionLineChart';
import { format } from 'date-fns';

// Device Selection Dropdown Component
const DeviceSelector = ({ selectedDevice, onDeviceChange, availableDevices, t, onDeleteDevice }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const dropdownRef = useRef(null);

  // Filter devices based on search term
  const filteredDevices = availableDevices.filter(device =>
    device.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleDeviceSelect = (device) => {
    onDeviceChange(device);
    setIsOpen(false);
    setSearchTerm('');
  };

  const clearSelection = () => {
    onDeviceChange('');
    setIsOpen(false);
    setSearchTerm('');
  };

  const handleDeleteClick = () => {
    if (selectedDevice) {
      setShowDeleteConfirm(true);
    }
  };

  const handleDeleteConfirm = () => {
    if (selectedDevice && onDeleteDevice) {
      onDeleteDevice(selectedDevice);
      setShowDeleteConfirm(false);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteConfirm(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <div className="flex items-center gap-2">
        <label className="text-sm font-medium text-gray-700 whitespace-nowrap">
          {t('dashboard.selectDevice')}:
        </label>
        <div className="relative">
          <button
            onClick={() => setIsOpen(!isOpen)}
            className="flex items-center justify-between w-64 px-3 py-2 text-sm border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <span className={selectedDevice ? 'text-gray-900' : 'text-gray-500'}>
              {selectedDevice || t('dashboard.selectDevicePlaceholder')}
            </span>
            <svg className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {isOpen && (
            <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-hidden">
              {/* Search input */}
              <div className="p-2 border-b border-gray-200">
                <input
                  type="text"
                  placeholder={t('dashboard.searchDevice')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                  autoFocus
                />
              </div>

              {/* Device options */}
              <div className="max-h-40 overflow-y-auto">
                {/* Clear selection option */}
                {selectedDevice && (
                  <button
                    onClick={clearSelection}
                    className="w-full px-3 py-2 text-left text-sm text-gray-500 hover:bg-gray-100 border-b border-gray-200"
                  >
                    {t('dashboard.clearSelection')}
                  </button>
                )}

                {/* Filtered device options */}
                {filteredDevices.length > 0 ? (
                  filteredDevices.map((device) => (
                    <button
                      key={device}
                      onClick={() => handleDeviceSelect(device)}
                      className={`w-full px-3 py-2 text-left text-sm hover:bg-gray-100 ${selectedDevice === device ? 'bg-blue-50 text-blue-600' : 'text-gray-900'
                        }`}
                    >
                      {device}
                    </button>
                  ))
                ) : (
                  <div className="px-3 py-2 text-sm text-gray-500">
                    {t('dashboard.noDevicesFound')}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Delete Button */}
        {selectedDevice && (
          <button
            onClick={handleDeleteClick}
            className="flex items-center px-3 py-2 text-sm text-red-600 border border-red-300 rounded-md bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-colors"
            title={t('dashboard.deleteDeviceData')}
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            {t('dashboard.deleteData')}
          </button>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="absolute top-full left-0 mt-2 bg-white rounded-lg shadow-lg border border-gray-200 p-6 w-96 z-50">
          <div className="flex items-center mb-4">
            <svg className="w-6 h-6 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <h3 className="text-lg font-semibold text-gray-900">
              {t('dashboard.confirmDelete')}
            </h3>
          </div>
          <p className="text-gray-600 mb-6">
            {t('dashboard.confirmDeleteMessage').replace('{device}', selectedDevice)}
          </p>
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleDeleteCancel}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              {t('dashboard.cancel')}
            </button>
            <button
              onClick={handleDeleteConfirm}
              className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500"
            >
              {t('dashboard.deleteConfirm')}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

const Dashboard = () => {
  const { t } = useLanguage();
  const [sensorData, setSensorData] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('offline');
  const [lastUpdate, setLastUpdate] = useState(null);
  const [dataHistory, setDataHistory] = useState([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [logs, setLogs] = useState([]);
  const [deviceLogs, setDeviceLogs] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedDevice, setSelectedDevice] = useState(() => {
    // Initialize from sessionStorage
    return sessionStorage.getItem('selectedDevice') || '';
  });
  const [availableDevices, setAvailableDevices] = useState([]);
  const [activeTab, setActiveTab] = useState('data'); // 'data' or 'charts'

  // Add log entry
  const addLog = useCallback((message, data = null, type = 'message') => {
    const timestamp = new Date();
    const time = timestamp.toLocaleTimeString();
    const logEntry = { 
      time, 
      message, 
      type, 
      timestamp: timestamp.toISOString(), // Preserve full timestamp for sorting
      isSystemLog: true // Mark as system log
    };
    if (data) {
      logEntry.data = data;
    }
    // Add new logs to the beginning to maintain newest-first order
    setLogs(prev => [logEntry, ...prev]);
  }, []);

  // Fetch available devices using new API
  const fetchAvailableDevices = useCallback(async () => {
    try {
      // Use the new endpoint for fetching grouped devices summary
      const response = await axios.get(getApiUrl('/grouped/devices/summary'));
      console.log("response.data for available devices", response.data);
      
      let deviceIds = [];
      // The API now returns an array of device objects with uuid property
      if (response.data && Array.isArray(response.data)) {
        deviceIds = response.data.map(device => device.uuid);
      } else {
        console.warn('API returned unexpected data format for device list:', response.data);
        deviceIds = [];
      }

      setAvailableDevices(deviceIds);
      addLog(t('log.devicesLoaded'), { 
        count: deviceIds.length, 
        devices: deviceIds
      });
    } catch (error) {
      console.error('Error fetching available devices:', error);
      setAvailableDevices([]);
      addLog(t('log.devicesLoadError'), { 
        error: error.message
      }, 'error');
    }
  }, [addLog, t]);

  // Lightweight device check function
  const checkForNewDevices = useCallback(async () => {
    try {
      const response = await axios.get(getApiUrl('/grouped/devices/basic'));
      const currentDevices = response.data || [];
      
      // Check if device count changed
      const hasChanges = currentDevices.length !== availableDevices.length;
      
      // If device count changed, refresh full list
      if (hasChanges) {
        await fetchAvailableDevices();
      }
      
    } catch (error) {
      console.error('Error checking for new devices:', error);
      // Fallback to full device list refresh on error
      if (availableDevices.length === 0) {
        await fetchAvailableDevices();
      }
    }
  }, [availableDevices, fetchAvailableDevices]);
  
  // Fetch history data for selected device using the unified API
  const fetchHistoryData = useCallback(async () => {
    if (!selectedDevice) {
      return;
    }

    try {
      // Use the new unified endpoint for device history (single API call)
      const response = await axios.get(getApiUrl(`/grouped/devices/${selectedDevice}/history`), {
        params: {
          pageSize: 100,
          offset: 0
        }
      });
      console.log("response.data for history", response.data);

      if (response.data && response.data.data) {
        const { data, pagination } = response.data;
        
        setDataHistory(data);
        setTotalRecords(pagination?.total || 0);
      } else {
        console.warn('API returned unexpected data format for history:', response.data);
        setDataHistory([]);
        setTotalRecords(0);
      }
    } catch (error) {
      console.error('Error fetching history data:', error);
      addLog(t('log.historyError'), { error: error.message }, 'error');
      setDataHistory([]);
      setTotalRecords(0);
    }
  }, [selectedDevice, addLog, t]);

  // Helper function to determine device online status based on latest data timestamp
  const checkDeviceOnlineStatus = useCallback((latestDataTimestamp) => {
    if (!latestDataTimestamp) {
      return 'offline';
    }

    const now = new Date();
    const dataTime = new Date(latestDataTimestamp);
    const timeDiffInSeconds = (now.getTime() - dataTime.getTime()) / 1000;

    // Device is considered online if latest data is within 10 seconds
    return timeDiffInSeconds <= 10 ? 'online' : 'offline';
  }, []);

  // Helper function to update connection status based on sensor data
  const updateConnectionStatus = useCallback((sensorData) => {
    if (sensorData && sensorData.timestamp) {
      const status = checkDeviceOnlineStatus(sensorData.timestamp);
      setConnectionStatus(status);
    } else {
      setConnectionStatus('offline');
    }
  }, [checkDeviceOnlineStatus]);

  // New function for polling latest data and updating incrementally
  const pollLatestData = useCallback(async () => {
    if (!selectedDevice) {
      return;
    }

    try {
      const response = await axios.get(getApiUrl(`/grouped/latest?uuid=${selectedDevice}`));
      if (response.data && response.data.row) {
        const newSensorData = response.data.row;

        // Update the main data display
        setSensorData(newSensorData);
        setLastUpdate(newSensorData.timestamp ? new Date(newSensorData.timestamp) : new Date());
        
        // Update connection status based on data timestamp
        updateConnectionStatus(newSensorData);

        // Incrementally update the history for charts
        setDataHistory(prevHistory => {
          // Avoid adding duplicate data points if the timestamp is the same
          if (prevHistory.length > 0 && prevHistory[0].timestamp === newSensorData.timestamp) {
            return prevHistory;
          }

          // Add new data to the beginning and keep the list at a max of 100 points
          const updatedHistory = [newSensorData, ...prevHistory];
          return updatedHistory.slice(0, 100);
        });
      }
    } catch (error) {
      console.error('Polling error:', error);
      setConnectionStatus('offline');
    }
  }, [selectedDevice, updateConnectionStatus]);

  // Fetch device logs for selected device
  const fetchDeviceLogs = useCallback(async () => {
    if (!selectedDevice) {
      setDeviceLogs([]);
      return;
    }

    try {
      const response = await axios.get(getApiUrl(`/logs/device/${selectedDevice}`), {
        params: {
          limit: 50,
          offset: 0
        }
      });

      if (response.data && response.data.logs) {
        const processedLogs = response.data.logs.map(log => ({
          time: new Date(log.timestamp).toLocaleTimeString(),
          message: log.message,
          type: log.log_type, // Use log_type from database
          timestamp: log.timestamp, // Preserve full timestamp for sorting
          isDeviceLog: true
        }));
        
        setDeviceLogs(processedLogs);
      } else {
        setDeviceLogs([]);
      }
    } catch (error) {
      console.error('Error fetching device logs:', error);
      setDeviceLogs([]);
      addLog(t('log.deviceLogsError'), { 
        device: selectedDevice,
        error: error.message 
      }, 'error');
    }
  }, [selectedDevice, addLog, t]);

  // Handle device selection change
  const handleDeviceChange = (device) => {
    setSelectedDevice(device);

    // Store device selection in sessionStorage
    if (device) {
      sessionStorage.setItem('selectedDevice', device);
      // Clear previous data
      setSensorData(null);
      setDataHistory([]);
      setTotalRecords(0);
      setConnectionStatus('offline');
      setLastUpdate(null);
      setIsLoading(true);
    } else {
      sessionStorage.removeItem('selectedDevice');
      // Clear all data when no device is selected
      setSensorData(null);
      setDataHistory([]);
      setTotalRecords(0);
      setConnectionStatus('offline');
      setLastUpdate(null);
      setIsLoading(false);
    }
  };

  // Delete device data
  const deleteDeviceData = useCallback(async (deviceUuid) => {
    try {
      setIsLoading(true);
      addLog(t('log.deletingDevice'), { device: deviceUuid });

      // Use the new endpoint for deleting grouped device data
      const response = await axios.delete(getApiUrl(`/grouped/data/${deviceUuid}`));

      if (response.status === 200) {
        addLog(t('log.deviceDeleted'), { device: deviceUuid });

        // Clear current data if this device was selected
        if (selectedDevice === deviceUuid) {
          setSensorData(null);
          setDataHistory([]);
          setTotalRecords(0);
          setConnectionStatus('offline');
          setLastUpdate(null);
          setSelectedDevice('');
          sessionStorage.removeItem('selectedDevice');
        }

        // Refresh device list
        await fetchAvailableDevices();
      }
    } catch (error) {
      console.error('Error deleting device data:', error);
      addLog(t('log.deleteError'), {
        device: deviceUuid,
        error: error.message
      }, 'error');
    } finally {
      setIsLoading(false);
    }
  }, [selectedDevice, addLog, t, fetchAvailableDevices]);

  // Helper function to format sensor values
  const formatSensorValue = (value, unit = '', decimals = 1) => {
    if (value === null || value === undefined) {
      return '--';
    }
    if (typeof value === 'number') {
      return decimals > 0 ? `${value.toFixed(decimals)}${unit}` : `${value}${unit}`;
    }
    return `${value}${unit}`;
  };

  // Effect for initial data load when device changes
  useEffect(() => {
    if (selectedDevice) {
      const initializeData = async () => {
        setIsLoading(true);
        
        // Fetch both history data and latest data in parallel
        await Promise.all([
          fetchHistoryData(), 
          fetchDeviceLogs(),
          pollLatestData() // Immediately get latest data on initialization
        ]);
        
        // Note: pollLatestData() will set sensorData, lastUpdate, and connectionStatus
        // We no longer need to rely on history data for initial sensor values
        setIsLoading(false);
      };
      initializeData();
    }
  }, [selectedDevice, fetchHistoryData, fetchDeviceLogs, pollLatestData, updateConnectionStatus]);

  // Effect for polling latest data
  useEffect(() => {
    if (selectedDevice) {
      // Setup auto-refresh interval for real-time data
      const dataIntervalRef = setInterval(() => {
        pollLatestData();
        fetchDeviceLogs();
      }, 2000); // Refresh every 2 seconds

      // Setup interval for refreshing total records count (less frequent)
      const recordsIntervalRef = setInterval(() => {
        // Refresh total records by re-fetching first page of history
        fetchHistoryData();
      }, 30000); // Refresh every 30 seconds to keep totalRecords accurate

      // Setup interval for periodic connection status check
      const statusIntervalRef = setInterval(() => {
        // Check if current sensor data is still considered "online" based on timestamp
        if (sensorData) {
          updateConnectionStatus(sensorData);
        }
      }, 5000); // Check every 5 seconds to update status promptly

      return () => {
        clearInterval(dataIntervalRef);
        clearInterval(recordsIntervalRef);
        clearInterval(statusIntervalRef);
      };
    } else {
      // If no device selected, check for new devices periodically
      const deviceCheckInterval = setInterval(() => {
        checkForNewDevices();
      }, 5000); // Check every 5 seconds when no device selected

      return () => {
        clearInterval(deviceCheckInterval);
      };
    }
  }, [selectedDevice, pollLatestData, fetchDeviceLogs, checkForNewDevices, fetchHistoryData, sensorData, updateConnectionStatus]);

  // Load available devices on component mount
  useEffect(() => {
    const initializeComponent = async () => {
      await fetchAvailableDevices();
    };

    initializeComponent();
  }, [fetchAvailableDevices]);

  return (
    <Layout>
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{t('dashboard.title')}</h1>
            <p className="text-gray-600">{t('dashboard.subtitle')}</p>
          </div>

          {/* Device Selector */}
          <DeviceSelector
            selectedDevice={selectedDevice}
            onDeviceChange={handleDeviceChange}
            availableDevices={availableDevices}
            t={t}
            onDeleteDevice={deleteDeviceData}
          />
        </div>

        {/* No Device Selected State */}
        {!selectedDevice && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
            <div className="text-center">
              <svg className="w-12 h-12 text-blue-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-700 mb-2">
                {t('dashboard.noDeviceSelected')}
              </h3>
              <p className="text-gray-600">
                {t('dashboard.selectDeviceToStart')}
              </p>
            </div>
          </div>
        )}

        {/* Loading State */}
        {selectedDevice && isLoading && (
          <div className="bg-white rounded-lg border border-gray-300 p-8 mb-6">
            <div className="flex flex-col items-center justify-center space-y-4">
              <div className="relative">
                <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-200"></div>
                <div className="animate-spin rounded-full h-12 w-12 border-4 border-blue-500 border-t-transparent absolute top-0 left-0"></div>
              </div>
              <div className="text-center">
                <h3 className="text-lg font-medium text-gray-700 mb-2">
                  {t('status.connecting')}
                </h3>
                <p className="text-gray-600 mb-4">
                  {t('dashboard.loadingDeviceData')}
                </p>
                <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
                  <span>{t('dashboard.selectedDevice')}:</span>
                  <span className="font-medium text-blue-600">{selectedDevice}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Dashboard Content - Only show when device is selected and not loading */}
        {selectedDevice && !isLoading && (
          <>
            {/* Status Bar */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className={`p-4 rounded-lg border ${connectionStatus === 'online'
                  ? 'bg-green-100 border-green-400'
                  : 'bg-red-100 border-red-400'
                }`}>
                <h3 className="text-sm font-medium text-gray-700">{t('status.connection')}</h3>
                <p className="text-lg font-semibold">
                  {connectionStatus === 'online' ? t('status.online') : t('status.offline')}
                </p>
              </div>
              <div className="bg-gray-100 p-4 rounded-lg border border-gray-300">
                <h3 className="text-sm font-medium text-gray-700">{t('status.records')}</h3>
                <p className="text-lg font-semibold">{totalRecords}</p>
              </div>
              <div className="bg-gray-100 p-4 rounded-lg border border-gray-300">
                <h3 className="text-sm font-medium text-gray-700">{t('status.lastUpdate')}</h3>
                <p className="text-lg font-semibold">
                  {lastUpdate ? format(lastUpdate, 'HH:mm:ss') : '--'}
                </p>
              </div>
            </div>

            {/* Tabs Navigation */}
            <div className="mb-6">
              <div className="border-b border-gray-200">
                <nav className="-mb-px flex space-x-8" aria-label="Tabs">
                  <button
                    onClick={() => setActiveTab('data')}
                    className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'data'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                  >
                    {t('dashboard.dataTab')}
                  </button>
                  <button
                    onClick={() => setActiveTab('charts')}
                    className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'charts'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                  >
                    {t('dashboard.chartsTab')}
                  </button>
                  <button
                    onClick={() => setActiveTab('composition')}
                    className={`whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm ${activeTab === 'composition'
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                  >
                    {t('dashboard.compositionTab')}
                  </button>
                </nav>
              </div>
            </div>

            {/* Tab Content */}
            {activeTab === 'data' && (
              <>
                {/* Sensor Data Sections */}
                {sensorData ? (
                  <div className="space-y-6 mb-6">
                    {/* MH-Z19B Sensor Section */}
                    <div className="bg-white rounded-lg border border-gray-300 p-4">
                      <h3 className="text-lg font-semibold mb-4 text-blue-700">{t('sensors.mhzCo2')}</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="text-sm font-medium text-gray-700">{t('sensors.co2')}</h4>
                          <p className="text-2xl font-semibold text-gray-900">{formatSensorValue(sensorData.mhz19b_co2, 'ppm', 0)}</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="text-sm font-medium text-gray-700">{t('sensors.minCo2')}</h4>
                          <p className="text-2xl font-semibold text-gray-900">{formatSensorValue(sensorData.mhz19b_min_co2, 'ppm', 0)}</p>
                        </div>
                      </div>
                    </div>

                    {/* PMS7003 Sensor Section */}
                    <div className="bg-white rounded-lg border border-gray-300 p-4">
                      <h3 className="text-lg font-semibold mb-4 text-green-700">{t('sensors.pms7003')}</h3>

                      {/* PM Concentration Data */}
                      <div className="mb-4">
                        <h4 className="text-md font-medium text-gray-700 mb-3">{t('sensors.pmConcentration')}</h4>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="bg-gray-50 p-4 rounded-lg">
                            <h5 className="text-sm font-medium text-gray-700">{t('sensors.pm1')}</h5>
                            <p className="text-2xl font-semibold text-gray-900">{formatSensorValue(sensorData.pms7003_pm01, 'μg/m³', 0)}</p>
                          </div>
                          <div className="bg-gray-50 p-4 rounded-lg">
                            <h5 className="text-sm font-medium text-gray-700">{t('sensors.pm25')}</h5>
                            <p className="text-2xl font-semibold text-gray-900">{formatSensorValue(sensorData.pms7003_pm25, 'μg/m³', 0)}</p>
                          </div>
                          <div className="bg-gray-50 p-4 rounded-lg">
                            <h5 className="text-sm font-medium text-gray-700">{t('sensors.pm10')}</h5>
                            <p className="text-2xl font-semibold text-gray-900">{formatSensorValue(sensorData.pms7003_pm10, 'μg/m³', 0)}</p>
                          </div>
                        </div>
                      </div>

                      {/* Particle Count Data */}
                      <div>
                        <h4 className="text-md font-medium text-gray-700 mb-3">{t('sensors.particleCount')}</h4>
                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                          <div className="bg-gray-50 p-3 rounded-lg">
                            <h5 className="text-sm font-medium text-gray-700">{t('sensors.n0p3')}</h5>
                            <p className="text-lg font-semibold text-gray-900">{formatSensorValue(sensorData.pms7003_n0p3, '#/0.1L', 0)}</p>
                          </div>
                          <div className="bg-gray-50 p-3 rounded-lg">
                            <h5 className="text-sm font-medium text-gray-700">{t('sensors.n0p5')}</h5>
                            <p className="text-lg font-semibold text-gray-900">{formatSensorValue(sensorData.pms7003_n0p5, '#/0.1L', 0)}</p>
                          </div>
                          <div className="bg-gray-50 p-3 rounded-lg">
                            <h5 className="text-sm font-medium text-gray-700">{t('sensors.n1p0')}</h5>
                            <p className="text-lg font-semibold text-gray-900">{formatSensorValue(sensorData.pms7003_n1p0, '#/0.1L', 0)}</p>
                          </div>
                          <div className="bg-gray-50 p-3 rounded-lg">
                            <h5 className="text-sm font-medium text-gray-700">{t('sensors.n2p5')}</h5>
                            <p className="text-lg font-semibold text-gray-900">{formatSensorValue(sensorData.pms7003_n2p5, '#/0.1L', 0)}</p>
                          </div>
                          <div className="bg-gray-50 p-3 rounded-lg">
                            <h5 className="text-sm font-medium text-gray-700">{t('sensors.n5p0')}</h5>
                            <p className="text-lg font-semibold text-gray-900">{formatSensorValue(sensorData.pms7003_n5p0, '#/0.1L', 0)}</p>
                          </div>
                          <div className="bg-gray-50 p-3 rounded-lg">
                            <h5 className="text-sm font-medium text-gray-700">{t('sensors.n10p0')}</h5>
                            <p className="text-lg font-semibold text-gray-900">{formatSensorValue(sensorData.pms7003_n10p0, '#/0.1L', 0)}</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* BME280 Sensor Section */}
                    <div className="bg-white rounded-lg border border-gray-300 p-4">
                      <h3 className="text-lg font-semibold mb-4 text-purple-700">{t('sensors.bme280')}</h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="text-sm font-medium text-gray-700">{t('sensors.temperature')}</h4>
                          <p className="text-2xl font-semibold text-gray-900">{formatSensorValue(sensorData.bme280_temperature, '°C')}</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="text-sm font-medium text-gray-700">{t('sensors.humidity')}</h4>
                          <p className="text-2xl font-semibold text-gray-900">{formatSensorValue(sensorData.bme280_humidity, '%')}</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="text-sm font-medium text-gray-700">{t('sensors.pressure')}</h4>
                          <p className="text-2xl font-semibold text-gray-900">{formatSensorValue(sensorData.bme280_pressure, 'Pa', 0)}</p>
                        </div>
                      </div>
                    </div>

                    {/* Wind Speed Sensor Section */}
                    <div className="bg-white rounded-lg border border-gray-300 p-4">
                      <h3 className="text-lg font-semibold mb-4 text-cyan-700">{t('sensors.windSpeedSensor')}</h3>
                      <div className="grid grid-cols-1 gap-4">
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="text-sm font-medium text-gray-700">{t('sensors.windSpeed')}</h4>
                          <p className="text-2xl font-semibold text-gray-900">{formatSensorValue(sensorData.wind_speed, 'm/s')}</p>
                        </div>
                        {/* Wind direction removed as it's not supported by ESP32 */}
                      </div>
                    </div>

                    {/* UV Sensor (LTR390) Section */}
                    <div className="bg-white rounded-lg border border-gray-300 p-4">
                      <h3 className="text-lg font-semibold mb-4 text-yellow-700">{t('sensors.uvSensor')} (LTR390)</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="text-sm font-medium text-gray-700">{t('sensors.uvIndex')}</h4>
                          <p className="text-2xl font-semibold text-gray-900">{formatSensorValue(sensorData.ltr390_uvi, '', 1)}</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="text-sm font-medium text-gray-700">{t('sensors.lux')}</h4>
                          <p className="text-2xl font-semibold text-gray-900">{formatSensorValue(sensorData.ltr390_lux, ' lux', 0)}</p>
                        </div>
                      </div>
                    </div>

                    {/* GPS Sensor Section */}
                    <div className="bg-white rounded-lg border border-gray-300 p-4 relative">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-indigo-700">{t('sensors.gpsSensor')}</h3>
                        {/* Google Maps Button */}
                        {sensorData.gps_latitude && sensorData.gps_longitude && 
                         sensorData.gps_latitude !== null && sensorData.gps_longitude !== null && 
                         sensorData.gps_latitude !== 0 && sensorData.gps_longitude !== 0 && (
                          <button
                            type="button"
                            onClick={() => {
                              const lat = sensorData.gps_latitude;
                              const lng = sensorData.gps_longitude;
                              const googleMapsUrl = `https://www.google.com/maps?q=${lat},${lng}`;
                              window.open(googleMapsUrl, '_blank');
                            }}
                            className="inline-flex items-center gap-1.5 whitespace-nowrap py-2 px-3 text-tremor-default font-medium text-gray-500 hover:text-gray-800 transition-colors rounded-md hover:bg-gray-50"
                          >
                            <svg className="size-4 shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                            </svg>
                            {t('dashboard.openInMaps') || 'Open in Maps'}
                          </button>
                        )}
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="text-sm font-medium text-gray-700">{t('sensors.gpsLatitude')}</h4>
                          <p className="text-2xl font-semibold text-gray-900">{formatSensorValue(sensorData.gps_latitude, '°', 6)}</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="text-sm font-medium text-gray-700">{t('sensors.gpsLongitude')}</h4>
                          <p className="text-2xl font-semibold text-gray-900">{formatSensorValue(sensorData.gps_longitude, '°', 6)}</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="text-sm font-medium text-gray-700">{t('sensors.gpsAltitude')}</h4>
                          <p className="text-2xl font-semibold text-gray-900">{formatSensorValue(sensorData.gps_altitude, 'm')}</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="text-sm font-medium text-gray-700">{t('sensors.gpsQuality')}</h4>
                          <p className="text-2xl font-semibold text-gray-900">{formatSensorValue(sensorData.gps_quality, '', 0)}</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="text-sm font-medium text-gray-700">{t('sensors.gpsSatellites')}</h4>
                          <p className="text-2xl font-semibold text-gray-900">{formatSensorValue(sensorData.gps_satellites, '', 0)}</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-lg">
                          <h4 className="text-sm font-medium text-gray-700">{t('sensors.gpsAccuracy')}</h4>
                          <p className="text-2xl font-semibold text-gray-900">{formatSensorValue(sensorData.gps_accuracy, 'm', 1)}</p>
                        </div>
                        {/* GPS speed removed as it's not supported by ESP32 */}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="bg-white rounded-lg border border-gray-300 p-8 mb-6">
                    <div className="text-center text-gray-500">
                      {t('log.noSensorData')}
                    </div>
                  </div>
                )}
              </>
            )}

            {/* Charts Tab Content */}
            {activeTab === 'charts' && (
              <div className="space-y-6 mb-6">
                <div className="bg-white rounded-lg border border-gray-300 p-6">
                  <SensorCardChart 
                    title={t('dashboard.chartsTitle')}
                    description={t('dashboard.chartsDescription')}
                    currentData={sensorData}
                    historyData={dataHistory}
                  />
                </div>
              </div>
            )}

            {/* Composition Tab Content */}
            {activeTab === 'composition' && (
              <div className="space-y-6 mb-6">
                <div className="bg-white rounded-lg border border-gray-300 p-6">
                  <CompositionLineChart 
                    title={t('dashboard.compositionTitle') || "Sensor Data Composition"}
                    description={t('dashboard.compositionDescription') || "Multi-sensor data comparison and analysis"}
                    currentData={sensorData}
                    historyData={dataHistory}
                  />
                </div>
              </div>
            )}

          </>
        )}

        {/* System Log */}
        <div className="bg-white rounded-lg border border-gray-300 p-4">
          <h3 className="text-lg font-semibold mb-4">{t('log.title')}</h3>
          <div className="min-h-64 max-h-[900px] overflow-y-auto bg-gray-50 rounded p-3">
            {/* Combine system logs and device logs */}
            {(() => {
              // Combine and sort logs by timestamp (newest first)
              const combinedLogs = [...logs, ...deviceLogs].sort((a, b) => {
                const timestampA = new Date(a.timestamp);
                const timestampB = new Date(b.timestamp);
                return timestampB - timestampA;
              });

              return combinedLogs.map((log, index) => {
                // Define styles based on log type - only support message, error, warning
                const getLogStyles = (type) => {
                  switch (type) {
                    case 'error':
                      return {
                        leftBorder: 'border-l-4 border-l-red-500',
                        tagStyle: 'bg-red-100 text-red-700 ring-1 ring-red-200'
                      };
                    case 'warning':
                      return {
                        leftBorder: 'border-l-4 border-l-amber-500',
                        tagStyle: 'bg-amber-100 text-amber-700 ring-1 ring-amber-200'
                      };
                    case 'message':
                    default:
                      return {
                        leftBorder: 'border-l-4 border-l-gray-300',
                        tagStyle: 'bg-gray-100 text-gray-700 ring-1 ring-gray-200'
                      };
                  }
                };

                const styles = getLogStyles(log.type);

                return (
                  <div key={`${log.isDeviceLog ? 'device' : log.isSystemLog ? 'system' : 'unknown'}-${log.timestamp}-${index}`} className={`mb-2 pl-3 py-2 ${styles.leftBorder}`}>
                    <div className="flex items-center gap-2">
                      <span className="text-gray-500 font-medium text-sm min-w-[60px]">[{log.time}]</span>
                      <span className="text-gray-700 flex-1 text-sm">{log.message}</span>
                      {log.type && log.type !== 'message' && (
                        <span className={`px-2 py-1 rounded text-xs font-medium shrink-0 ${styles.tagStyle}`}>
                          {log.type.toUpperCase()}
                        </span>
                      )}
                    </div>
                    {log.data && (
                      <div className="mt-2 ml-16 p-3 bg-gray-100 rounded-md border border-gray-200 text-xs">
                        <pre className="whitespace-pre-wrap text-gray-600 font-mono">
                          {JSON.stringify(log.data, null, 2)}
                        </pre>
                      </div>
                    )}
                  </div>
                );
              });
            })()}
            {logs.length === 0 && deviceLogs.length === 0 && (
              <div className="text-center text-gray-500 py-8">
                {t('log.noLogs')}
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Dashboard; 