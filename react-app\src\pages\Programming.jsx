import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import * as Blockly from 'blockly';
import 'blockly/blocks'; // Import all built-in blocks including lists
import ArduinoGenerator from '../utils/blocklyArduinoGenerator';
import createModernTheme from '../components/blocks/blocklyTheme';
import { createToolboxConfig, createWorkspaceOptions, registerWorkspaceCallbacks } from '../components/blocks/blocklyToolbox';
import { initializeBlocks } from '../components/blocks/blocklyBlocks';
import { blocklyProgrammingStyles } from '../components/blocks/blocklyStyles';
import { 
  ensureSetupLoopBlocks, 
  addBlockCreationListener, 
  loadWorkspaceFromStorage, 
  clearWorkspace, 
  saveWorkspace,
  debounce,
  shouldAutoSave
} from '../components/blocks/blocklyWorkspace';
import Layout from '../components/Layout';

const Programming = () => {
  const { t, translations, currentLanguage } = useLanguage();
  const [isBlocklyLoaded, setIsBlocklyLoaded] = useState(false);
  const [generatedCode, setGeneratedCode] = useState('');
  const [loadingError, setLoadingError] = useState('');
  const blocklyDivRef = useRef(null);
  const workspaceRef = useRef(null);
  const [projectName, setProjectName] = useState('MyProject');
  const [jsonImportError, setJsonImportError] = useState('');
  
  // Simple save status tracking
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaveTime, setLastSaveTime] = useState(null);
  const [lastSaveType, setLastSaveType] = useState(null);
  const [relativeTimeDisplay, setRelativeTimeDisplay] = useState('');
  
  // Track the currently loaded project name
  const [loadedProjectName, setLoadedProjectName] = useState('MyProject');

  // Collapsible panel state
  const [isCodePanelCollapsed, setIsCodePanelCollapsed] = useState(false);
  
  // Resizable panel state
  const [panelWidth, setPanelWidth] = useState(384); // Default 384px (w-96)
  const [isResizing, setIsResizing] = useState(false);
  const [startX, setStartX] = useState(0);
  const [startWidth, setStartWidth] = useState(0);
  
  // Panel width constraints
  const MIN_PANEL_WIDTH = 280;
  const MAX_PANEL_WIDTH = 600;
  const COLLAPSED_WIDTH = 48;

  // Refs for stable container management
  const workspaceContainerRef = useRef(null);
  const sidebarContainerRef = useRef(null);
  const resizeObserverRef = useRef(null);
  const resizeTimeoutRef = useRef(null);

  // Warning tracking state
  const [hasWarnings, setHasWarnings] = useState(false);
  const [warningCount, setWarningCount] = useState(0);
  
  // Use ref to track current project name to avoid dependency issues
  const projectNameRef = useRef(projectName);
  
  // Update ref when projectName changes
  useEffect(() => {
    projectNameRef.current = projectName;
  }, [projectName]);

  // Format relative time like Google Docs
  const formatRelativeTime = useCallback((timestamp) => {
    if (!timestamp) return '';
    
    const now = new Date();
    const then = new Date(timestamp);
    const diffInSeconds = Math.floor((now - then) / 1000);
    
    if (diffInSeconds < 10) {
      return t('programming.justNow') || 'just now';
    } else if (diffInSeconds < 60) {
      const key = t('programming.secondsAgo');
      return typeof key === 'function' ? key(diffInSeconds) : `${diffInSeconds} seconds ago`;
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      if (minutes === 1) {
        return t('programming.oneMinuteAgo') || '1 minute ago';
      } else {
        const key = t('programming.minutesAgo');
        return typeof key === 'function' ? key(minutes) : `${minutes} minutes ago`;
      }
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      if (hours === 1) {
        return t('programming.oneHourAgo') || '1 hour ago';
      } else {
        const key = t('programming.hoursAgo');
        return typeof key === 'function' ? key(hours) : `${hours} hours ago`;
      }
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      if (days === 1) {
        return t('programming.oneDayAgo') || '1 day ago';
      } else {
        const key = t('programming.daysAgo');
        return typeof key === 'function' ? key(days) : `${days} days ago`;
      }
    }
  }, [t]);

  // Update relative time display every 10 seconds
  useEffect(() => {
    if (!lastSaveTime) {
      setRelativeTimeDisplay('');
      return;
    }

    const updateRelativeTime = () => {
      setRelativeTimeDisplay(formatRelativeTime(lastSaveTime));
    };

    // Update immediately
    updateRelativeTime();

    // Set up interval to update every 10 seconds
    const interval = setInterval(updateRelativeTime, 10000);

    return () => clearInterval(interval);
  }, [lastSaveTime, formatRelativeTime]);

  // Function to check for warnings in workspace
  const checkWorkspaceWarnings = useCallback(() => {
    if (!workspaceRef.current) return;
    
    try {
      const blocks = workspaceRef.current.getAllBlocks();
      const warnings = [];
      
      for (let i = 0; i < blocks.length; i++) {
        const block = blocks[i];
        
        // Check for warnings using the icon system (Blockly v12+ with react-blockly v9+)
        if (block.getIcon && typeof block.getIcon === 'function') {
          try {
            const warningIcon = block.getIcon('warning');
            if (warningIcon && warningIcon.getText && typeof warningIcon.getText === 'function') {
              const warningText = warningIcon.getText();
              if (warningText && warningText.trim()) {
                warnings.push({
                  blockId: block.id,
                  blockType: block.type,
                  warningText: warningText.trim()
                });
              }
            }
          } catch {
            // Ignore errors from icon access
          }
        }
      }
      
      const warningsExist = warnings.length > 0;
      setHasWarnings(warningsExist);
      setWarningCount(warnings.length);
      
      return warnings;
    } catch (error) {
      console.warn('Error checking workspace warnings:', error);
      setHasWarnings(false);
      setWarningCount(0);
      return [];
    }
  }, []);

  // Stable resize handler with throttling
  const handleBlocklyResize = useCallback(() => {
    if (resizeTimeoutRef.current) {
      clearTimeout(resizeTimeoutRef.current);
    }
    
    resizeTimeoutRef.current = setTimeout(() => {
      if (workspaceRef.current && isBlocklyLoaded) {
        try {
          Blockly.svgResize(workspaceRef.current);
        } catch (error) {
          console.warn('Blockly resize error:', error);
        }
      }
    }, 50); // Throttle to 50ms for smooth resizing
  }, [isBlocklyLoaded]);

  // Setup ResizeObserver for workspace container
  useEffect(() => {
    if (!workspaceContainerRef.current || !isBlocklyLoaded) return;

    resizeObserverRef.current = new ResizeObserver((entries) => {
      for (const entry of entries) {
        if (entry.target === workspaceContainerRef.current) {
          handleBlocklyResize();
        }
      }
    });

    resizeObserverRef.current.observe(workspaceContainerRef.current);

    return () => {
      if (resizeObserverRef.current) {
        resizeObserverRef.current.disconnect();
      }
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
    };
  }, [isBlocklyLoaded, handleBlocklyResize]);

  // Simple save function
  const performSave = useCallback(async (isAutoSave = false) => {
    if (!workspaceRef.current) return false;
    
    setIsSaving(true);
    
    try {
      // Use ref to get current project name instead of depending on it
      const success = saveWorkspace(workspaceRef.current, projectNameRef.current);
      if (success) {
        setLoadedProjectName(projectNameRef.current);
        setLastSaveTime(new Date());
        setLastSaveType(isAutoSave ? 'auto' : 'manual');
      }
      return success;
    } catch (error) {
      console.error('Save failed:', error);
      return false;
    } finally {
      // Show saving indicator for at least 300ms
      setTimeout(() => setIsSaving(false), 300);
    }
  }, []);

  // Debounced auto save function
  const debouncedAutoSave = useCallback(
    debounce(() => performSave(true), 2000),
    [performSave]
  );

  // Handle workspace changes for auto save
  const handleWorkspaceChange = useCallback((event) => {
    // Generate code
    try {
      const code = ArduinoGenerator.workspaceToCode(workspaceRef.current);
      setGeneratedCode(code || `// ${t('programming.dragHint')}`);
    } catch (error) {
      setGeneratedCode(`// Code generation error: ${error.message}`);
    }
    
    // Check for warnings
    checkWorkspaceWarnings();
    
    // Trigger auto save if this is a meaningful change
    if (shouldAutoSave(event)) {
      debouncedAutoSave();
    }
  }, [t, checkWorkspaceWarnings, debouncedAutoSave]);

  // Initialize Blockly
  useEffect(() => {
    const initializeBlockly = async () => {
      if (!blocklyDivRef.current) return;

      try {
        // Setup Blockly internationalization
        const blocklyTranslations = translations[currentLanguage]?.blockly;
        if (blocklyTranslations && typeof blocklyTranslations === 'object') {
          Object.keys(blocklyTranslations).forEach(key => {
            Blockly.Msg[key] = blocklyTranslations[key];
          });
        }

        // Initialize all custom blocks
        initializeBlocks(t);

        const modernTheme = createModernTheme();
        if (!blocklyDivRef.current) {
          setLoadingError(t('programming.blocklyContainerNotFound') || 'Blockly container not found');
          return;
        }
        
        const toolbox = createToolboxConfig(t);
        const options = createWorkspaceOptions(toolbox, modernTheme);
        workspaceRef.current = Blockly.inject(blocklyDivRef.current, options);
        
        // Register workspace button callbacks
        registerWorkspaceCallbacks(workspaceRef.current);
        
        // Add change listeners
        addBlockCreationListener(workspaceRef.current);
        
        // Add undeletable properties listener
        const updateUndeletableProperties = () => {
          ensureSetupLoopBlocks(workspaceRef.current);
        };
        workspaceRef.current.addChangeListener(updateUndeletableProperties);

        // Add main change listener for code generation and auto save
        workspaceRef.current.addChangeListener(handleWorkspaceChange);

        // Load workspace from storage
        loadWorkspaceFromStorage(workspaceRef.current, setProjectName, setLoadedProjectName);
        
        // Initial code generation and warning check
        const code = ArduinoGenerator.workspaceToCode(workspaceRef.current);
        setGeneratedCode(code || `// ${t('programming.dragHint')}`);
        checkWorkspaceWarnings();
        
        setIsBlocklyLoaded(true);

      } catch (error) {
        console.error('Failed to load Blockly:', error);
        setLoadingError('Blockly loading failed: ' + error.message);
      }
    };

    const timer = setTimeout(initializeBlockly, 100);
    
    return () => {
      clearTimeout(timer);
      if (workspaceRef.current) {
        workspaceRef.current.dispose();
      }
    };
  }, [t, translations, currentLanguage, handleWorkspaceChange, checkWorkspaceWarnings]);

  // Auto save when project name changes
  useEffect(() => {
    if (!isBlocklyLoaded || projectName === loadedProjectName) return;
    
    const timeoutId = setTimeout(() => {
      performSave(true); // auto-save
    }, 500);
    
    return () => clearTimeout(timeoutId);
  }, [projectName, loadedProjectName, isBlocklyLoaded, performSave]);

  // Save as .ino file
  const handleSaveIno = () => {
    // Check for workspace availability
    if (!workspaceRef.current) {
      alert(t('programming.noWorkspaceError', 'No workspace available'));
      return;
    }

    // Proceed with export
    const blob = new Blob([generatedCode], { type: 'text/x-arduino' });
    const a = document.createElement('a');
    a.href = URL.createObjectURL(blob);
    a.download = (projectNameRef.current.trim() || 'MyProject') + '.ino';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  // Save workspace as JSON
  const handleSaveJson = () => {
    if (!workspaceRef.current) return;
    const json = Blockly.serialization.workspaces.save(workspaceRef.current);
    const jsonString = JSON.stringify(json, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    const a = document.createElement('a');
    a.href = URL.createObjectURL(blob);
    a.download = (projectNameRef.current.trim() || 'MyProject') + '.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  // Import workspace from JSON
  const handleImportJson = async (e) => {
    setJsonImportError('');
    const file = e.target.files[0];
    if (!file) return;
    if (!file.name.endsWith('.json')) {
      setJsonImportError(t('programming.fileTypeNotSupported'));
      return;
    }
    
    const reader = new FileReader();
    reader.onload = async (event) => {
      try {
        const jsonText = event.target.result;
        const json = JSON.parse(jsonText);
        
        // Dispose current workspace
        if (workspaceRef.current) {
          workspaceRef.current.dispose();
        }
        
        // Re-import theme and toolbox
        const modernTheme = createModernTheme();
        const toolbox = createToolboxConfig(t);
        const options = createWorkspaceOptions(toolbox, modernTheme);
        
        // Create new workspace
        workspaceRef.current = Blockly.inject(blocklyDivRef.current, options);
        Blockly.serialization.workspaces.load(json, workspaceRef.current);
        
        // Set project name from file name
        const importedProjectName = file.name.replace('.json', '');
        setProjectName(importedProjectName);
        setLoadedProjectName(importedProjectName);
        console.log(`Imported project: ${importedProjectName}`);
        
        // Re-attach all listeners
        addBlockCreationListener(workspaceRef.current);
        
        const updateUndeletableProperties = () => {
          ensureSetupLoopBlocks(workspaceRef.current);
        };
        workspaceRef.current.addChangeListener(updateUndeletableProperties);
        workspaceRef.current.addChangeListener(handleWorkspaceChange);
        
        // Initial code generation and warning check
        const code = ArduinoGenerator.workspaceToCode(workspaceRef.current);
        setGeneratedCode(code || `// ${t('programming.dragHint')}`);
        checkWorkspaceWarnings();
        
        setIsBlocklyLoaded(true);
      } catch (err) {
        setJsonImportError(t('programming.failedToImportProject') + ' ' + err.message);
      }
    };
    reader.readAsText(file);
  };

  // Clear workspace function
  const handleClearWorkspace = () => {
    clearWorkspace(workspaceRef.current, setProjectName, setLoadedProjectName);
    
    // Update generated code and check warnings
    const code = ArduinoGenerator.workspaceToCode(workspaceRef.current);
    setGeneratedCode(code || `// ${t('programming.dragHint')}`);
    checkWorkspaceWarnings();
  };

  // Manual save function
  const handleManualSave = async () => {
    await performSave(false);
  };

  // Toggle code panel collapse
  const toggleCodePanel = () => {
    setIsCodePanelCollapsed(!isCodePanelCollapsed);
    // Trigger resize after panel state change
    setTimeout(handleBlocklyResize, 350); // Slightly longer than CSS transition
  };

  // Handle resize start
  const handleResizeStart = (e) => {
    if (isCodePanelCollapsed) return;
    
    setIsResizing(true);
    setStartX(e.clientX);
    setStartWidth(panelWidth);
    
    // Prevent text selection during resize
    document.body.style.userSelect = 'none';
    document.body.style.cursor = 'col-resize';
  };

  // Handle resize move with throttling
  const handleResizeMove = useCallback((e) => {
    if (!isResizing) return;
    
    const deltaX = startX - e.clientX; // Reverse direction since we're resizing from left edge
    const newWidth = Math.min(Math.max(startWidth + deltaX, MIN_PANEL_WIDTH), MAX_PANEL_WIDTH);
    
    setPanelWidth(newWidth);
    
    // Immediate resize for smooth interaction
    handleBlocklyResize();
  }, [isResizing, startX, startWidth, MIN_PANEL_WIDTH, MAX_PANEL_WIDTH, handleBlocklyResize]);

  // Handle resize end
  const handleResizeEnd = useCallback(() => {
    if (!isResizing) return;
    
    setIsResizing(false);
    document.body.style.userSelect = '';
    document.body.style.cursor = '';
    
    // Final resize after end
    setTimeout(handleBlocklyResize, 100);
  }, [isResizing, handleBlocklyResize]);

  // Add mouse event listeners for resizing
  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleResizeMove);
      document.addEventListener('mouseup', handleResizeEnd);
      
      return () => {
        document.removeEventListener('mousemove', handleResizeMove);
        document.removeEventListener('mouseup', handleResizeEnd);
      };
    }
  }, [isResizing, handleResizeMove, handleResizeEnd]);

  // Handle window resize for overall responsiveness
  useEffect(() => {
    const handleWindowResize = () => {
      handleBlocklyResize();
    };

    window.addEventListener('resize', handleWindowResize);
    return () => window.removeEventListener('resize', handleWindowResize);
  }, [handleBlocklyResize]);

  return (
    <Layout>
      <style>{blocklyProgrammingStyles}</style>
      
      {/* Project Controls */}
      <div className="flex items-center justify-between px-6 py-3 bg-white border-b border-gray-200">
        <div className="flex items-center gap-4">
          <label className="font-semibold text-gray-700">{t('programming.projectName')}</label>
          <input
            type="text"
            className="border rounded px-2 py-1 w-48 focus:outline-none focus:ring focus:border-blue-400"
            value={projectName}
            onChange={e => setProjectName(e.target.value)}
            placeholder={t('programming.enterProjectName')}
          />
          <div className="relative">
            <button
              data-tooltip-target={hasWarnings ? "tooltip-ino-disabled" : undefined}
              className={`ml-2 px-3 py-1 rounded transition-all duration-200 ${
                hasWarnings 
                  ? 'bg-gray-400 text-gray-700 cursor-not-allowed'
                  : 'bg-blue-600 text-white hover:bg-blue-700 hover:shadow-md'
              }`}
              onClick={hasWarnings ? undefined : handleSaveIno}
              disabled={hasWarnings}
              title={!hasWarnings ? t('programming.downloadInoTooltip', 'Download Arduino (.ino) file') : undefined}
              aria-label={t('programming.saveAsInoAriaLabel', 'Save as Arduino file')}
            >
              {t('programming.saveAsIno')}
            </button>
            {hasWarnings && (
              <div id="tooltip-ino-disabled" role="tooltip" className="absolute z-10 invisible inline-block px-3 py-2 text-sm font-medium text-white transition-opacity duration-300 bg-gray-900 rounded-lg shadow-sm opacity-0 tooltip -top-12 left-1/2 transform -translate-x-1/2 whitespace-nowrap">
                {t('programming.exportDisabledWarning', `Cannot export with ${warningCount} warning(s) in workspace`)}
                <div className="tooltip-arrow" data-popper-arrow></div>
              </div>
            )}
          </div>
          <button
            className="ml-2 px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition"
            onClick={handleSaveJson}
            title={t('programming.downloadJsonTooltip')}
          >
            {t('programming.saveAsJson')}
          </button>
          <label className="ml-2 px-3 py-1 bg-gray-600 text-white rounded hover:bg-gray-700 transition cursor-pointer" title={t('programming.importJsonTooltip')}>
            {t('programming.importJson')}
            <input
              type="file"
              accept=".json"
              onChange={handleImportJson}
              className="hidden"
            />
          </label>
          {jsonImportError && <span className="ml-2 text-red-600 text-sm">{jsonImportError}</span>}
        </div>
        
        {/* Right side buttons */}
        <div className="flex items-center gap-3">
          {/* Save Status */}
          {isSaving ? (
            <div className="flex items-center gap-2 text-sm text-yellow-600">
              <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span>{t('programming.saving') || 'Saving...'}</span>
            </div>
          ) : relativeTimeDisplay && (
            <div className="flex items-center gap-2 text-sm">
              <svg className={`w-4 h-4 ${lastSaveType === 'auto' ? 'text-green-600' : 'text-blue-600'}`} fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <span className="text-gray-600">
                {lastSaveType === 'auto' ? t('programming.autoSavedAt') || 'Auto-saved' : t('programming.manuallySaved') || 'Saved'} {relativeTimeDisplay}
              </span>
            </div>
          )}
          
          <button
            className="px-3 py-1 bg-yellow-600 hover:bg-yellow-700 text-white rounded transition"
            onClick={handleManualSave}
            title={t('programming.manualSaveTooltip') || 'Save workspace to local storage'}
          >
            {t('programming.manualSave') || 'Save'}
          </button>
          <button
            className="px-3 py-1 bg-red-600 text-white rounded hover:bg-red-700 transition"
            onClick={handleClearWorkspace}
            title={t('programming.clearWorkspaceTooltip') || 'Clear Workspace'}
          >
            {t('programming.clearWorkspace') || 'Clear'}
          </button>
        </div>
      </div>
      
      {/* Main Content Container - Stable Layout Wrapper */}
      <div className="flex h-[calc(100vh-130px)]">
        {/* Workspace Container */}
        <div 
          ref={workspaceContainerRef}
          className="flex-1 flex flex-col transition-all duration-300 ease-in-out"
          style={{ 
            width: isCodePanelCollapsed 
              ? `calc(100% - ${COLLAPSED_WIDTH}px)` 
              : `calc(100% - ${panelWidth}px)` 
          }}
        >
          {/* Blockly Workspace */}
          <div className="flex-1 relative bg-gray-50 overflow-hidden">
            {!isBlocklyLoaded && (
              <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-90 z-10">
                <div className="text-center">
                  <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-4"></div>
                  <h2 className="text-2xl font-bold text-gray-700 mb-2">
                    {t('programming.title')}
                  </h2>
                  {loadingError ? (
                    <div className="text-red-600">
                      <p className="text-lg font-medium">{t('programming.loadingFailed')}</p>
                      <p className="text-sm">{loadingError}</p>
                    </div>
                  ) : (
                    <div>
                      <p className="text-gray-500">
                        {t('programming.loadingInterface')}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}
            <div
              ref={blocklyDivRef}
              className="w-full h-full"
              style={{ minHeight: '400px' }}
            />
          </div>
        </div>

        {/* Sidebar Container - Right Panel */}
        <div 
          ref={sidebarContainerRef}
          className="bg-white border-l border-gray-200 flex flex-col shadow-lg transition-all duration-300 ease-in-out relative"
          style={{ 
            width: isCodePanelCollapsed ? `${COLLAPSED_WIDTH}px` : `${panelWidth}px`,
            minWidth: isCodePanelCollapsed ? `${COLLAPSED_WIDTH}px` : `${MIN_PANEL_WIDTH}px`,
            maxWidth: isCodePanelCollapsed ? `${COLLAPSED_WIDTH}px` : `${MAX_PANEL_WIDTH}px`
          }}
        >
          {/* Resize Handle */}
          {!isCodePanelCollapsed && (
            <div
              className="absolute left-0 top-0 w-1 h-full cursor-col-resize hover:bg-blue-500 hover:bg-opacity-50 active:bg-blue-500 transition-colors group"
              onMouseDown={handleResizeStart}
              title={t('programming.dragToResize')}
            >
              <div className="absolute left-0 top-1/2 transform -translate-y-1/2 -translate-x-1/2 w-3 h-8 bg-gray-300 rounded-full opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                <div className="w-0.5 h-4 bg-gray-500 rounded-full"></div>
              </div>
            </div>
          )}
          {/* Panel Header */}
          <div className="p-4 border-b border-gray-200 bg-gray-50 flex items-center justify-between">
            {!isCodePanelCollapsed ? (
              <>
                <div>
                  <h3 className="font-semibold text-gray-900">
                    {t('programming.generatedCode')}
                  </h3>
                  <p className="text-xs text-gray-500">{t('programming.jsCodeOutput')}</p>
                </div>
                <button
                  onClick={toggleCodePanel}
                  className="ml-2 p-1 rounded border border-gray-300 hover:bg-gray-100 transition-colors"
                  title={t('programming.collapseCodePanel')}
                >
                  <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </>
            ) : (
              <div className="w-full flex flex-col items-center">
                <button
                  onClick={toggleCodePanel}
                  className="p-1 rounded border border-gray-300 hover:bg-gray-100 transition-colors mb-2"
                  title={t('programming.expandCodePanel')}
                >
                  <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
              </div>
            )}
          </div>
          
          {/* Panel Content */}
          {!isCodePanelCollapsed ? (
            <div className="flex-1 overflow-hidden">
              <pre className="p-4 text-xs font-mono bg-gray-900 text-green-400 h-full overflow-auto modern-scrollbar whitespace-pre-wrap leading-relaxed">
                {generatedCode || `// ${t('programming.dragHint')}\n// ${t('programming.dragSubHint')}`}
              </pre>
            </div>
          ) : (
            <div className="flex-1 flex flex-col items-center justify-center text-center px-1">
              <div className="transform -rotate-90 whitespace-nowrap text-xs text-gray-600 font-medium">
                {t('programming.generatedCodeCollapsed')}
              </div>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default Programming; 