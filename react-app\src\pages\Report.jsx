import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import Layout from '../components/Layout';
import axios from 'axios';
import { format } from 'date-fns';
import { getApiUrl } from '../config/api';
import { Menu, MenuButton, MenuItems } from '@headlessui/react';
import { ChevronDownIcon } from '@heroicons/react/20/solid';

// Column configuration for the table (updated for prefixed field names)
const columnGroups = [
  {
    id: 'basic',
    titleKey: 'report.table.basicInfo',
    columns: [
      { id: 'timeDevice', key: 'report.table.timeDevice', field: 'timestamp', required: true }
    ]
  },
  {
    id: 'mhz19b',
    titleKey: 'report.table.mhz19bSensor',
    columns: [
      { id: 'co2', key: 'report.table.co2Ppm', field: 'mhz19b_co2' },
      { id: 'minCo2', key: 'report.table.minCo2Ppm', field: 'mhz19b_min_co2' }
    ]
  },
  {
    id: 'pms7003',
    titleKey: 'report.table.pms7003Sensor',
    columns: [
      { id: 'pm1', key: 'report.table.pm1', field: 'pms7003_pm01' },
      { id: 'pm25', key: 'report.table.pm25', field: 'pms7003_pm25' },
      { id: 'pm10', key: 'report.table.pm10', field: 'pms7003_pm10' }
    ]
  },
  {
    id: 'particles',
    titleKey: 'report.table.pms7003ParticleCount',
    columns: [
      { id: 'n0p3', key: 'report.table.n0p3', field: 'pms7003_n0p3' },
      { id: 'n0p5', key: 'report.table.n0p5', field: 'pms7003_n0p5' },
      { id: 'n1p0', key: 'report.table.n1p0', field: 'pms7003_n1p0' },
      { id: 'n2p5', key: 'report.table.n2p5', field: 'pms7003_n2p5' },
      { id: 'n5p0', key: 'report.table.n5p0', field: 'pms7003_n5p0' },
      { id: 'n10p0', key: 'report.table.n10p0', field: 'pms7003_n10p0' }
    ]
  },
  {
    id: 'bme280',
    titleKey: 'report.table.bme280Sensor',
    columns: [
      { id: 'temperature', key: 'report.table.temperature', field: 'bme280_temperature' },
      { id: 'humidity', key: 'report.table.humidity', field: 'bme280_humidity' },
      { id: 'pressure', key: 'report.table.pressure', field: 'bme280_pressure' }
    ]
  },
  {
    id: 'wind',
    titleKey: 'report.table.windSensor',
    columns: [
      { id: 'windSpeed', key: 'report.table.windSpeed', field: 'wind_speed' }
    ]
  },
  {
    id: 'ltr390',
    titleKey: 'report.table.uvSensor',
    columns: [
      { id: 'uvIndex', key: 'report.table.uvIndex', field: 'ltr390_uvi' },
      { id: 'uvLux', key: 'report.table.uvLux', field: 'ltr390_lux' }
    ]
  },
  {
    id: 'gps',
    titleKey: 'report.table.gpsSensor',
    columns: [
      { id: 'gpsLatitude', key: 'report.table.gpsLatitude', field: 'gps_latitude' },
      { id: 'gpsLongitude', key: 'report.table.gpsLongitude', field: 'gps_longitude' },
      { id: 'gpsAltitude', key: 'report.table.gpsAltitude', field: 'gps_altitude' },
      { id: 'gpsQuality', key: 'report.table.gpsQuality', field: 'gps_quality' },
      { id: 'gpsSatellites', key: 'report.table.gpsSatellites', field: 'gps_satellites' },
      { id: 'gpsAccuracy', key: 'report.table.gpsAccuracy', field: 'gps_accuracy' }
    ]
  }
];

// Get all columns flattened
const getAllColumns = () => {
  return columnGroups.reduce((acc, group) => [...acc, ...group.columns], []);
};

// Time Window Selector Component
const TimeWindowSelector = ({ timeWindow, onTimeWindowChange, t, disabled = false }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  const timeWindowOptions = [
    { value: 0, label: t('report.noAggregation') || 'No Aggregation' },
    { value: 50, label: '50ms' },
    { value: 100, label: '100ms' },
    { value: 200, label: '200ms' },
    { value: 500, label: '500ms' },
    { value: 1000, label: '1s' },
    { value: 5000, label: '5s' },
    { value: 10000, label: '10s' }
  ];

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleOptionSelect = (value) => {
    if (!disabled) {
      onTimeWindowChange(value);
      setIsOpen(false);
    }
  };

  const currentOption = timeWindowOptions.find(option => option.value === timeWindow);

  return (
    <div className="relative" ref={dropdownRef}>
      <div className="flex items-center gap-2">
        <span className="text-sm font-medium text-gray-700 whitespace-nowrap">
          {t('report.timeWindow')}:
        </span>
        <div className="relative">
          <button
            onClick={() => !disabled && setIsOpen(!isOpen)}
            disabled={disabled}
            className={`flex items-center justify-between w-40 px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              disabled 
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                : 'bg-white hover:bg-gray-50'
            }`}
          >
            <span className={disabled ? "text-gray-400" : "text-gray-900"}>
              {currentOption ? currentOption.label : (t('report.noAggregation') || '0ms (No Data Merging)')}
            </span>
            <svg className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''} ${disabled ? 'text-gray-400' : 'text-gray-600'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>

          {isOpen && !disabled && (
            <div className="absolute right-0 z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg overflow-hidden">
              {timeWindowOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={() => handleOptionSelect(option.value)}
                  className={`w-full px-3 py-2 text-left text-sm hover:bg-gray-100 ${
                    timeWindow === option.value ? 'bg-blue-50 text-blue-600' : 'text-gray-900'
                  }`}
                >
                  {option.label}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const Report = () => {
  const { t } = useLanguage();
  const [deviceData, setDeviceData] = useState({}); // { uuid: { data: [], total: 0, currentPage: 1, loading: false } }
  const [availableDevices, setAvailableDevices] = useState([]);
  const [expandedGroups, setExpandedGroups] = useState(new Set());
  const [isLoading, setIsLoading] = useState(true);
  const [selectedDeviceIds, setSelectedDeviceIds] = useState([]);
  const [pageSize] = useState(50);
  
  // Column selection state
  const [selectedColumns, setSelectedColumns] = useState(() => {
    // Default: select all columns
    return getAllColumns();
  });
  const [columnSearchTerm, setColumnSearchTerm] = useState('');
  
  // Device search state
  const [deviceSearchTerm, setDeviceSearchTerm] = useState('');
  
  // Time window configuration state
  const [timeWindow, setTimeWindow] = useState(0); // Default 0ms (no aggregation)

  // Fetch available devices using new grouped devices summary API
  const fetchAvailableDevices = useCallback(async () => {
    try {
      const response = await axios.get(getApiUrl('/grouped/devices/summary'));
      const devicesSummary = response.data || [];

      // Extract device IDs and store full device info
      const deviceIds = devicesSummary.map(device => device.uuid);
      setAvailableDevices(deviceIds);
      
      // Select all devices by default when the list is first loaded
      setSelectedDeviceIds(deviceIds);

      // Store device metadata for later use (total records, etc.)
      const deviceMetadata = {};
      devicesSummary.forEach(device => {
        deviceMetadata[device.uuid] = {
          totalRecords: device.totalRecords,
          lastUpdateTime: device.lastUpdateTime,
          availableSensors: device.availableSensors
        };
      });
      
      return { deviceIds, deviceMetadata };
    } catch (error) {
      console.error('Error fetching available devices:', error);
      setAvailableDevices([]);
      setSelectedDeviceIds([]);
      return { deviceIds: [], deviceMetadata: {} };
    }
  }, []);

  // Fetch paginated historical data for a single device using unified API
  const fetchDeviceHistoryPage = useCallback(async (uuid, page, pageSize) => {
    const offset = (page - 1) * pageSize;

    try {
      // Single API call replaces the previous 6 separate calls
      const response = await axios.get(getApiUrl(`/grouped/devices/${uuid}/history`), {
        params: {
          pageSize,
          offset,
          timeWindow // Add time window parameter for data aggregation
        }
      });
      console.log(response.data);

      const { data, pagination, metadata } = response.data;
      
      // Add uuid to each record for consistency with existing code
      const dataWithUuid = data.map(record => ({
        ...record,
        uuid
      }));

      return { 
        data: dataWithUuid, 
        hasMore: pagination.hasMore,
        total: pagination.total,
        metadata 
      };
    } catch (error) {
      console.error(`Error fetching device history for ${uuid}:`, error);
      return { data: [], hasMore: false, total: 0, metadata: {} };
    }
  }, [timeWindow]);

  // Fetch initial data (first page) for all available devices
  const fetchInitialData = useCallback(async () => {
    try {
      setIsLoading(true);
      const { deviceIds, deviceMetadata } = await fetchAvailableDevices();
      
      if (deviceIds.length === 0) {
        setIsLoading(false);
        return;
      }
      
      const deviceDataPromises = deviceIds.map(async (uuid) => {
        const { data, hasMore, total, metadata } = await fetchDeviceHistoryPage(uuid, 1, pageSize);
        return {
          uuid,
          data: data.map((row, index) => ({ ...row, id: `${uuid}-${index}` })),
          total: total || data.length, // Use total from API, fallback to data length
          currentPage: 1,
          loading: false,
          hasMore: hasMore,
          metadata: deviceMetadata[uuid] || {},
          apiMetadata: metadata || {}
        };
      });
      
      const deviceResults = await Promise.all(deviceDataPromises);
      const transformedData = {};
      deviceResults.forEach(result => {
        transformedData[result.uuid] = result;
      });
      
      setDeviceData(transformedData);
      
    } catch (error) {
      console.error('Error fetching initial data:', error);
      setDeviceData({});
    } finally {
      setIsLoading(false);
    }
  }, [fetchAvailableDevices, fetchDeviceHistoryPage, pageSize, timeWindow]);

  // Load more data for a specific device using the unified API
  const loadMoreDeviceData = async (uuid) => {
    const currentDevice = deviceData[uuid];
    if (!currentDevice || !currentDevice.hasMore || currentDevice.loading) {
      return;
    }

    try {
      setDeviceData(prev => ({ ...prev, [uuid]: { ...prev[uuid], loading: true } }));

      const nextPage = currentDevice.currentPage + 1;
      const { data: newData, hasMore, total } = await fetchDeviceHistoryPage(uuid, nextPage, pageSize);
      
      setDeviceData(prev => {
        const existingData = prev[uuid].data;
        const combinedData = [...existingData, ...newData.map((row, index) => ({
          ...row,
          id: `${uuid}-${existingData.length + index}`
        }))];

        return {
          ...prev,
          [uuid]: {
            ...prev[uuid],
            data: combinedData,
            total: total || combinedData.length, // Use total from API
            hasMore: hasMore,
            currentPage: nextPage,
            loading: false
          }
        };
      });
      
    } catch (error) {
      console.error('Error loading more data:', error);
      setDeviceData(prev => ({ ...prev, [uuid]: { ...prev[uuid], loading: false } }));
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchInitialData();
  }, [fetchInitialData]);

  // Handle device ID filter change
  const handleDeviceIdChange = (deviceId) => {
    setSelectedDeviceIds(prev => {
      if (prev.includes(deviceId)) {
        return prev.filter(id => id !== deviceId);
      } else {
        return [...prev, deviceId];
      }
    });
  };

  // Select all / Deselect all
  const handleSelectAll = () => {
    if (selectedDeviceIds.length === availableDevices.length) {
      setSelectedDeviceIds([]);
    } else {
      setSelectedDeviceIds([...availableDevices]);
    }
  };

  // Toggle group expansion
  const toggleGroupExpansion = (uuid) => {
    setExpandedGroups(prev => {
      const newSet = new Set(prev);
      if (newSet.has(uuid)) {
        newSet.delete(uuid);
      } else {
        newSet.add(uuid);
      }
      return newSet;
    });
  };

  // Format timestamp
  const formatTimestamp = (timestamp) => {
    try {
      return format(new Date(timestamp), 'yyyy-MM-dd HH:mm:ss.SSS');
    } catch {
      return 'Invalid Date';
    }
  };

  // Format sensor values
  const formatValue = (value, decimals = 1) => {
    if (value === null || value === undefined) return '--';
    if (typeof value === 'number') {
      return decimals > 0 ? value.toFixed(decimals) : value.toString();
    }
    return value.toString();
  };

  // Calculate average values for a device group
  const calculateAverages = (deviceRecords) => {
    if (deviceRecords.length === 0) return {};
    
    const totals = deviceRecords.reduce((acc, record) => {
      Object.keys(record).forEach(key => {
        if (typeof record[key] === 'number' && key !== 'id') {
          acc[key] = (acc[key] || 0) + record[key];
        }
      });
      return acc;
    }, {});
    
    const averages = {};
    Object.keys(totals).forEach(key => {
      averages[key] = totals[key] / deviceRecords.length;
    });
    
    return averages;
  };

  // Export to CSV (updated for prefixed field names)
  const handleExportCSV = async () => {
    if (selectedDeviceIds.length === 0) return;

    try {
      // Collect all data for selected devices
      const csvData = [];
      const headers = [
        'UUID', 'Timestamp',
        // MH-Z19B
        'CO2', 'MinCO2',
        // PMS7003 Concentration
        'PM1.0', 'PM2.5', 'PM10',
        // PMS7003 Particle Count
        'N0.3', 'N0.5', 'N1.0', 'N2.5', 'N5.0', 'N10.0',
        // BME280
        'Temperature', 'Humidity', 'Pressure',
        // Wind
        'WindSpeed',
        // LTR390 UV
        'UVIndex', 'UVLux',
        // GPS
        'GPSLatitude', 'GPSLongitude', 'GPSAltitude', 'GPSQuality', 'GPSSatellites', 'GPSAccuracy'
      ];

      csvData.push(headers.join(','));

      selectedDeviceIds.forEach(uuid => {
        const device = deviceData[uuid];
        if (device && device.data) {
          device.data.forEach(row => {
            const csvRow = [
              uuid,
              row.timestamp || '-',
              // MH-Z19B (prefixed)
              row.mhz19b_co2 || '-',
              row.mhz19b_min_co2 || '-',
              // PMS7003 Concentration (prefixed)
              row.pms7003_pm01 || '-',
              row.pms7003_pm25 || '-',
              row.pms7003_pm10 || '-',
              // PMS7003 Particle Count (prefixed)
              row.pms7003_n0p3 || '-',
              row.pms7003_n0p5 || '-',
              row.pms7003_n1p0 || '-',
              row.pms7003_n2p5 || '-',
              row.pms7003_n5p0 || '-',
              row.pms7003_n10p0 || '-',
              // BME280 (prefixed)
              row.bme280_temperature || '-',
              row.bme280_humidity || '-',
              row.bme280_pressure || '-',
              // Wind (prefixed)
              row.wind_speed || '-',
              // LTR390 UV (prefixed)
              row.ltr390_uvi || '-',
              row.ltr390_lux || '-',
              // GPS (prefixed)
              row.gps_latitude || '-',
              row.gps_longitude || '-',
              row.gps_altitude || '-',
              row.gps_quality || '-',
              row.gps_satellites || '-',
              row.gps_accuracy || '-'
            ];
            csvData.push(csvRow.join(','));
          });
        }
      });

      // Create and download CSV file
      const csvContent = csvData.join('\n');
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `sensor_report_${format(new Date(), 'yyyy-MM-dd_HH-mm-ss')}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

    } catch (error) {
      console.error('Error exporting CSV:', error);
    }
  };

  // Get filtered data for display
  const getFilteredDeviceData = () => {
    const filtered = {};
    selectedDeviceIds.forEach(uuid => {
      if (deviceData[uuid]) {
        filtered[uuid] = deviceData[uuid];
      }
    });
    return filtered;
  };

  // Get total records count for selected devices
  const getTotalRecordsCount = () => {
    return selectedDeviceIds.reduce((total, uuid) => {
      const device = deviceData[uuid];
      return total + (device ? device.data.length : 0);
    }, 0);
  };

  // Handle column selection
  const handleColumnToggle = (column) => {
    if (column.required) return; // Can't deselect required columns
    
    setSelectedColumns(prev => {
      const isSelected = prev.some(col => col.id === column.id);
      if (isSelected) {
        return prev.filter(col => col.id !== column.id);
      } else {
        return [...prev, column];
      }
    });
  };

  // Filter columns based on search term
  const getFilteredColumns = () => {
    const allColumns = getAllColumns();
    if (!columnSearchTerm) return allColumns;
    
    return allColumns.filter(column => {
      const translatedName = t(column.key);
      return translatedName.toLowerCase().includes(columnSearchTerm.toLowerCase()) ||
             column.field.toLowerCase().includes(columnSearchTerm.toLowerCase());
    });
  };

  // Get selected columns in proper order
  const getOrderedSelectedColumns = () => {
    const allColumns = getAllColumns();
    return selectedColumns.sort((a, b) => {
      const indexA = allColumns.findIndex(col => col.id === a.id);
      const indexB = allColumns.findIndex(col => col.id === b.id);
      return indexA - indexB;
    });
  };

  // Handle column select all/deselect all
  const handleColumnSelectAll = () => {
    const allColumns = getAllColumns();
    const requiredColumns = allColumns.filter(col => col.required);
    
    // Check if all columns are selected
    const allColumnsSelected = selectedColumns.length === allColumns.length;
    
    if (allColumnsSelected) {
      // Deselect all non-required columns, keep only required ones
      setSelectedColumns(requiredColumns);
    } else {
      // Select all columns
      setSelectedColumns(allColumns);
    }
  };

  // Handle group-level selection
  const handleGroupToggle = (group) => {
    const groupColumns = group.columns;
    const selectableGroupColumns = groupColumns.filter(col => !col.required);
    
    // Check if all selectable columns in this group are selected
    const allSelectableSelected = selectableGroupColumns.every(col => 
      selectedColumns.some(selectedCol => selectedCol.id === col.id)
    );
    
    if (allSelectableSelected && selectableGroupColumns.length > 0) {
      // Deselect all selectable columns in this group
      setSelectedColumns(prev => 
        prev.filter(col => !selectableGroupColumns.some(groupCol => groupCol.id === col.id))
      );
    } else {
      // Select all columns in this group
      const columnsToAdd = groupColumns.filter(col => 
        !selectedColumns.some(selectedCol => selectedCol.id === col.id)
      );
      setSelectedColumns(prev => [...prev, ...columnsToAdd]);
    }
  };

  // Get group selection state (none, partial, all)
  const getGroupSelectionState = (group) => {
    const groupColumns = group.columns;
    const selectableGroupColumns = groupColumns.filter(col => !col.required);
    const selectedSelectableColumns = selectedColumns.filter(col => 
      selectableGroupColumns.some(groupCol => groupCol.id === col.id)
    );
    
    if (selectableGroupColumns.length === 0) {
      // All columns in group are required
      return 'all';
    }
    
    if (selectedSelectableColumns.length === 0) {
      return 'none';
    } else if (selectedSelectableColumns.length === selectableGroupColumns.length) {
      return 'all';
    } else {
      return 'partial';
    }
  };

  // Filter devices based on search term
  const getFilteredDevices = () => {
    if (!deviceSearchTerm.trim()) {
      return availableDevices;
    }
    return availableDevices.filter(device =>
      device.toLowerCase().includes(deviceSearchTerm.toLowerCase())
    );
  };

  // Check if a row has any non-empty values in the selected columns (excluding timestamp)
  const isRowEmpty = (row) => {
    const nonTimestampColumns = getOrderedSelectedColumns().filter(col => col.field !== 'timestamp');
    return nonTimestampColumns.every(column => {
      const value = row[column.field];
      return value === null || value === undefined || value === '' || 
             (typeof value === 'number' && isNaN(value));
    });
  };

  // Filter out empty rows from device data
  const getFilteredDeviceDataWithoutEmptyRows = () => {
    const filteredData = getFilteredDeviceData();
    const result = {};
    
    Object.keys(filteredData).forEach(uuid => {
      const deviceInfo = filteredData[uuid];
      result[uuid] = {
        ...deviceInfo,
        data: deviceInfo.data.filter(row => !isRowEmpty(row))
      };
    });
    
    return result;
  };

  const filteredDeviceData = getFilteredDeviceDataWithoutEmptyRows();

  return (
    <Layout>
      <div className="mx-auto px-4 py-6 max-w-[1440px]">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{t('report.title')}</h1>
            <p className="text-gray-600">{t('report.subtitle')}</p>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={handleExportCSV}
              disabled={selectedDeviceIds.length === 0}
              className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
            >
              {t('report.exportCsv')} ({getTotalRecordsCount()})
            </button>
            <button
              onClick={fetchInitialData}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              {t('report.refresh')}
            </button>
          </div>
        </div>

        {/* Filter Controls */}
        <div className="flex items-center justify-between gap-4 mb-6">
          {/* Left side - Device and Column Filters */}
          <div className="flex items-center gap-4">
            {/* Device Filter Dropdown */}
            <Menu as="div" className="relative inline-block text-left">
          <div>
            <MenuButton className="inline-flex w-full justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
              {t('report.deviceFilter')} ({selectedDeviceIds.length})
              <ChevronDownIcon aria-hidden="true" className="-mr-1 size-5 text-gray-400" />
            </MenuButton>
          </div>

          <MenuItems
            transition
            className="absolute left-0 z-10 mt-2 w-64 origin-top-left divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in"
          >
            <div className="p-3">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-gray-700">{t('report.selectDevices')}</span>
                <button
                  onClick={handleSelectAll}
                  className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  {selectedDeviceIds.length === availableDevices.length ? 
                    t('report.deselectAll') : 
                    t('report.selectAll')
                  }
                </button>
              </div>

              {/* Search input */}
              <div className="mb-3">
                <input
                  type="text"
                  placeholder={t('report.searchDevices') || 'Search devices...'}
                  value={deviceSearchTerm}
                  onChange={(e) => setDeviceSearchTerm(e.target.value)}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2 text-sm text-gray-700 max-h-60 overflow-y-auto">
                {getFilteredDevices().length > 0 ? (
                  getFilteredDevices().map(uuid => {
                    return (
                      <div key={uuid} className="flex items-center py-1">
                        <input
                          id={`checkbox-${uuid}`}
                          type="checkbox"
                          checked={selectedDeviceIds.includes(uuid)}
                          onChange={() => handleDeviceIdChange(uuid)}
                          className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mr-2 flex-shrink-0"
                        />
                        <label htmlFor={`checkbox-${uuid}`} className="text-sm font-medium text-gray-900 truncate">
                          {uuid}
                        </label>
                      </div>
                    );
                  })
                ) : (
                  <div className="text-center text-gray-500 py-4">
                    {t('report.noDevicesFound') || `No devices found matching "${deviceSearchTerm}"`}
                  </div>
                )}
              </div>
            </div>
          </MenuItems>
        </Menu>

        {/* Column Filter Dropdown */}
        <Menu as="div" className="relative inline-block text-left">
          <div>
            <MenuButton className="inline-flex w-full justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
              {t('report.columnFilter') || 'Select Columns'} ({selectedColumns.length})
              <ChevronDownIcon aria-hidden="true" className="-mr-1 size-5 text-gray-400" />
            </MenuButton>
          </div>

          <MenuItems
            transition
            className="absolute left-0 z-10 mt-2 w-[32rem] origin-top-left divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in"
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-gray-700">
                  {t('report.selectColumns') || 'Select Columns'}
                </span>
                <button
                  onClick={handleColumnSelectAll}
                  className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  {selectedColumns.length === getAllColumns().length ? 
                    t('report.deselectAll') : 
                    t('report.selectAll')
                  }
                </button>
              </div>

              {/* Search input */}
              <div className="mb-3">
                <input
                  type="text"
                  placeholder={t('report.searchColumns') || 'Search columns...'}
                  value={columnSearchTerm}
                  onChange={(e) => setColumnSearchTerm(e.target.value)}
                  className="w-full px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>

              <div className="space-y-2 text-sm text-gray-700 max-h-96 overflow-y-auto">
                {/* Group columns by category */}
                {columnGroups.map(group => {
                  const groupColumns = getFilteredColumns().filter(col => 
                    group.columns.some(groupCol => groupCol.id === col.id)
                  );
                  if (groupColumns.length === 0) return null;

                  const groupSelectionState = getGroupSelectionState(group);
                  const selectableGroupColumns = group.columns.filter(col => !col.required);
                  const hasSelectableColumns = selectableGroupColumns.length > 0;

                  return (
                    <div key={group.id} className="mb-3">
                      <div className="flex items-center justify-between mb-2 border-b border-gray-200 pb-1">
                        <div className="text-xs font-semibold text-gray-500 uppercase tracking-wide">
                          {t(group.titleKey)}
                        </div>
                        {hasSelectableColumns && (
                          <button
                            onClick={() => handleGroupToggle(group)}
                            className="flex items-center text-xs text-blue-600 hover:text-blue-800 font-medium"
                          >
                            <div className="relative mr-1">
                              <input
                                type="checkbox"
                                checked={groupSelectionState === 'all'}
                                ref={(el) => {
                                  if (el) el.indeterminate = groupSelectionState === 'partial';
                                }}
                                onChange={() => {}} // Handled by button click
                                className="w-3 h-3 text-blue-600 border-gray-300 rounded focus:ring-blue-500 pointer-events-none"
                              />
                            </div>
                            {groupSelectionState === 'all' ? 
                              (t('report.deselectGroup') || 'Deselect All') : 
                              (t('report.selectGroup') || 'Select All')
                            }
                          </button>
                        )}
                      </div>
                      {groupColumns.map(column => {
                        const isSelected = selectedColumns.some(col => col.id === column.id);
                        const isRequired = column.required;
                        const translatedName = t(column.key);
                        return (
                          <div key={column.id} className={`flex items-center ${isRequired ? 'opacity-75' : ''}`}>
                            <input
                              id={`column-${column.id}`}
                              type="checkbox"
                              checked={isSelected}
                              onChange={() => handleColumnToggle(column)}
                              disabled={isRequired}
                              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 disabled:opacity-50"
                            />
                            <label htmlFor={`column-${column.id}`} className={`ml-2 text-sm font-medium ${isRequired ? 'text-gray-600' : 'text-gray-900'}`}>
                              {translatedName}
                            </label>
                          </div>
                        );
                      })}
                    </div>
                  );
                })}

                {getFilteredColumns().length === 0 && (
                  <div className="text-center text-gray-500 py-4">
                    {t('report.noColumnsFound') || `No columns found matching "${columnSearchTerm}"`}
                  </div>
                )}
              </div>
            </div>
          </MenuItems>
        </Menu>
          </div>

          {/* Right side - Time Window Selector */}
          <TimeWindowSelector 
            timeWindow={timeWindow}
            onTimeWindowChange={setTimeWindow}
            t={t}
          />
        </div>

        {/* Data Table */}
        <div className="relative bg-white rounded-lg border border-gray-200 overflow-hidden">
          {/* Loading Overlay */}
          {isLoading && (
            <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-10">
              <div className="flex flex-col items-center justify-center text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
                <p className="text-gray-600 font-medium">{t('report.loading')}</p>
                <p className="text-sm text-gray-500 mt-1">
                  {timeWindow === 0 ? 
                    t('report.loadingOriginalData') || 'Loading original data...' : 
                    `${t('report.loadingAggregatedData') || 'Loading aggregated data'} (${timeWindow}ms)`
                  }
                </p>
              </div>
            </div>
          )}

          <div className="overflow-x-auto max-h-[70vh] overflow-y-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                {/* Generate dynamic headers based on selected columns */}
                <tr>
                  {columnGroups.map(group => {
                    const visibleGroupColumns = selectedColumns.filter(col => 
                      group.columns.some(groupCol => groupCol.id === col.id)
                    );
                    if (visibleGroupColumns.length === 0) return null;
                    
                    const isFirst = group.id === 'basic';
                    return (
                      <th 
                        key={group.id}
                        colSpan={visibleGroupColumns.length} 
                        className={`px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap ${!isFirst ? 'border-l border-gray-200' : ''}`}
                      >
                        {t(group.titleKey)}
                      </th>
                    );
                  })}
                </tr>
                <tr className="bg-gray-100">
                  {getOrderedSelectedColumns().map(column => (
                    <th 
                      key={column.id}
                      className="px-3 py-2 text-left text-xs font-medium text-gray-700 whitespace-nowrap"
                    >
                      {t(column.key)}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {Object.keys(filteredDeviceData).length === 0 ? (
                  <tr>
                    <td colSpan={selectedColumns.length} className="px-6 py-12 text-gray-500">
                      {selectedDeviceIds.length === 0 
                        ? t('report.messages.noDeviceSelected')
                        : t('report.messages.noDataAvailable')
                      }
                    </td>
                  </tr>
                ) : (
                  Object.keys(filteredDeviceData).sort().map(uuid => {
                    const deviceInfo = filteredDeviceData[uuid];
                    const isExpanded = expandedGroups.has(uuid);
                    const averages = calculateAverages(deviceInfo.data);
                    
                    return (
                      <React.Fragment key={uuid}>
                        {/* Device Group Header */}
                        <tr 
                          className="cursor-pointer hover:bg-gray-50 sticky top-0 bg-white border-b border-gray-200"
                          style={{ zIndex: 1 }}
                          onClick={() => toggleGroupExpansion(uuid)}
                        >
                          {getOrderedSelectedColumns().map((column) => {
                            if (column.field === 'timestamp') {
                              return (
                                <td key={column.id} className="px-3 py-4 text-sm font-semibold whitespace-nowrap">
                                  <div className="flex items-center gap-2">
                                    <svg 
                                      className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-90' : ''}`} 
                                      fill="currentColor" 
                                      viewBox="0 0 20 20"
                                    >
                                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                                    </svg>
                                    <div className="text-gray-900 font-bold">{uuid}</div>
                                    <span className="text-xs text-gray-500">
                                      ({deviceInfo.data.length}/{deviceInfo.total || 0})
                                    </span>
                                  </div>
                                </td>
                              );
                            } else {
                              return (
                                <td key={column.id} className="px-3 py-4 text-sm text-gray-700 whitespace-nowrap">
                                  {formatValue(averages[column.field], 2)}
                                </td>
                              );
                            }
                          })}
                        </tr>
                        
                        {/* Device Records */}
                        {isExpanded && deviceInfo.data.map((row, index) => (
                          <tr key={row.id}>
                            {getOrderedSelectedColumns().map((column) => {
                              if (column.field === 'timestamp') {
                                return (
                                  <td key={column.id} className="px-3 py-3 text-sm whitespace-nowrap">
                                    <div className="flex items-center gap-2">
                                      <span className="text-gray-400 text-xs">#{index + 1}</span>
                                      <div className="text-gray-500 text-xs">{formatTimestamp(row.timestamp)}</div>
                                    </div>
                                  </td>
                                );
                              } else {
                                const decimals = ['temperature', 'humidity'].includes(column.field) ? 1 : 0;
                                return (
                                  <td key={column.id} className="px-3 py-3 text-sm text-gray-900 whitespace-nowrap">
                                    {formatValue(row[column.field], decimals)}
                                  </td>
                                );
                              }
                            })}
                          </tr>
                        ))}
                        
                        {/* Load More Row */}
                        {isExpanded && deviceInfo.hasMore && (
                          <tr 
                            onClick={() => loadMoreDeviceData(uuid)}
                            className={`${deviceInfo.loading ? 'cursor-not-allowed bg-gray-50' : 'cursor-pointer hover:bg-blue-50'} transition-colors`}
                          >
                            <td colSpan={selectedColumns.length} className="px-6 py-4 text-center">
                              <div className={`${deviceInfo.loading ? 'text-gray-400' : 'text-blue-600'} transition-colors`}>
                                {deviceInfo.loading ? (
                                  <div className="flex items-center justify-center gap-2">
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                                    {t('report.loadingMore')} - {uuid}
                                  </div>
                                ) : (
                                  <div className="flex items-center justify-center gap-2">
                                    <span>{t('report.loadMore')} - {uuid}</span>
                                  </div>
                                )}
                              </div>
                            </td>
                          </tr>
                        )}
                      </React.Fragment>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* Summary */}
        {Object.keys(filteredDeviceData).length > 0 && (
          <div className="mt-6 bg-gray-50 rounded-lg p-4">
            <div className="text-sm text-gray-600">
              {t('report.messages.showingRecords')} {getTotalRecordsCount()} {t('report.messages.recordsFrom')} {selectedDeviceIds.length} {t('report.messages.devices')}
              {selectedDeviceIds.length > 0 && (
                <span className="ml-2">
                  [{selectedDeviceIds.join(', ')}]
                </span>
              )}
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default Report; 