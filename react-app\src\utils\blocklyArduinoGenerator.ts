import * as Blockly from 'blockly';

// Create the Arduino generator instance directly
const ArduinoGenerator = new Blockly.Generator('Arduino') as any;

// Initialize properties for the Arduino generator
ArduinoGenerator.includes_ = Object.create(null);
ArduinoGenerator.definitions_ = Object.create(null);
ArduinoGenerator.variables_ = Object.create(null);
ArduinoGenerator.codeFunctions_ = Object.create(null);
ArduinoGenerator.userFunctions_ = Object.create(null);
ArduinoGenerator.functionNames_ = Object.create(null);
ArduinoGenerator.setups_ = Object.create(null);
ArduinoGenerator.pins_ = Object.create(null);

// Order of operations for Arduino
ArduinoGenerator.ORDER_ATOMIC = 0;         // 0 "" ...
ArduinoGenerator.ORDER_UNARY_POSTFIX = 1;  // expr++ expr-- () [] .
ArduinoGenerator.ORDER_UNARY_PREFIX = 2;   // -expr !expr ~expr ++expr --expr
ArduinoGenerator.ORDER_MULTIPLICATIVE = 3; // * / % ~/
ArduinoGenerator.ORDER_ADDITIVE = 4;       // + -
ArduinoGenerator.ORDER_SHIFT = 5;          // << >>
ArduinoGenerator.ORDER_RELATIONAL = 6;     // is is! >= > <= <
ArduinoGenerator.ORDER_EQUALITY = 7;       // == != === !==
ArduinoGenerator.ORDER_BITWISE_AND = 8;    // &
ArduinoGenerator.ORDER_BITWISE_XOR = 9;    // ^
ArduinoGenerator.ORDER_BITWISE_OR = 10;    // |
ArduinoGenerator.ORDER_LOGICAL_AND = 11;   // &&
ArduinoGenerator.ORDER_LOGICAL_OR = 12;    // ||
ArduinoGenerator.ORDER_CONDITIONAL = 13;   // expr ? expr : expr
ArduinoGenerator.ORDER_ASSIGNMENT = 14;    // = *= /= ~/= %= += -= <<= >>= &= ^= |=
ArduinoGenerator.ORDER_NONE = 99;          // (...)

/**
 * Gets the Arduino type from a block.
 * This function centralizes type detection by relying on block output checks
 * and variable types defined in Blockly's variable model.
 * @param {Blockly.Block | null} block The block to check.
 * @returns {string} The Arduino type (e.g., 'int', 'float', 'String', 'bool').
 */
const getArduinoType = (block: Blockly.Block | null): string => {
  if (!block) return 'void';

  // 1. For variable getters, the most reliable source is the variable's type.
  const variableField = block.getField('VAR');
  if (variableField && block.type.includes('variables_get')) {
    const variable = variableField.getVariable();
    if (variable && variable.type) {
      // The variable type from Blockly ('int', 'float', 'String') is what we need.
      return variable.type;
    }
  }

  // 2. For all other blocks, check the output connection type.
  if (block.outputConnection && block.outputConnection.check) {
    const check = block.outputConnection.check;
    
    // Check array types FIRST (before checking basic types)
    if (check.includes('String[]')) return 'String[]';
    if (check.includes('int[]')) return 'int[]';
    if (check.includes('float[]')) return 'float[]';
    if (check.includes('Array')) return 'Array'; // Generic array (for arrays of unknown type fallback)
    
    // Then check basic types
    if (check.includes('String')) return 'String';
    if (check.includes('float')) return 'float';
    if (check.includes('int')) return 'int';
    if (check.includes('Boolean')) return 'bool'; // Blockly uses 'Boolean' for its type check
  }
  
  // 3. Fallback for untyped or mis-typed blocks (should be minimal)
  switch (block.type) {
    case 'math_number':
      const num = block.getFieldValue('NUM');
      return String(num).includes('.') ? 'float' : 'int';
    case 'text':
      return 'String';
    case 'logic_boolean':
        return 'bool';
  }

  return 'int'; // Default fallback
};

ArduinoGenerator.scrubNakedValue = function(line: string) {
  return line + ';\n';
};

ArduinoGenerator.quote_ = function(string: string) {
  string = string.replace(/\\/g, '\\\\')
                 .replace(/\n/g, '\\n')
                 .replace(/\$/g, '\\$')
                 .replace(/'/g, '\\\'');
  return '"' + string + '"';
};

ArduinoGenerator.scrub_ = function(block: Blockly.Block, code: string, thisOnly?: boolean) {
  let commentCode = '';
  // Only collect comments for blocks that aren't inline.
  if (!block.outputConnection || !block.outputConnection.targetConnection) {
    // Collect comment for this block.
    let comment = block.getCommentText();
    if (comment) {
      comment = Blockly.utils.string.wrap(comment, (this.COMMENT_WRAP || 80) - 3);
      commentCode += this.prefixLines(comment + '\n', '// ');
    }
    // Collect comments for all value arguments.
    // Don't collect comments for nested statements.
    for (let i = 0; i < block.inputList.length; i++) {
      if (block.inputList[i].type === 1) {
        const childBlock = block.inputList[i].connection!.targetBlock();
        if (childBlock) {
          comment = this.allNestedComments(childBlock);
          if (comment) {
            commentCode += this.prefixLines(comment, '// ');
          }
        }
      }
    }
  }
  const nextBlock = block.nextConnection && block.nextConnection.targetBlock();
  const nextCode = thisOnly ? '' : this.blockToCode(nextBlock);
  return commentCode + code + nextCode;
};

// --- Add your custom block generators here ---

// WiFi Setup Block
ArduinoGenerator['wifi_setup'] = function(block: Blockly.Block): string {
  const ssid = block.getFieldValue('SSID') || 'SSID';
  const password = block.getFieldValue('PASSWORD') || 'PASSWORD';
  // Call setWiFi() with parameters directly
  return `setWiFi("${ssid}", "${password}");\n`;
};

// Setup and Loop containers
ArduinoGenerator['arduino_setup'] = function(block: Blockly.Block): string {
  const statements = this.statementToCode(block, 'SETUP_BODY');
  // The setup code from Arduino_Controller.ino is now handled in workspaceToCode
  // Only add user-defined setup statements here
  return statements;
};

ArduinoGenerator['arduino_loop'] = function(block: Blockly.Block): string {
  const statements = this.statementToCode(block, 'LOOP_BODY');
  return statements;
};

// Sensor Read Value Blocks (output: String)
ArduinoGenerator['read_bme_temp'] = function() {
  return ['readBMESensorTemperature()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['read_bme_humidity'] = function() {
  return ['readBMESensorHumidity()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['read_bme_pressure'] = function() {
  return ['readBMESensorPressure()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['read_pms_pm1_0'] = function() {
  return ['readPMS7003SensorPM1_0()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['read_pms_pm2_5'] = function() {
  return ['readPMS7003SensorPM2_5()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['read_pms_pm10'] = function() {
  return ['readPMS7003SensorPM10()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['read_pms_n0_3'] = function() {
  return ['readPMS7003SensorN0_3()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['read_pms_n0_5'] = function() {
  return ['readPMS7003SensorN0_5()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['read_pms_n1_0'] = function() {
  return ['readPMS7003SensorN1_0()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['read_pms_n2_5'] = function() {
  return ['readPMS7003SensorN2_5()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['read_pms_n5_0'] = function() {
  return ['readPMS7003SensorN5_0()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['read_pms_n10_0'] = function() {
  return ['readPMS7003SensorN10_0()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['read_mhz_co2'] = function() {
  return ['readMHZ19BSensorCO2()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['read_mhz_min_co2'] = function() {
  return ['readMHZ19BSensorMinCO2()', ArduinoGenerator.ORDER_ATOMIC];
};

// Additional MHZ19B sensor functions
ArduinoGenerator['read_mhz_temperature'] = function() {
  return ['readMHZ19BSensorTemperature()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['read_mhz_accuracy'] = function() {
  return ['readMHZ19BSensorAccuracy()', ArduinoGenerator.ORDER_ATOMIC];
};

// Additional GPS sensor functions
ArduinoGenerator['read_gps_quality'] = function() {
  return ['readGPSQuality()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['read_gps_accuracy'] = function() {
  return ['readGPSAccuracy()', ArduinoGenerator.ORDER_ATOMIC];
};

// Device UUID function
ArduinoGenerator['get_device_uuid'] = function() {
  return ['getDeviceUUID()', ArduinoGenerator.ORDER_ATOMIC];
};




// Serial Print Block
ArduinoGenerator['serial_print'] = function(block: Blockly.Block) {
  const value = ArduinoGenerator.valueToCode(block, 'VALUE', ArduinoGenerator.ORDER_NONE) || '""';
  return 'COMPUTER_SERIAL.println(' + value + ');\n';
};

// === New Block Generators ===



// Sensor Status Blocks (return bool)
ArduinoGenerator['get_bme280_status'] = function() {
  return ['getBME280Status()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['get_pms7003_status'] = function() {
  return ['getPMS7003Status()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['get_mhz19b_status'] = function() {
  return ['getMHZ19BStatus()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['get_wind_speed_status'] = function() {
  return ['getWindSpeedStatus()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['get_uv_status'] = function() {
  return ['getUVStatus()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['get_gps_status'] = function() {
  return ['getGPSStatus()', ArduinoGenerator.ORDER_ATOMIC];
};

// New Sensor Reading Blocks (return String)
ArduinoGenerator['read_wind_speed'] = function() {
  return ['readWindSpeed()', ArduinoGenerator.ORDER_ATOMIC];
};
// Wind direction is not supported by ESP32 - removed
ArduinoGenerator['read_uv_index'] = function() {
  return ['readUVIndex()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['read_uv_intensity'] = function() {
  return ['readUVIntensity()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['read_gps_latitude'] = function() {
  return ['readGPSLatitude()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['read_gps_longitude'] = function() {
  return ['readGPSLongitude()', ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['read_gps_altitude'] = function() {
  return ['readGPSAltitude()', ArduinoGenerator.ORDER_ATOMIC];
};
// GPS speed is not supported by ESP32 - removed
ArduinoGenerator['read_gps_satellites'] = function() {
  return ['readGPSSatellites()', ArduinoGenerator.ORDER_ATOMIC];
};

// Send Log to Server Block
ArduinoGenerator['send_log_to_server'] = function(block: Blockly.Block) {
  const logType = block.getFieldValue('LOG_TYPE') || 'MESSAGE';
  const message = ArduinoGenerator.valueToCode(block, 'MESSAGE', ArduinoGenerator.ORDER_NONE) || '""';
  return `sendLogToServer("${logType}", ${message});\n`;
};

// Data Processing Blocks
ArduinoGenerator['array_aggregate'] = function(block: Blockly.Block) {
  const op = block.getFieldValue('OP') || 'SUM';
  const list = ArduinoGenerator.valueToCode(block, 'LIST', ArduinoGenerator.ORDER_NONE) || '{}';
  const functionName = {
    'SUM': 'getArraySum',
    'AVERAGE': 'getArrayAverage', 
    'MIN': 'getArrayMin',
    'MAX': 'getArrayMax'
  }[op] || 'getArraySum';
  
  // Generate code that calculates array size and calls the function
  const tempVar = ArduinoGenerator.nameDB_.getDistinctName('temp_array', Blockly.Names.DEVELOPER_VARIABLE_TYPE);
  const sizeVar = ArduinoGenerator.nameDB_.getDistinctName('array_size', Blockly.Names.DEVELOPER_VARIABLE_TYPE);
  
  const code = `([&]() {
  auto ${tempVar} = ${list};
  int ${sizeVar} = sizeof(${tempVar}) / sizeof(${tempVar}[0]);
  return ${functionName}(${tempVar}, ${sizeVar});
}())`;
  
  return [code, ArduinoGenerator.ORDER_ATOMIC];
};

// Type Conversion Blocks
ArduinoGenerator['convert_to_string'] = function(block: Blockly.Block) {
  const value = ArduinoGenerator.valueToCode(block, 'VALUE', ArduinoGenerator.ORDER_NONE) || '0';
  // Always wrap in String() constructor for consistent behavior
  return [`String(${value})`, ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['convert_to_int'] = function(block: Blockly.Block) {
  const value = ArduinoGenerator.valueToCode(block, 'VALUE', ArduinoGenerator.ORDER_NONE) || '0';
  const valueBlock = block.getInputTargetBlock('VALUE');
  const inputType = getArduinoType(valueBlock);
  // Generate C-style cast for numbers, and .toInt() for Strings.
  if (inputType === 'String') {
    return [`String(${value}).toInt()`, ArduinoGenerator.ORDER_ATOMIC];
  } else {
    return [`(int)(${value})`, ArduinoGenerator.ORDER_ATOMIC];
  }
};
ArduinoGenerator['convert_to_float'] = function(block: Blockly.Block) {
  const value = ArduinoGenerator.valueToCode(block, 'VALUE', ArduinoGenerator.ORDER_NONE) || '0';
  const valueBlock = block.getInputTargetBlock('VALUE');
  const inputType = getArduinoType(valueBlock);
  // Generate C-style cast for numbers, and .toFloat() for Strings.
  if (inputType === 'String') {
    return [`String(${value}).toFloat()`, ArduinoGenerator.ORDER_ATOMIC];
  } else {
    return [`(float)(${value})`, ArduinoGenerator.ORDER_ATOMIC];
  }
};

// Text Append Block
ArduinoGenerator['text_append'] = function(block: Blockly.Block) {
  const varName = ArduinoGenerator.variableDB_.getName(block.getFieldValue('VAR'), Blockly.VARIABLE_CATEGORY_NAME);
  const text = ArduinoGenerator.valueToCode(block, 'TEXT', ArduinoGenerator.ORDER_NONE) || '""';
  return `${varName} += ${text};\n`;
};



// Value blocks for int, float, string, bool
ArduinoGenerator['int_value'] = function(block: Blockly.Block) {
  const value = block.getFieldValue('INT') || '0';
  return [value, ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['float_value'] = function(block: Blockly.Block) {
  let value = block.getFieldValue('FLOAT') || '0.0';
  // Ensure it always has a decimal point
  if (!value.includes('.')) value += '.0';
  return [value, ArduinoGenerator.ORDER_ATOMIC];
};
ArduinoGenerator['string_value'] = function(block: Blockly.Block) {
  const value = block.getFieldValue('STRING') || '';
  return ['"' + value.replace(/\\/g, '\\\\').replace(/"/g, '\\"') + '"', ArduinoGenerator.ORDER_ATOMIC];
};




// You need to port or implement generator functions for ALL standard blocks
// (controls_if, math_number, etc.) from blockly-arduino-master/generators/blockly.js
// and other specific Arduino blocks from blockly-arduino-master/generators/arduino.js
// into this file, adapted for TypeScript.

// Example:
ArduinoGenerator['math_number'] = function(block: Blockly.Block): [string, number] {
  const fieldValue = block.getFieldValue('NUM');
  const code = String(Number(fieldValue));
  const order = Number(fieldValue) < 0 ? ArduinoGenerator.ORDER_UNARY_PREFIX : ArduinoGenerator.ORDER_ATOMIC;
  return [code, order];
};

ArduinoGenerator['controls_if'] = function(block: Blockly.Block): string {
  let n = 0;
  let code = '';
  let branchCode, conditionCode;
  do {
    conditionCode = this.valueToCode(block, 'IF' + n, ArduinoGenerator.ORDER_NONE) || 'false';
    branchCode = this.statementToCode(block, 'DO' + n);
    code += (n > 0 ? ' else ' : '') + 'if (' + conditionCode + ') {\n' + branchCode + '}';
    ++n;
  } while (block.getInput('IF' + n));

  if (block.getInput('ELSE')) {
    branchCode = this.statementToCode(block, 'ELSE');
    code += ' else {\n' + branchCode + '}';
  }
  return code + '\n';
};

// Override workspaceToCode to ensure only arduino_setup code goes to setup(), and only arduino_loop code goes to loop()
ArduinoGenerator.workspaceToCode = function(workspace: Blockly.WorkspaceSvg): string {
  if (!workspace) {
    console.log('No workspace provided to workspaceToCode');
    return '';
  }
  this.init(workspace); // Ensure variableDB_ is initialized
  let setupCode = '';
  let loopCode = '';
  const topBlocks = workspace.getTopBlocks(true);
  for (let i = 0, block; (block = topBlocks[i]); i++) {
    if (block.type === 'arduino_setup') {
      let code = this.blockToCode(block);
      if (Array.isArray(code)) code = code[0];
      setupCode += code;
    } else if (block.type === 'arduino_loop') {
      let code = this.blockToCode(block);
      if (Array.isArray(code)) code = code[0];
      loopCode += code;
    }
  }

  // 1. Map block types to function names, declarations, and definitions
  const functionMap = {
    'wifi_setup': {
      decl: `void setWiFi(String wifiSSID, String wifiPassword) {
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.println(F("Setting WiFi..."));
    COMPUTER_SERIAL.print(F("WiFi SSID: "));
    COMPUTER_SERIAL.println(wifiSSID);
    COMPUTER_SERIAL.print(F("WiFi Password: "));
    COMPUTER_SERIAL.println(wifiPassword);
  }
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("S WiFi"));
    ESP32_SERIAL.println(wifiSSID);
    ESP32_SERIAL.println(wifiPassword);
  }
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.println(F("Waiting for WiFi Response"));
  }
  while (ESP32_SERIAL.available() == 0);
  String response = ESP32_SERIAL.readStringUntil('\\n');
  response.trim();
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.print(F("WiFi Response: "));
    COMPUTER_SERIAL.println(response);
  }
}`
    },
    'read_bme_temp': {
      decl: `float readBMESensorTemperature() {
  // Read temperature from BME280 sensor
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R BME T"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for BME Temperature Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String temperature = ESP32_SERIAL.readStringUntil('\\n');
  temperature.trim();
  return temperature.toFloat();
}`,
    },
    'read_bme_humidity': {
      decl: `float readBMESensorHumidity() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R BME H"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for BME Humidity Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String humidity = ESP32_SERIAL.readStringUntil('\\n');
  humidity.trim();
  return humidity.toFloat();
}`,
    },
    'read_bme_pressure': {
      decl: `float readBMESensorPressure() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R BME P"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for BME Pressure Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String pressure = ESP32_SERIAL.readStringUntil('\\n');
  pressure.trim();
  return pressure.toFloat();
}`,
    },
    'read_pms_pm1_0': {
      decl: `int readPMS7003SensorPM1_0() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R PMS 1.0"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for PMS PM1.0 Feedback"));
        delay(10);
      }
      return -999;  // Error value for int
    }
  }
  String pm1_0 = ESP32_SERIAL.readStringUntil('\\n');
  pm1_0.trim();
  return pm1_0.toInt();
}`,
    },
    'read_pms_pm2_5': {
      decl: `int readPMS7003SensorPM2_5() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R PMS 2.5"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for PMS PM2.5 Feedback"));
        delay(10);
      }
      return -999;  // Error value for int
    }
  }
  String pm2_5 = ESP32_SERIAL.readStringUntil('\\n');
  pm2_5.trim();
  return pm2_5.toInt();
}`,
    },
    'read_pms_pm10': {
      decl: `int readPMS7003SensorPM10() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R PMS 10.0"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for PMS PM10 Feedback"));
        delay(10);
      }
      return -999;  // Error value for int
    }
  }
  String pm10 = ESP32_SERIAL.readStringUntil('\\n');
  pm10.trim();
  return pm10.toInt();
}`,
    },
    'read_pms_n0_3': {
      decl: `int readPMS7003SensorN0_3() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R PMS N 0.3"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for PMS Number of 0.3um Particles Feedback"));
        delay(10);
      }
      return -999;  // Error value for int
    }
  }
  String n0_3 = ESP32_SERIAL.readStringUntil('\\n');
  n0_3.trim();
  return n0_3.toInt();
}`,
    },
    'read_pms_n0_5': {
      decl: `int readPMS7003SensorN0_5() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R PMS N 0.5"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for PMS Number of 0.5um Particles Feedback"));
        delay(10);
      }
      return -999;  // Error value for int
    }
  }
  String n0_5 = ESP32_SERIAL.readStringUntil('\\n');
  n0_5.trim();
  return n0_5.toInt();
}`,
    },
    'read_pms_n1_0': {
      decl: `int readPMS7003SensorN1_0() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R PMS N 1.0"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for PMS Number of 1.0um Particles Feedback"));
        delay(10);
      }
      return -999;  // Error value for int
    }
  }
  String n1_0 = ESP32_SERIAL.readStringUntil('\\n');
  n1_0.trim();
  return n1_0.toInt();
}`,
    },
    'read_pms_n2_5': {
      decl: `int readPMS7003SensorN2_5() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R PMS N 2.5"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for PMS Number of 2.5um Particles Feedback"));
        delay(10);
      }
      return -999;  // Error value for int
    }
  }
  String n2_5 = ESP32_SERIAL.readStringUntil('\\n');
  n2_5.trim();
  return n2_5.toInt();
}`,
    },
    'read_pms_n5_0': {
      decl: `int readPMS7003SensorN5_0() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R PMS N 5.0"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for PMS Number of 5.0um Particles Feedback"));
        delay(10);
      }
      return -999;  // Error value for int
    }
  }
  String n5_0 = ESP32_SERIAL.readStringUntil('\\n');
  n5_0.trim();
  return n5_0.toInt();
}`,
    },
    'read_pms_n10_0': {
      decl: `int readPMS7003SensorN10_0() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R PMS N 10.0"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for PMS Number of 10.0um Particles Feedback"));
        delay(10);
      }
      return -999;  // Error value for int
    }
  }
  String n10_0 = ESP32_SERIAL.readStringUntil('\\n');
  n10_0.trim();
  return n10_0.toInt();
}`,
    },
    'read_mhz_co2': {
      decl: `int readMHZ19BSensorCO2() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R MHZ19B CO2"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for MHZ19B CO2 Feedback"));
        delay(10);
      }
      return -999;  // Error value for int
    }
  }
  String co2 = ESP32_SERIAL.readStringUntil('\\n');
  co2.trim();
  return co2.toInt();
}`,
    },
    'read_mhz_min_co2': {
      decl: `int readMHZ19BSensorMinCO2() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R MHZ19B Min CO2"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for MHZ19B Min CO2 Feedback"));
        delay(10);
      }
      return -999;  // Error value for int
    }
  }
  String minCO2 = ESP32_SERIAL.readStringUntil('\\n');
  minCO2.trim();
  return minCO2.toInt();
}`,
    },
    // Additional MHZ19B functions
    'read_mhz_temperature': {
      decl: `float readMHZ19BSensorTemperature() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R MHZ19B Temperature"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for MHZ19B Temperature Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String temperature = ESP32_SERIAL.readStringUntil('\\n');
  temperature.trim();
  return temperature.toFloat();
}`,
    },
    'read_mhz_accuracy': {
      decl: `float readMHZ19BSensorAccuracy() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R MHZ19B Accuracy"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for MHZ19B Accuracy Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String accuracy = ESP32_SERIAL.readStringUntil('\\n');
  accuracy.trim();
  return accuracy.toFloat();
}`,
    },
    // Additional GPS functions
    'read_gps_quality': {
      decl: `float readGPSQuality() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R GPS Quality"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for GPS Quality Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String gpsQuality = ESP32_SERIAL.readStringUntil('\\n');
  gpsQuality.trim();
  return gpsQuality.toFloat();
}`,
    },
    'read_gps_accuracy': {
      decl: `float readGPSAccuracy() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R GPS Accuracy"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for GPS Accuracy Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String gpsAccuracy = ESP32_SERIAL.readStringUntil('\\n');
  gpsAccuracy.trim();
  return gpsAccuracy.toFloat();
}`,
    },
    // Device UUID function
    'get_device_uuid': {
      decl: `String getDeviceUUID() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("G UUID"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for UUID Feedback"));
        delay(10);
      }
      return "Get UUID Timeout";
    }
  }
  String uuid = ESP32_SERIAL.readStringUntil('\\n');
  uuid.trim();
  return uuid;
}`,
    },



    // === SENSOR STATUS FUNCTIONS ===
    'get_bme280_status': {
      decl: `bool getBME280Status() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("G BME280 STATUS"));
  }
  while (ESP32_SERIAL.available() == 0) {
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("Waiting for BME280 Status"));
      delay(10);
    }
  }
  String status = ESP32_SERIAL.readStringUntil('\\n');
  status.trim();
  return status == "BME280_OK";
}`,
    },
    'get_pms7003_status': {
      decl: `bool getPMS7003Status() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("G PMS7003 STATUS"));
  }
  while (ESP32_SERIAL.available() == 0) {
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("Waiting for PMS7003 Status"));
      delay(10);
    }
  }
  String status = ESP32_SERIAL.readStringUntil('\\n');
  status.trim();
  return status == "PMS7003_OK";
}`,
    },
    'get_mhz19b_status': {
      decl: `bool getMHZ19BStatus() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("G MHZ19B STATUS"));
  }
  while (ESP32_SERIAL.available() == 0) {
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("Waiting for MHZ19B Status"));
      delay(10);
    }
  }
  String status = ESP32_SERIAL.readStringUntil('\\n');
  status.trim();
  return status == "MHZ19B_OK";
}`,
    },
    'get_wind_speed_status': {
      decl: `bool getWindSpeedStatus() {
  int windReading = analogRead(WIND_SPEED_SENSOR_PIN);
  bool wind_status = false;
  if (windReading >= 0 && windReading <= 5000) {
    wind_status = true;
  }
  return wind_status;
}`,
    },
    'get_uv_status': {
      decl: `bool getUVStatus() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("G LTR390 STATUS"));
  }
  while (ESP32_SERIAL.available() == 0) {
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("Waiting for UV Status"));
      delay(10);
    }
  }
  String status = ESP32_SERIAL.readStringUntil('\\n');
  status.trim();
  return status == "LTR390_OK";
}`,
    },
    'get_gps_status': {
      decl: `bool getGPSStatus() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("G GPS STATUS"));
  }
  while (ESP32_SERIAL.available() == 0) {
    if (COMPUTER_SERIAL) {
      COMPUTER_SERIAL.println(F("Waiting for GPS Status"));
      delay(10);
    }
  }
  String status = ESP32_SERIAL.readStringUntil('\\n');
  status.trim();
  return status == "GPS_OK";
}`,
    },
    // New sensor functions
    'read_wind_speed': {
      decl: `float readWindSpeed() {
  float windSpeed = float(6 * analogRead(WIND_SPEED_SENSOR_PIN) * 5) / 1023;
  ESP32_SERIAL.println("S WIND");
  ESP32_SERIAL.println(String(windSpeed));
  return windSpeed;
}`,
    },
    // Wind direction not supported by ESP32 - removed
    'read_uv_index': {
      decl: `float readUVIndex() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R LTR390 Uvi"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for UV Index Feedback"));
        delay(10);
      }
      return -999.0;  // Error value for float
    }
  }
  String uvIndex = ESP32_SERIAL.readStringUntil('\\n');
  uvIndex.trim();
  return uvIndex.toFloat();
}`,
    },
    'read_uv_intensity': {
      decl: `float readUVIntensity() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R LTR390 Lux"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for UV Intensity Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String uvIntensity = ESP32_SERIAL.readStringUntil('\\n');
  uvIntensity.trim();
  return uvIntensity.toFloat();
}`,
    },
    'read_gps_latitude': {
      decl: `float readGPSLatitude() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R GPS Latitude"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for GPS Latitude Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String gpsLatitude = ESP32_SERIAL.readStringUntil('\\n');
  gpsLatitude.trim();
  return gpsLatitude.toFloat();
}`,
    },
    'read_gps_longitude': {
      decl: `float readGPSLongitude() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R GPS Longitude"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for GPS Longitude Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String gpsLongitude = ESP32_SERIAL.readStringUntil('\\n');
  gpsLongitude.trim();
  return gpsLongitude.toFloat();
}`,
    },
    'read_gps_altitude': {
      decl: `float readGPSAltitude() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R GPS Altitude"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for GPS Altitude Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String gpsAltitude = ESP32_SERIAL.readStringUntil('\\n');
  gpsAltitude.trim();
  return gpsAltitude.toFloat();
}`,
    },
    // GPS speed not supported by ESP32 - removed
    'read_gps_satellites': {
      decl: `float readGPSSatellites() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("R GPS Satellites"));
  }
  int timeout = 0;
  while (ESP32_SERIAL.available() == 0) {
    delay(10);
    timeout++;
    if (timeout > 10) {
      if (COMPUTER_SERIAL) {
        COMPUTER_SERIAL.println(F("Waiting for GPS Satellites Feedback"));
        delay(10);
      }
      return NAN;  // Error value for float
    }
  }
  String gpsSatellites = ESP32_SERIAL.readStringUntil('\\n');
  gpsSatellites.trim();
  return gpsSatellites.toFloat();
}`,
    },
    // Additional utility functions
    'send_log_to_server': {
      decl: `void sendLogToServer(String logType, String message) {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("U LOG"));
    ESP32_SERIAL.println(logType);
    ESP32_SERIAL.println(message);
  }
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.println(F("Waiting for Log Upload Feedback"));
  }
  while (ESP32_SERIAL.available() == 0);
  String response = ESP32_SERIAL.readStringUntil('\\n');
  response.trim();
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.print(F("Log Response: "));
    COMPUTER_SERIAL.println(response);
  }
}`,
    },
    
    // Individual array aggregate functions - included only when used
    'getArraySum': {
      decl: `template<typename T>
float getArraySum(T* array, int arraySize) {
  float sum = 0.0;
  for (int i = 0; i < arraySize; i++) {
    sum += array[i];
  }
  return sum;
}`,
    },
    'getArrayAverage': {
      decl: `template<typename T>
float getArrayAverage(T* array, int arraySize) {
  if (arraySize == 0) return 0.0;
  float sum = getArraySum(array, arraySize);
  return sum / arraySize;
}`,
    },
    'getArrayMin': {
      decl: `template<typename T>
float getArrayMin(T* array, int arraySize) {
  if (arraySize == 0) return 0.0;
  float minVal = array[0];
  for (int i = 1; i < arraySize; i++) {
    if (array[i] < minVal) minVal = array[i];
  }
  return minVal;
}`,
    },
    'getArrayMax': {
      decl: `template<typename T>
float getArrayMax(T* array, int arraySize) {
  if (arraySize == 0) return 0.0;
  float maxVal = array[0];
  for (int i = 1; i < arraySize; i++) {
    if (array[i] > maxVal) maxVal = array[i];
  }
  return maxVal;
}`,
    },
    'getArrayLength': {
      decl: `int getArrayLength(float* array) {
  // Note: In Arduino C++, array length must be tracked separately
  // This is a placeholder - actual implementation depends on context
  return 0;
}`,
    },
    
    'lists_setIndex': {
      decl: `template<typename T1, typename T2>
void setArrayElement(T1 array[], int index, T2 value) {
  // Note: This is a simplified implementation for Arduino.
  // In practice, array bounds are not checked.
  array[index] = value;
}`,

    },
    
    'lists_getSublist': {
      decl: `template<typename T>
T* getSubArray(T array[], int start, int end) {
  // NOTE: This function is problematic on Arduino due to memory management.
  // Returning a pointer to a new array requires dynamic allocation,
  // which can lead to memory leaks if not handled carefully by the user.
  // This is a placeholder and will not work without a proper implementation.
  return NULL;
}`,
    },
    
    'lists_length': {
      decl: `int getArrayLength(String array[]) {
  // Note: Arduino arrays don't have built-in length
  // This would need to be tracked separately
  return 0; // Placeholder implementation
}`,
    },
    
    'lists_indexOf': {
      decl: `template<typename T>
int findInArray(T array[], int arraySize, T value) {
  for (int i = 0; i < arraySize; i++) {
    if (array[i] == value) {
      return i;
    }
  }
  return -1; // Not found
}`,
    },



    // === INDIVIDUAL GROUPED SENSOR UPLOAD FUNCTIONS ===
    'send_bme280_data_to_server': {
      decl: `String sendBME280DataToServer() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("U BME280"));
  }
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.println(F("Waiting for BME280 Upload Feedback"));
  }
  while (ESP32_SERIAL.available() == 0);
  String response = ESP32_SERIAL.readStringUntil('\\n');
  response.trim();
  COMPUTER_SERIAL.println(response);
  return response;
}`,
    },
    'send_pms7003_data_to_server': {
      decl: `String sendPMS7003DataToServer() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("U PMS7003"));
  }
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.println(F("Waiting for PMS7003 Upload Feedback"));
  }
  while (ESP32_SERIAL.available() == 0);
  String response = ESP32_SERIAL.readStringUntil('\\n');
  response.trim();
  COMPUTER_SERIAL.println(response);
  return response;
}`,
    },
    'send_mhz19b_data_to_server': {
      decl: `String sendMHZ19BDataToServer() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("U MHZ19B"));
  }
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.println(F("Waiting for MHZ19B Upload Feedback"));
  }
  while (ESP32_SERIAL.available() == 0);
  String response = ESP32_SERIAL.readStringUntil('\\n');
  response.trim();
  COMPUTER_SERIAL.println(response);
  return response;
}`,
    },
    'send_wind_data_to_server': {
      decl: `String sendWindDataToServer() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("U WIND"));
  }
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.println(F("Waiting for Wind Upload Feedback"));
  }
  while (ESP32_SERIAL.available() == 0);
  String response = ESP32_SERIAL.readStringUntil('\\n');
  response.trim();
  COMPUTER_SERIAL.println(response);
  return response;
}`,
    },
    'send_ltr390_data_to_server': {
      decl: `String sendLTR390DataToServer() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("U LTR390"));
  }
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.println(F("Waiting for LTR390 Upload Feedback"));
  }
  while (ESP32_SERIAL.available() == 0);
  String response = ESP32_SERIAL.readStringUntil('\\n');
  response.trim();
  COMPUTER_SERIAL.println(response);
  return response;
}`,
    },
    'send_gps_data_to_server': {
      decl: `String sendGPSDataToServer() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("U GPS"));
  }
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.println(F("Waiting for GPS Upload Feedback"));
  }
  while (ESP32_SERIAL.available() == 0);
  String response = ESP32_SERIAL.readStringUntil('\\n');
  response.trim();
  COMPUTER_SERIAL.println(response);
  return response;
}`,
    },
    'send_all_sensors_data_to_server': {
      decl: `String sendAllSensorGroupsToServer() {
  if (ESP32_SERIAL) {
    ESP32_SERIAL.println(F("U ALL"));
  }
  if (COMPUTER_SERIAL) {
    COMPUTER_SERIAL.println(F("Waiting for All Sensors Upload Feedback"));
  }
  while (ESP32_SERIAL.available() == 0);
  String response = ESP32_SERIAL.readStringUntil('\\n');
  response.trim();
  COMPUTER_SERIAL.println(response);
  return response;
}`,
    }
  };

  // 2. Traverse all blocks in the workspace and collect used function block types
  const usedFunctionTypes = new Set<string>();
  let needsWindSensorPin = false;

  function traverseBlocks(block: Blockly.Block | null) {
    if (!block) return;

    // Check for wind sensor usage
    if (block.type === 'read_wind_speed' ||
        block.type === 'get_wind_speed_status' ||
        (block.type === 'send_grouped_sensor_data' &&
         (block.getFieldValue('SENSOR_GROUP') === 'WIND' || block.getFieldValue('SENSOR_GROUP') === 'ALL'))) {
      needsWindSensorPin = true;
    }

    // Handle send_grouped_sensor_data block specially to only include needed functions
    if (block.type === 'send_grouped_sensor_data') {
      const sensorGroup = block.getFieldValue('SENSOR_GROUP') || 'ALL';
      switch(sensorGroup) {
        case 'BME280':
          usedFunctionTypes.add('send_bme280_data_to_server');
          break;
        case 'PMS7003':
          usedFunctionTypes.add('send_pms7003_data_to_server');
          break;
        case 'MHZ19B':
          usedFunctionTypes.add('send_mhz19b_data_to_server');
          break;
        case 'WIND':
          usedFunctionTypes.add('send_wind_data_to_server');
          break;
        case 'LTR390':
          usedFunctionTypes.add('send_ltr390_data_to_server');
          break;
        case 'GPS':
          usedFunctionTypes.add('send_gps_data_to_server');
          break;
        case 'ALL':
        default:
          usedFunctionTypes.add('send_all_sensors_data_to_server');
          break;
      }
    } else if (block.type === 'array_aggregate') {
      // Handle array_aggregate block specially to only include the specific operation
      const op = block.getFieldValue('OP') || 'SUM';
      const functionName = {
        'SUM': 'getArraySum',
        'AVERAGE': 'getArrayAverage',
        'MIN': 'getArrayMin',
        'MAX': 'getArrayMax'
      }[op] || 'getArraySum';
      usedFunctionTypes.add(functionName);

      // If using average, also need sum function as dependency
      if (op === 'AVERAGE') {
        usedFunctionTypes.add('getArraySum');
      }
    } else if (block.type === 'lists_length') {
      // If explicitly using getArrayLength function instead of sizeof, add it
      // Current implementation uses sizeof(), so this is optional
      // usedFunctionTypes.add('getArrayLength');
    } else if (functionMap[block.type]) {
      usedFunctionTypes.add(block.type);
    }

    // Traverse all children
    for (const input of block.inputList) {
      const child = input.connection && input.connection.targetBlock();
      if (child) traverseBlocks(child);
    }
    // Traverse next block
    if (block.nextConnection && block.nextConnection.targetBlock()) {
      traverseBlocks(block.nextConnection.targetBlock());
    }
  }
  for (const block of workspace.getAllBlocks(false)) {
    traverseBlocks(block);
  }

  // 3. Build function declarations for only used functions
  let functionDeclarations = '';
  if (usedFunctionTypes.size > 0) {
    functionDeclarations += '\n// === User Function Declarations ===\n';
    for (const type of usedFunctionTypes) {
      functionDeclarations += functionMap[type].decl + '\n';
    }
  }

  // Collect variable declarations from tracked variables
  let variableDeclarations = '';
  if (this.variableDeclarations_ && this.variableDeclarations_.size > 0) {
    variableDeclarations = '\n// === User Variables ===\n';
    for (const declaration of this.variableDeclarations_) {
      variableDeclarations += declaration + '\n';
    }
    variableDeclarations += '\n';
  }

  // Build includes and global variables based on Arduino_Controller.ino
  let includes = `// === Arduino Controller for ESP32 Management ===
// Handles graphical programming logic and ESP32 control
// === Basic Libraries ===
#include "Arduino.h"
#include <SoftwareSerial.h>

// === Serial Communication ===
SoftwareSerial esp32Serial(3, 2);  // RX, TX pins for ESP32 communication
#define ESP32_SERIAL esp32Serial   // Communication with ESP32
#define COMPUTER_SERIAL Serial     // USB communication with computer
`;

  // Conditionally add wind sensor pin definition
  if (needsWindSensorPin) {
    includes += `
// WIND-SPEED-SENSOR config
#define WIND_SPEED_SENSOR_PIN A0
`;
  }

  includes += `
bool initFlag = false;
`;

  // Build setup function based on Arduino_Controller.ino
  const setupTemplate = `// === Setup ===
void setup() {
  COMPUTER_SERIAL.begin(115200);
  while (!COMPUTER_SERIAL) {
    delay(10);
  }
  COMPUTER_SERIAL.println(F("Arduino Controller Initialized"));

  // Initialize ESP32 communication
  ESP32_SERIAL.begin(9600);  // SoftwareSerial typically uses lower baud rates
  while (!ESP32_SERIAL){
    delay(10);
  }

  ESP32_SERIAL.println(F("R INIT"));
  while (!initFlag) {
    delay(10);
    if (ESP32_SERIAL.available() > 0) {
      String feedback = ESP32_SERIAL.readStringUntil('\\n');
      feedback.trim();
      if (feedback == "YES") {
        initFlag = true;
        COMPUTER_SERIAL.println(F("System Ready"));
      }
    }
  }
${setupCode ? setupCode.replace(/\n/g, '\n  ') : ''}
}

`;

  const loopTemplate = `// === Main Loop ===
void loop() {
  if (initFlag) {
    ESP32_SERIAL.println(F("SYNC TIME"));
${loopCode ? '  ' + loopCode.replace(/\n/g, '\n    ') : ''}
  }
}

`;

  return includes + functionDeclarations + variableDeclarations + '\n' + setupTemplate + loopTemplate;
};

// Add statementToCode utility to ArduinoGenerator
ArduinoGenerator.statementToCode = function(block: Blockly.Block, inputName: string): string {
  const targetBlock = block.getInputTargetBlock(inputName);
  if (!targetBlock && !block.getInput(inputName)) {
    throw new ReferenceError(`Input "${inputName}" doesn't exist on "${block.type}"`);
  }
  let code = this.blockToCode(targetBlock);
  if (typeof code !== 'string') {
    throw new TypeError('Expecting code from statement block: ' + (targetBlock && targetBlock.type));
  }
  if (code) {
    code = this.prefixLines(code, this.INDENT || '  ');
  }
  return code;
};

// Refactored blockToCode for clarity and maintainability
Blockly.Generator.prototype.blockToCode = function(block: Blockly.Block | null, opt_thisOnly?: boolean): any {
  if (!block) return '';

  // If the block is disabled, skip it and process the next block (unless thisOnly is set)
  if (!(block.isEnabled && block.isEnabled())) {
    return opt_thisOnly ? '' : this.blockToCode(block.getNextBlock());
  }

  // Find the generator function for this block type
  const generatorFn = this[block.type];
  if (typeof generatorFn !== 'function') {
    throw Error(
      `Language "${this.name_}" does not know how to generate code for block type "${block.type}".`
    );
  }
  // Call the generator function with the block as both context and argument
  let code = generatorFn.call(this, block);
  // If the generator returns a tuple, it's a value block ([code, order])
  if (Array.isArray(code)) {
    if (!block.outputConnection) {
      throw TypeError(`Expecting string from statement block: ${block.type}`);
    }
    // Scrub the code and return the tuple
    return [this.scrub_(block, code[0], opt_thisOnly), code[1]];
  }

  // If the generator returns a string, it's a statement block
  if (typeof code === 'string') {
    // Optionally add a statement prefix for debugging/tracing
    if (this.STATEMENT_PREFIX) {
      const blockId = block.id.replace(/\$/g, '$$$$');
      code = this.STATEMENT_PREFIX.replace(/%1/g, `'${blockId}'`) + code;
    }
    return this.scrub_(block, code, opt_thisOnly);
  }

  // If the generator returns null, treat as empty string
  if (code === null) return '';

  // Otherwise, invalid code was generated
  throw SyntaxError(`Invalid code generated: ${code}`);
};

// --- Standard Blockly control and logic blocks for Arduino ---



// Logic operations (and/or)
ArduinoGenerator['logic_operation'] = function(block) {
  const op = block.getFieldValue('OP');
  const operator = (op === 'AND') ? '&&' : '||';
  const order = (operator === '&&') ? ArduinoGenerator.ORDER_LOGICAL_AND : ArduinoGenerator.ORDER_LOGICAL_OR;
  const argument0 = ArduinoGenerator.valueToCode(block, 'A', order) || 'false';
  const argument1 = ArduinoGenerator.valueToCode(block, 'B', order) || 'false';
  return [argument0 + ' ' + operator + ' ' + argument1, order];
};

// Logic compare (==, !=, <, >, <=, >=)
ArduinoGenerator['logic_compare'] = function(block) {
  const op = block.getFieldValue('OP');
  const ops = {
    'EQ': '==',
    'NEQ': '!=',
    'LT': '<',
    'LTE': '<=',
    'GT': '>',
    'GTE': '>='
  };
  const operator = ops[op];
  const order = ArduinoGenerator.ORDER_RELATIONAL;
  
  const blockA = block.getInputTargetBlock('A');
  const blockB = block.getInputTargetBlock('B');
  const typeA = getArduinoType(blockA);
  const typeB = getArduinoType(blockB);

  const argument0 = ArduinoGenerator.valueToCode(block, 'A', order) || (typeA === 'String' ? '""' : '0');
  const argument1 = ArduinoGenerator.valueToCode(block, 'B', order) || (typeB === 'String' ? '""' : '0');

  // String comparison requires a specific method and only works with EQ/NEQ
  if (typeA === 'String' || typeB === 'String') {
    if (typeA !== 'String' || typeB !== 'String') {
      return ['/* ERROR: Cannot compare String with other types. */', order];
    }
    if (op !== 'EQ' && op !== 'NEQ') {
      return ['/* ERROR: String can only be compared with == or !=. */', order];
    }
    // Use == for string comparison in Arduino C++
    if (op === 'EQ') {
      return [`(${argument0}) == (${argument1})`, ArduinoGenerator.ORDER_EQUALITY];
    } else { // NEQ
      return [`(${argument0}) != (${argument1})`, ArduinoGenerator.ORDER_EQUALITY];
    }
  }
  
  return [argument0 + ' ' + operator + ' ' + argument1, order];
};

// Logic negate (!)
ArduinoGenerator['logic_negate'] = function(block) {
  const argument = ArduinoGenerator.valueToCode(block, 'BOOL', ArduinoGenerator.ORDER_UNARY_PREFIX) || 'false';
  return ['!' + argument, ArduinoGenerator.ORDER_UNARY_PREFIX];
};



// Variable getter
ArduinoGenerator['variables_get'] = function(block: Blockly.Block) {
  const varName = ArduinoGenerator.variableDB_.getName(block.getFieldValue('VAR'), Blockly.VARIABLE_CATEGORY_NAME);
  return [varName, ArduinoGenerator.ORDER_ATOMIC];
};

// Typed Variable Getters
ArduinoGenerator['int_variables_get'] = function(block: Blockly.Block) {
  const varName = ArduinoGenerator.variableDB_.getName(block.getFieldValue('VAR'), Blockly.VARIABLE_CATEGORY_NAME);
  return [varName, ArduinoGenerator.ORDER_ATOMIC];
};

ArduinoGenerator['float_variables_get'] = function(block: Blockly.Block) {
  const varName = ArduinoGenerator.variableDB_.getName(block.getFieldValue('VAR'), Blockly.VARIABLE_CATEGORY_NAME);
  return [varName, ArduinoGenerator.ORDER_ATOMIC];
};

ArduinoGenerator['text_variables_get'] = function(block: Blockly.Block) {
  const varName = ArduinoGenerator.variableDB_.getName(block.getFieldValue('VAR'), Blockly.VARIABLE_CATEGORY_NAME);
  return [varName, ArduinoGenerator.ORDER_ATOMIC];
};

// Variable setter with type detection and declaration tracking
ArduinoGenerator['variables_set'] = function(block: Blockly.Block) {
  const varName = ArduinoGenerator.variableDB_.getName(block.getFieldValue('VAR'), Blockly.VARIABLE_CATEGORY_NAME);
  const value = ArduinoGenerator.valueToCode(block, 'VALUE', ArduinoGenerator.ORDER_ASSIGNMENT) || '0';

  // Initialize variable declarations tracking if not exists
  if (!ArduinoGenerator.variableDeclarations_) {
    ArduinoGenerator.variableDeclarations_ = new Set();
  }

  const valueBlock = block.getInputTargetBlock('VALUE');
  // The type is now easily determined by the connected value block's output type.
  const varType = getArduinoType(valueBlock);

  // Add variable declaration if not already declared.
  // The type is inferred from the assigned value.
  const declaration = `${varType} ${varName};`;
  if (!ArduinoGenerator.variableDeclarations_.has(declaration)) {
    ArduinoGenerator.variableDeclarations_.add(declaration);
  }

  return varName + ' = ' + value + ';\n';
};

// Typed Variable Setters
ArduinoGenerator['int_variables_set'] = function(block: Blockly.Block) {
  const varName = ArduinoGenerator.variableDB_.getName(block.getFieldValue('VAR'), Blockly.VARIABLE_CATEGORY_NAME);
  const value = ArduinoGenerator.valueToCode(block, 'VALUE', ArduinoGenerator.ORDER_ASSIGNMENT) || '0';

  // Initialize variable declarations tracking if not exists
  if (!ArduinoGenerator.variableDeclarations_) {
    ArduinoGenerator.variableDeclarations_ = new Set();
  }

  // For typed variables, use the explicit type
  const varType = 'int';
  const declaration = `${varType} ${varName};`;
  if (!ArduinoGenerator.variableDeclarations_.has(declaration)) {
    ArduinoGenerator.variableDeclarations_.add(declaration);
  }

  return varName + ' = ' + value + ';\n';
};

ArduinoGenerator['float_variables_set'] = function(block: Blockly.Block) {
  const varName = ArduinoGenerator.variableDB_.getName(block.getFieldValue('VAR'), Blockly.VARIABLE_CATEGORY_NAME);
  const value = ArduinoGenerator.valueToCode(block, 'VALUE', ArduinoGenerator.ORDER_ASSIGNMENT) || '0.0';

  // Initialize variable declarations tracking if not exists
  if (!ArduinoGenerator.variableDeclarations_) {
    ArduinoGenerator.variableDeclarations_ = new Set();
  }

  // For typed variables, use the explicit type
  const varType = 'float';
  const declaration = `${varType} ${varName};`;
  if (!ArduinoGenerator.variableDeclarations_.has(declaration)) {
    ArduinoGenerator.variableDeclarations_.add(declaration);
  }

  return varName + ' = ' + value + ';\n';
};

ArduinoGenerator['text_variables_set'] = function(block: Blockly.Block) {
  const varName = ArduinoGenerator.variableDB_.getName(block.getFieldValue('VAR'), Blockly.VARIABLE_CATEGORY_NAME);
  const value = ArduinoGenerator.valueToCode(block, 'VALUE', ArduinoGenerator.ORDER_ASSIGNMENT) || '""';

  // Initialize variable declarations tracking if not exists
  if (!ArduinoGenerator.variableDeclarations_) {
    ArduinoGenerator.variableDeclarations_ = new Set();
  }

  // For typed variables, use the explicit type
  const varType = 'String';
  const declaration = `${varType} ${varName};`;
  if (!ArduinoGenerator.variableDeclarations_.has(declaration)) {
    ArduinoGenerator.variableDeclarations_.add(declaration);
  }

  return varName + ' = ' + value + ';\n';
};

// Custom Typed Variable Set Generator (with enhanced type detection) - REMOVED as it is dead code
// Custom Typed Variable Get Generator - REMOVED as it is dead code

// Arithmetic operations
ArduinoGenerator['math_arithmetic'] = function(block) {
  const OPERATORS = {
    'ADD': [' + ', ArduinoGenerator.ORDER_ADDITIVE],
    'MINUS': [' - ', ArduinoGenerator.ORDER_ADDITIVE],
    'MULTIPLY': [' * ', ArduinoGenerator.ORDER_MULTIPLICATIVE],
    'DIVIDE': [' / ', ArduinoGenerator.ORDER_MULTIPLICATIVE],
    'POWER': [null, ArduinoGenerator.ORDER_NONE] // handled separately
  };
  const op = block.getFieldValue('OP');
  const tuple = OPERATORS[op];
  const argument0 = ArduinoGenerator.valueToCode(block, 'A', tuple ? tuple[1] : ArduinoGenerator.ORDER_NONE) || '0';
  const argument1 = ArduinoGenerator.valueToCode(block, 'B', tuple ? tuple[1] : ArduinoGenerator.ORDER_NONE) || '0';
  let code;
  if (op === 'POWER') {
    code = 'pow(' + argument0 + ', ' + argument1 + ')';
    return [code, ArduinoGenerator.ORDER_NONE];
  } else {
    code = argument0 + tuple[0] + argument1;
    return [code, tuple[1]];
  }
};

// Single math functions (sqrt, abs, etc.)
ArduinoGenerator['math_single'] = function(block) {
  const operator = block.getFieldValue('OP');
  const arg = ArduinoGenerator.valueToCode(block, 'NUM', ArduinoGenerator.ORDER_NONE) || '0';
  let code;
  switch (operator) {
    case 'ROOT': code = 'sqrt(' + arg + ')'; break;
    case 'ABS': code = 'abs(' + arg + ')'; break;
    case 'NEG': code = '-(' + arg + ')'; break;
    case 'LN': code = 'log(' + arg + ')'; break;
    case 'LOG10': code = 'log10(' + arg + ')'; break;
    case 'EXP': code = 'exp(' + arg + ')'; break;
    case 'POW10': code = 'pow(10, ' + arg + ')'; break;
    case 'ROUND': code = 'round(' + arg + ')'; break;
    case 'ROUNDUP': code = 'ceil(' + arg + ')'; break;
    case 'ROUNDDOWN': code = 'floor(' + arg + ')'; break;
    case 'SIN': code = 'sin(' + arg + ')'; break;
    case 'COS': code = 'cos(' + arg + ')'; break;
    case 'TAN': code = 'tan(' + arg + ')'; break;
    default: code = arg;
  }
  return [code, ArduinoGenerator.ORDER_NONE];
};

// Math round block
ArduinoGenerator['math_round'] = function(block) {
  const op = block.getFieldValue('OP');
  const num = ArduinoGenerator.valueToCode(block, 'NUM', ArduinoGenerator.ORDER_NONE) || '0';
  // Casting to int is crucial because ceil() and floor() return doubles, which are invalid for array subscripts.
  // While round() often returns an int/long in Arduino, explicit casting is safer.
  if (op === 'ROUND') return [`(int)round(${num})`, ArduinoGenerator.ORDER_NONE];
  if (op === 'ROUNDUP') return [`(int)ceil(${num})`, ArduinoGenerator.ORDER_NONE];
  if (op === 'ROUNDDOWN') return [`(int)floor(${num})`, ArduinoGenerator.ORDER_NONE];
  return [num, ArduinoGenerator.ORDER_NONE];
};

// Delay Block
ArduinoGenerator['delay'] = function(block: Blockly.Block) {
  const delayMs = ArduinoGenerator.valueToCode(block, 'DELAY_MS', ArduinoGenerator.ORDER_NONE) || '0';
  return 'delay(' + delayMs + ');\n';
};



// Send Grouped Sensor Data Block - Fixed to use correct specific sensor upload functions
ArduinoGenerator['send_grouped_sensor_data'] = function(block: Blockly.Block) {
  const sensorGroup = block.getFieldValue('SENSOR_GROUP') || 'ALL';

  switch(sensorGroup) {
    case 'BME280':
      return 'sendBME280DataToServer();\n';
    case 'PMS7003':
      return 'sendPMS7003DataToServer();\n';
    case 'MHZ19B':
      return 'sendMHZ19BDataToServer();\n';
    case 'WIND':
      return 'sendWindDataToServer();\n';
    case 'LTR390':
      return 'sendLTR390DataToServer();\n';
    case 'GPS':
      return 'sendGPSDataToServer();\n';
    case 'ALL':
    default:
      return 'sendAllSensorGroupsToServer();\n';
  }
};



// === Standard Blockly Blocks ===

// Text operations
ArduinoGenerator['text_join'] = function(block: Blockly.Block) {
  // Properly get the number of items by counting inputs
  let itemCount = 0;
  while (block.getInput('ADD' + itemCount)) {
    itemCount++;
  }
  
  if (itemCount === 0) {
    return ['""', ArduinoGenerator.ORDER_ATOMIC];
  } else if (itemCount === 1) {
    const element = ArduinoGenerator.valueToCode(block, 'ADD0', ArduinoGenerator.ORDER_NONE) || '""';
    return [`${element}`, ArduinoGenerator.ORDER_ATOMIC];
  } else {
    const elements: string[] = [];
    for (let i = 0; i < itemCount; i++) {
      const element = ArduinoGenerator.valueToCode(block, 'ADD' + i, ArduinoGenerator.ORDER_NONE) || '""';
      elements.push(`${element}`);
    }
    const code = elements.join(' + ');
    return [code, ArduinoGenerator.ORDER_ADDITIVE];
  }
};

ArduinoGenerator['text_length'] = function(block: Blockly.Block) {
  const text = ArduinoGenerator.valueToCode(block, 'VALUE', ArduinoGenerator.ORDER_NONE) || '""';
  return [`(${text}).length()`, ArduinoGenerator.ORDER_ATOMIC];
};

// List operations (simplified for Arduino)
ArduinoGenerator['lists_create_with'] = function(block: Blockly.Block) {
  // Create a list with any number of elements of any type.
  const elements: string[] = [];
  let itemCount = 0;
  while (block.getInput('ADD' + itemCount)) {
    const element = this.valueToCode(block, 'ADD' + itemCount, this.ORDER_NONE) || '0';
    elements.push(element);
    itemCount++;
  }

  // Determine the element type from the block's output connection.
  let elementType = 'int'; // Default to int
  if (block.outputConnection && block.outputConnection.check) {
    const outputTypes = block.outputConnection.check;
    if (outputTypes.includes('String[]')) {
      elementType = 'String';
    } else if (outputTypes.includes('float[]')) {
      elementType = 'float';
    } else if (outputTypes.includes('int[]')) {
      elementType = 'int';
    }
  }

  // Instead of a temporary compound literal, create a global variable.
  const listVarName = this.variableDB_.getDistinctName('list', Blockly.Names.DEVELOPER_VARIABLE_TYPE);

  // Initialize the declarations set if it doesn't exist.
  if (!this.variableDeclarations_) {
    this.variableDeclarations_ = new Set();
  }

  // The variable is declared and initialized as a global array.
  // This avoids issues with temporary array lifetimes.
  const declaration = `${elementType} ${listVarName}[] = {${elements.join(', ')}};`;
  this.variableDeclarations_.add(declaration);

  // The block now returns the name of the global variable.
  return [listVarName, this.ORDER_ATOMIC];
};

ArduinoGenerator['lists_getIndex'] = function(block: Blockly.Block) {
  // Note: The user's block definition in blocklyBlocks.js uses 'VALUE' and 'AT'.
  const list = ArduinoGenerator.valueToCode(block, 'VALUE', ArduinoGenerator.ORDER_UNARY_POSTFIX) || '{}';
  const at = ArduinoGenerator.valueToCode(block, 'AT', ArduinoGenerator.ORDER_NONE) || '0';

  const code = `${list}[${at}]`;
  return [code, ArduinoGenerator.ORDER_UNARY_POSTFIX];
};

ArduinoGenerator['lists_setIndex'] = function(block: Blockly.Block) {
  const list = ArduinoGenerator.valueToCode(block, 'LIST', ArduinoGenerator.ORDER_NONE) || '{}';
  const value = ArduinoGenerator.valueToCode(block, 'TO', ArduinoGenerator.ORDER_NONE) || '0';
  const at = ArduinoGenerator.valueToCode(block, 'AT', ArduinoGenerator.ORDER_NONE) || '0';
  return `setArrayElement(${list}, ${at}, ${value});\n`;
};

ArduinoGenerator['lists_getSublist'] = function(block: Blockly.Block) {
  const list = ArduinoGenerator.valueToCode(block, 'LIST', ArduinoGenerator.ORDER_NONE) || '{}';
  const at1 = ArduinoGenerator.valueToCode(block, 'AT1', ArduinoGenerator.ORDER_NONE) || '0';
  const at2 = ArduinoGenerator.valueToCode(block, 'AT2', ArduinoGenerator.ORDER_NONE) || '0';
  return [`getSubArray(${list}, ${at1}, ${at2})`, ArduinoGenerator.ORDER_ATOMIC];
};

ArduinoGenerator['lists_length'] = function(block: Blockly.Block) {
  // Note: The user's block definition in blocklyBlocks.js uses 'VALUE'.
  const list = ArduinoGenerator.valueToCode(block, 'VALUE', ArduinoGenerator.ORDER_NONE) || '{}';
  // Generate native C++ code for array length calculation.
  // This works for statically declared arrays and C99-style compound literals.
  const code = `(sizeof(${list}) / sizeof((${list})[0]))`;
  return [code, ArduinoGenerator.ORDER_MULTIPLICATIVE];
};

ArduinoGenerator['lists_indexOf'] = function(block: Blockly.Block) {
  const list = ArduinoGenerator.valueToCode(block, 'VALUE', ArduinoGenerator.ORDER_NONE) || '{}';
  const find = ArduinoGenerator.valueToCode(block, 'FIND', ArduinoGenerator.ORDER_NONE) || '""';
  // We need array size for the findInArray function.
  const code = `findInArray(${list}, (sizeof(${list}) / sizeof((${list})[0])), ${find})`;
  return [code, ArduinoGenerator.ORDER_ATOMIC];
};



// Add init function to ensure variableDB_ is initialized
ArduinoGenerator.init = function(workspace) {
  // Create a new database of variable names.
  if (!this.nameDB_) {
    this.nameDB_ = new Blockly.Names(this.RESERVED_WORDS_);
  } else {
    this.nameDB_.reset();
  }
  this.variableDB_ = this.nameDB_;
  if (workspace && workspace.getVariableMap) {
    this.nameDB_.setVariableMap(workspace.getVariableMap());
  }

  // Initialize variable declarations tracking
  this.variableDeclarations_ = new Set();
};

// IMPORTANT: Export the generator instance
export default ArduinoGenerator;
