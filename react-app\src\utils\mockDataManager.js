// Mock data management utilities for development and testing
// This file should be excluded or disabled in production builds

class MockDataManager {
  constructor() {
    this.intervalRef = null;
    this.isActive = false;
    this.callbacks = {
      onDataUpdate: null,
      onStatusChange: null,
      onLog: null
    };
  }

  // Generate realistic mock sensor data with gradual changes (updated for prefixed field names)
  generateMockSensorData() {
    const timestamp = new Date().toISOString();

    return {
      timestamp,
      // MH-Z19B data - CO2 fluctuates around 400-600 ppm (prefixed format)
      mhz19b_co2: Math.floor(Math.random() * 200) + 400,
      mhz19b_min_co2: Math.floor(Math.random() * 50) + 380,

      // PMS7003 data - Air quality particles (prefixed format)
      pms7003_pm01: Math.floor(Math.random() * 20) + 5,
      pms7003_pm25: Math.floor(Math.random() * 30) + 10,
      pms7003_pm10: Math.floor(Math.random() * 40) + 15,
      pms7003_n0p3: Math.floor(Math.random() * 1000) + 500,
      pms7003_n0p5: Math.floor(Math.random() * 800) + 400,
      pms7003_n1p0: Math.floor(Math.random() * 600) + 300,
      pms7003_n2p5: Math.floor(Math.random() * 400) + 200,
      pms7003_n5p0: Math.floor(Math.random() * 200) + 100,
      pms7003_n10p0: Math.floor(Math.random() * 100) + 50,

      // BME280 data - Environmental conditions (prefixed format)
      bme280_temperature: parseFloat((Math.random() * 15 + 18).toFixed(1)),
      bme280_humidity: parseFloat((Math.random() * 40 + 40).toFixed(1)),
      bme280_pressure: Math.floor(Math.random() * 2000) + 100000,

      // Wind Speed data (prefixed format)
      wind_speed: parseFloat((Math.random() * 15).toFixed(1)),
      // Note: Wind direction removed as it's not supported by ESP32

      // LTR390 UV data (prefixed format)
      ltr390_uvi: parseFloat((Math.random() * 10).toFixed(1)),
      ltr390_lux: Math.floor(Math.random() * 100000 + 10000),

      // GPS data - Around Hong Kong with slight variations (prefixed format)
      gps_latitude: parseFloat((22.3 + Math.random() * 0.1).toFixed(6)),
      gps_longitude: parseFloat((114.1 + Math.random() * 0.1).toFixed(6)),
      gps_altitude: parseFloat((Math.random() * 100 + 50).toFixed(1)),
      gps_quality: Math.floor(Math.random() * 3), // 0-2 quality levels
      gps_satellites: Math.floor(Math.random() * 8) + 4, // 4-12 satellites
      gps_accuracy: parseFloat((Math.random() * 10 + 2).toFixed(1))
      // Note: GPS speed removed as it's not supported by ESP32
    };
  }

  // Generate initial mock history data
  generateMockHistoryData(count = 100) {
    const mockHistory = [];
    for (let i = count - 1; i >= 0; i--) {
      // Generate data points every 30 seconds going back (for testing time filters)
      const timestamp = new Date(Date.now() - i * 30 * 1000); 
      mockHistory.push({
        ...this.generateMockSensorData(),
        timestamp: timestamp.toISOString()
      });
    }
    return mockHistory;
  }

  // Set up callbacks for data updates
  setCallbacks({ onDataUpdate, onStatusChange, onLog }) {
    this.callbacks.onDataUpdate = onDataUpdate;
    this.callbacks.onStatusChange = onStatusChange;
    this.callbacks.onLog = onLog;
  }

  // Start mock data streaming
  startStreaming(deviceId, interval = 500) {
    if (this.intervalRef) {
      this.stopStreaming();
    }

    this.isActive = true;
    
    // Notify status change
    if (this.callbacks.onStatusChange) {
      this.callbacks.onStatusChange('online', true);
    }

    // Log start
    if (this.callbacks.onLog) {
      this.callbacks.onLog('Mock data streaming started', { 
        device: deviceId, 
        interval: interval >= 1000 ? `${interval/1000}s` : `${interval}ms`
      });
    }

    // Generate initial data immediately
    const initialData = this.generateMockSensorData();
    if (this.callbacks.onDataUpdate) {
      this.callbacks.onDataUpdate(initialData, 'replace');
    }

    // Start interval for continuous updates
    this.intervalRef = setInterval(() => {
      const newMockData = this.generateMockSensorData();
      
      if (this.callbacks.onDataUpdate) {
        this.callbacks.onDataUpdate(newMockData, 'append');
      }

      // Log the update
      if (this.callbacks.onLog) {
        this.callbacks.onLog('Mock data updated', {
          timestamp: newMockData.timestamp,
          dataAge: '0s'
        });
      }
    }, interval);
  }

  // Stop mock data streaming
  stopStreaming() {
    if (this.intervalRef) {
      clearInterval(this.intervalRef);
      this.intervalRef = null;
    }
    
    this.isActive = false;

    // Notify status change
    if (this.callbacks.onStatusChange) {
      this.callbacks.onStatusChange('offline', false);
    }

    // Log stop
    if (this.callbacks.onLog) {
      this.callbacks.onLog('Mock data streaming stopped');
    }
  }

  // Get current status
  getStatus() {
    return {
      isActive: this.isActive,
      hasInterval: !!this.intervalRef
    };
  }

  // Cleanup - should be called on component unmount
  cleanup() {
    this.stopStreaming();
    this.callbacks = {
      onDataUpdate: null,
      onStatusChange: null,
      onLog: null
    };
  }
}

// Singleton instance
const mockDataManager = new MockDataManager();

// Export functions for easier use
export const startMockDataStreaming = (deviceId, callbacks, interval = 500) => {
  mockDataManager.setCallbacks(callbacks);
  mockDataManager.startStreaming(deviceId, interval);
};

export const stopMockDataStreaming = () => {
  mockDataManager.stopStreaming();
};

export const getMockDataStatus = () => {
  return mockDataManager.getStatus();
};

export const generateMockHistoryData = (count = 100) => {
  return mockDataManager.generateMockHistoryData(count);
};

export const cleanupMockData = () => {
  mockDataManager.cleanup();
};

// Check if mock data is enabled (can be controlled by environment variables)
export const isMockDataEnabled = () => {
  // In production, this should return false
  // Can be controlled by environment variables
  if (import.meta.env.PROD) {
    return false; // Disable in production builds
  }
  
  // In development, check for specific flag
  return import.meta.env.VITE_ENABLE_MOCK_DATA !== 'false';
};

// Mock device identifier
export const MOCK_DEVICE_ID = 'MOCK-DEVICE-001';

export default mockDataManager; 