// Utility functions for sensor data processing
import { getTimeRangeByValue, defaultTimeRange } from '../config/sensorConfig';

// Helper function to format timestamp for chart display
export const formatTimeForChart = (timestamp, timeRange = null) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  
  // Choose format based on time range
  if (timeRange) {
    const config = getTimeRangeByValue(timeRange);
    if (config) {
      const rangeMs = config.milliseconds;
      
      // For very short ranges (< 10 minutes), show seconds
      if (rangeMs <= 10 * 60 * 1000) {
        return date.toLocaleTimeString('en', { 
          hour: '2-digit', 
          minute: '2-digit',
          second: '2-digit'
        });
      }
      // For medium ranges (< 2 hours), show hour:minute
      else if (rangeMs <= 2 * 60 * 60 * 1000) {
        return date.toLocaleTimeString('en', { 
          hour: '2-digit', 
          minute: '2-digit' 
        });
      }
      // For longer ranges, show date and hour
      else {
        return date.toLocaleDateString('en', { 
          month: 'short', 
          day: 'numeric',
          hour: '2-digit'
        });
      }
    }
  }
  
  // Default format
  return date.toLocaleTimeString('en', { hour: '2-digit', minute: '2-digit' });
};

// Helper function to safely get numeric value
export const getNumericValue = (value) => {
  if (value === null || value === undefined) return null;
  const num = parseFloat(value);
  return isNaN(num) ? null : num;
};

// Process history data for chart visualization
export const processHistoryDataForCharts = (historyData, fieldMappings, maxDataPoints = 200, timeRange = null) => {
  if (!historyData || historyData.length === 0) return [];
  
  // Sort data by timestamp first (oldest to newest)
  const sortedData = [...historyData].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
  
  // If we have more data points than the maximum, sample intelligently
  let dataToProcess = sortedData;
  if (sortedData.length > maxDataPoints) {
    const step = Math.floor(sortedData.length / maxDataPoints);
    dataToProcess = sortedData.filter((_, index) => index % step === 0);
    // Always include the last data point
    if (dataToProcess[dataToProcess.length - 1] !== sortedData[sortedData.length - 1]) {
      dataToProcess.push(sortedData[sortedData.length - 1]);
    }
  }
  
  return dataToProcess
    .map(item => {
      const processedItem = {
        date: formatTimeForChart(item.timestamp, timeRange)
      };
      
      // Map fields based on provided mappings
      fieldMappings.forEach(mapping => {
        processedItem[mapping.chartField] = getNumericValue(item[mapping.dataField]);
      });
      
      return processedItem;
    })
    .filter(item => item.date);
};

// Filter history data based on selected time range - show data within recent X time
export const filterHistoryDataByTimeRange = (historyData, selectedTimeRange = defaultTimeRange) => {
  if (!historyData || historyData.length === 0) return [];
  
  const timeRangeConfig = getTimeRangeByValue(selectedTimeRange);
  if (!timeRangeConfig) return historyData;
  
  // Calculate cutoff time (current time minus the selected time range)
  const currentTime = new Date();
  const cutoffTime = new Date(currentTime.getTime() - timeRangeConfig.milliseconds);
  
  // Filter data to only include records within the time range
  const filteredData = historyData.filter(item => {
    if (!item.timestamp) return false;
    const itemTime = new Date(item.timestamp);
    return itemTime >= cutoffTime;
  });
  
  // Sort by timestamp (oldest first) for proper chart display
  return filteredData.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
};

// Calculate statistics for sensors from processed chart data
export const calculateSensorStatistics = (processedChartData, selectedSensors, currentData, t, getSensorI18nKey) => {
  if (processedChartData.length === 0) return [];
  
  return selectedSensors.map(sensor => {
    const translatedName = t(getSensorI18nKey(sensor.field));
    const values = processedChartData
      .map(item => item[translatedName])
      .filter(val => val !== null && val !== undefined && !isNaN(val));
    
    if (values.length === 0) {
      return {
        name: translatedName,
        min: '--',
        max: '--',
        avg: '--',
        current: '--',
        unit: sensor.unit,
        color: sensor.bgColor,
        groupTitle: sensor.groupTitle
      };
    }

    const min = Math.min(...values);
    const max = Math.max(...values);
    const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
    const current = currentData ? getNumericValue(currentData[sensor.field]) : null;

    return {
      name: translatedName,
      min: min.toFixed(1),
      max: max.toFixed(1),
      avg: avg.toFixed(1),
      current: current !== null ? current.toFixed(1) : '--',
      unit: sensor.unit,
      color: sensor.bgColor,
      groupTitle: sensor.groupTitle
    };
  });
};