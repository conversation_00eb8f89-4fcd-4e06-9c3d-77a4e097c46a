# Robot Teaching System Server v2.0

A comprehensive, modular backend system for robot sensor data collection, Arduino code generation, and data reporting.

## 🏗️ Architecture Overview

The server is built with a modular architecture supporting three main functional areas:

### 1. Dashboard Module
- Real-time device monitoring
- Sensor data collection and storage
- Device-specific data filtering
- Live status tracking

### 2. Arduino Code Generation Module
- Visual programming interface support
- Template-based code generation
- Multi-sensor support (MH-Z19B, PMS7003, BME280)
- ESP32 configuration management

### 3. Reports Module
- Comprehensive data analysis
- Device comparison reports
- Time-based analytics
- Data quality assessment
- Health monitoring

## 📁 Project Structure

```
server/
├── src/
│   ├── config/
│   │   └── config.js              # Server configuration
│   ├── models/
│   │   ├── SensorData.js          # Data model and storage
│   │   └── ArduinoCodeGenerator.js # Code generation logic
│   ├── controllers/
│   │   ├── dashboardController.js  # Dashboard API logic
│   │   ├── arduinoController.js    # Arduino API logic
│   │   └── reportController.js     # Reports API logic
│   ├── routes/
│   │   ├── dashboardRoutes.js      # Dashboard endpoints
│   │   ├── arduinoRoutes.js        # Arduino endpoints
│   │   └── reportRoutes.js         # Reports endpoints
│   ├── middleware/
│   │   ├── errorHandler.js         # Error handling
│   │   └── requestLogger.js        # Request logging
│   ├── utils/
│   │   └── networkUtils.js         # Network utilities
│   ├── app.js                      # Main application
│   └── server.js                   # Server entry point
├── server.js                       # Legacy server (for compatibility)
├── package.json
└── README.md
```

## 🚀 Quick Start

### Installation
```bash
cd server
npm install
```

### Development
```bash
npm run dev          # Start with nodemon (auto-restart)
npm start            # Start production server
npm run start:legacy # Start legacy server
```

### Testing
```bash
# Test all modules
curl http://localhost:3000/api/devices
curl http://localhost:3000/api/arduino/templates
curl http://localhost:3000/api/reports/summary
```

## 📡 API Endpoints

### Dashboard API (`/api/`)
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/devices` | Get list of available devices |
| GET | `/latest-data?device_id=<id>` | Get latest sensor data |
| GET | `/history?device_id=<id>&limit=<n>` | Get sensor data history |
| GET | `/stats?device_id=<id>` | Get statistics |
| POST | `/sensor-data` | Submit sensor data |
| GET | `/dashboard-summary` | Get dashboard summary |

### Arduino API (`/api/arduino/`)
| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/generate-code` | Generate Arduino code |
| GET | `/sensors` | Get available sensors |
| GET | `/templates` | Get code templates |
| POST | `/generate-from-template` | Generate code from template |
| POST | `/validate-config` | Validate configuration |
| GET | `/setup-instructions` | Get setup instructions |

### Reports API (`/api/reports/`)
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/data?device_id=<id>&format=<csv\|json>` | Get report data |
| GET | `/summary` | Get report summary |
| GET | `/device-comparison` | Get device comparison |
| GET | `/time-analysis?interval=<hour\|day\|minute>` | Get time analysis |
| GET | `/sensor-health` | Get sensor health report |

## 🔧 Configuration

### Environment Variables
```bash
PORT=3000                    # Server port
NODE_ENV=development         # Environment mode
```

### Sensor Configuration
The system supports three sensor types:
- **MH-Z19B**: CO2 sensor (co2, minCO2, mhzTemperature, accuracy)
- **PMS7003**: Particulate matter sensor (pm01, pm25, pm10)
- **BME280**: Environmental sensor (temperature, humidity, pressure)

### Arduino Templates
- **Basic**: Simple BME280 sensor reading
- **Environmental**: Complete monitoring (all sensors)
- **Air Quality**: CO2 and particulate matter focus
- **Weather Station**: Temperature, humidity, pressure

## 📊 Data Format

### Sensor Data Submission
```json
{
  "device_id": "DEVICE_1",
  "co2": 420,
  "minCO2": 400,
  "mhzTemperature": 23,
  "accuracy": 95,
  "pm01": 12,
  "pm25": 25,
  "pm10": 35,
  "temperature": 22.5,
  "humidity": 65.2,
  "pressure": 101325
}
```

### Response Format
```json
{
  "device_id": "DEVICE_1",
  "timestamp": "2025-06-03T13:41:56.589Z",
  "status": "online",
  "sensor_data": {
    "co2": 420,
    "temperature": 22.5,
    // ... other sensor readings
  }
}
```

## 🎯 Frontend Integration

### Dashboard Page
```javascript
// Get devices
const devices = await fetch('/api/devices').then(r => r.json());

// Get latest data for specific device
const data = await fetch(`/api/latest-data?device_id=${deviceId}`).then(r => r.json());
```

### Arduino Code Generation Page
```javascript
// Get available templates
const templates = await fetch('/api/arduino/templates').then(r => r.json());

// Generate code from template
const code = await fetch('/api/arduino/generate-from-template', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    templateName: 'environmental',
    deviceId: 'DEVICE_1',
    wifiSSID: 'MyWiFi',
    wifiPassword: 'password123'
  })
}).then(r => r.json());
```

### Reports Page
```javascript
// Get comprehensive report
const report = await fetch('/api/reports/summary').then(r => r.json());

// Get device comparison
const comparison = await fetch('/api/reports/device-comparison').then(r => r.json());
```

## 🔍 Monitoring & Debugging

### Health Check
```bash
curl http://localhost:3000/health
```

### Request Logging
All requests are automatically logged with:
- Timestamp
- Method and path
- Query parameters
- Response time
- Status code

### Error Handling
- Structured error responses
- Validation error details
- Development stack traces
- 404 handling for unknown endpoints

## 🚀 Deployment

### Production Setup
1. Set environment variables
2. Install dependencies: `npm install --production`
3. Start server: `npm start`

### Docker (Optional)
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install --production
COPY src/ ./src/
EXPOSE 3000
CMD ["npm", "start"]
```

## 🤝 Contributing

1. Follow the modular architecture
2. Add new endpoints to appropriate controllers
3. Update routes and documentation
4. Test all three functional modules
5. Maintain backward compatibility

## 📝 License

MIT License - see LICENSE file for details.

---

**Robot Teaching System v2.0** - Empowering education through modular IoT development. 