version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    restart: always
    environment:
      POSTGRES_DB: blockly_db
      POSTGRES_USER: blockly_user
      POSTGRES_PASSWORD: blockly_password
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U blockly_user -d blockly_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  app:
    build: .
    restart: always
    ports:
      - "0.0.0.0:8086:8086"  # Backend port - bind to all interfaces
    environment:
      NODE_ENV: production
      PORT: 8086
      DATABASE_URL: ********************************************************/blockly_db
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - .:/app
      - /app/node_modules

  adminer:
    image: adminer:latest
    restart: always
    ports:
      - "8087:8080"  # Admin panel port (8080 → 8087)
    depends_on:
      - postgres

volumes:
  postgres_data: 