#!/usr/bin/env node

import postgres from 'postgres';
import config from './src/config/config.js';

// Database connection
const connectionString = process.env.DATABASE_URL || 'postgresql://blockly_user:blockly_password@localhost:5432/blockly_db';
const sql = postgres(connectionString);

// UTC+8 timezone offset (8 hours in milliseconds)
const UTC_PLUS_8_OFFSET = 8 * 60 * 60 * 1000;

// Get current time in UTC+8
function getCurrentTimeUTC8() {
  return new Date(Date.now() + UTC_PLUS_8_OFFSET);
}

// Generate realistic sensor data
function generateSensorData(deviceUuid, baseTime = getCurrentTimeUTC8()) {
  const deviceSeed = deviceUuid.charCodeAt(deviceUuid.length - 1) || 65;
  const deviceOffset = (deviceSeed - 65) * 10; // Device-specific offset
  
  // Time-based variations (simulate day/night cycles using UTC+8 time)
  const hour = baseTime.getHours();
  const timeVariation = Math.sin((hour / 24) * 2 * Math.PI) * 0.3;
  
  return {
    // MH-Z19B CO2 Sensor data (prefixed format)
    mhz19b_co2: Math.max(350, Math.floor(450 + deviceOffset + (Math.random() * 600) + (timeVariation * 100))),
    mhz19b_min_co2: Math.max(300, Math.floor(350 + Math.random() * 100)),

    // PMS7003 Particulate Matter Concentration (μg/m³) (prefixed format)
    pms7003_pm01: Math.max(1, Math.floor(5 + (deviceOffset / 10) + Math.random() * 20)),
    pms7003_pm25: Math.max(3, Math.floor(10 + (deviceOffset / 8) + Math.random() * 35)),
    pms7003_pm10: Math.max(5, Math.floor(15 + (deviceOffset / 6) + Math.random() * 45)),

    // PMS7003 Particle Count (#/0.1L) (prefixed format)
    pms7003_n0p3: Math.max(500, Math.floor(1000 + deviceOffset * 10 + Math.random() * 10000)),
    pms7003_n0p5: Math.max(400, Math.floor(800 + deviceOffset * 8 + Math.random() * 8000)),
    pms7003_n1p0: Math.max(300, Math.floor(600 + deviceOffset * 6 + Math.random() * 5000)),
    pms7003_n2p5: Math.max(50, Math.floor(100 + deviceOffset * 2 + Math.random() * 1500)),
    pms7003_n5p0: Math.max(20, Math.floor(50 + deviceOffset + Math.random() * 500)),
    pms7003_n10p0: Math.max(5, Math.floor(10 + (deviceOffset / 2) + Math.random() * 200)),

    // BME280 Environmental data (prefixed format)
    bme280_temperature: Math.max(15, parseFloat((22 + (deviceOffset / 10) + Math.random() * 8 + (timeVariation * 2)).toFixed(1))),
    bme280_humidity: Math.max(20, parseFloat((45 + (deviceOffset / 5) + Math.random() * 30).toFixed(1))),
    bme280_pressure: Math.max(98000, Math.floor(100000 + deviceOffset * 10 + Math.random() * 2000)),

    // Wind sensor data (prefixed format)
    wind_speed: Math.max(0, parseFloat((Math.random() * 25 + (timeVariation * 5)).toFixed(1))),
    
    // LTR390 UV sensor data (prefixed format)
    ltr390_uvi: Math.max(0, parseFloat((Math.random() * 11 + Math.max(0, timeVariation * 3)).toFixed(1))),
    ltr390_lux: Math.max(0, Math.floor(Math.random() * 100000 + Math.max(0, timeVariation * 20000))),
    
    // GPS data (prefixed format)
    gps_quality: Math.floor(Math.random() * 3), // 0-2 quality levels
    gps_satellites: Math.floor(Math.random() * 8 + 4), // 4-12 satellites
    gps_accuracy: Math.max(1, parseFloat((Math.random() * 10 + 2).toFixed(1))),
    gps_altitude: Math.max(0, parseFloat((Math.random() * 1000 + 100).toFixed(1))),
    gps_latitude: parseFloat((22.3193 + (Math.random() - 0.5) * 0.01).toFixed(6)), // Hong Kong area
    gps_longitude: parseFloat((114.1694 + (Math.random() - 0.5) * 0.01).toFixed(6)) // Hong Kong area
  };
}

// Generate realistic log data based on ESP32 operations
function generateLogData(deviceUuid, baseTime = getCurrentTimeUTC8()) {
  const logTypes = ['message', 'error', 'warning'];
  const logMessages = {
    message: [
      'Device initialized successfully',
      'WiFi connection established',
      'Sensor reading completed',
      'Data upload to server successful',
      'BME280 sensor status: OK',
      'PMS7003 sensor status: OK',
      'MHZ19B sensor status: OK',
      'GPS sensor status: OK',
      'LTR390 sensor status: OK',
      'Wind sensor data collected',
      'All sensors functioning normally',
      'Routine data collection cycle completed'
    ],
    error: [
      'WiFi connection failed',
      'Sensor reading timeout',
      'Data upload failed',
      'BME280 sensor communication error',
      'PMS7003 sensor not responding',
      'MHZ19B sensor calibration error',
      'GPS signal lost',
      'LTR390 sensor initialization failed',
      'Memory allocation error',
      'System restart required'
    ],
    warning: [
      'WiFi signal weak',
      'Low memory warning',
      'Sensor reading unstable',
      'GPS accuracy degraded',
      'Temperature sensor drift detected',
      'Humidity reading out of range',
      'CO2 sensor needs calibration',
      'PM sensor cleaning required',
      'Battery level low',
      'System performance degraded'
    ]
  };

  const randomType = logTypes[Math.floor(Math.random() * logTypes.length)];
  const messages = logMessages[randomType];
  const randomMessage = messages[Math.floor(Math.random() * messages.length)];

  return {
    device_uuid: deviceUuid,
    log_type: randomType,
    message: randomMessage,
    timestamp: baseTime
  };
}

async function insertCorrectData() {
  console.log('🚀 Starting sensor data insertion with UTC+8 timestamps...');
  console.log('📝 Configuration: 10 devices, recent time data for real-time testing');
  
  try {
    // 1. Clear existing data from all sensor tables (only core tables now)
    console.log('🧹 Clearing existing data from core sensor tables...');
    const tablesToClear = ['bme280_data', 'pms7003_data', 'mhz19b_data', 'wind_data', 'ltr390_data', 'gps_data', 'device_logs'];
    
    for (const table of tablesToClear) {
      console.log(`  - Clearing ${table}`);
      await sql.unsafe(`TRUNCATE TABLE ${table} RESTART IDENTITY CASCADE`);
    }
    
    // 2. Test devices with recent time data for real-time testing
    const devices = [
      { uuid: 'ESP32_001', records: 200, logs: 25 }, // Recent high frequency data
      { uuid: 'ESP32_002', records: 180, logs: 22 },
      { uuid: 'ESP32_003', records: 160, logs: 20 },
      { uuid: 'ESP32_004', records: 140, logs: 18 },
      { uuid: 'ESP32_005', records: 120, logs: 15 },
      { uuid: 'ESP32_006', records: 100, logs: 12 },
      { uuid: 'ESP32_007', records: 80, logs: 10 },
      { uuid: 'ESP32_008', records: 60, logs: 8 },
      { uuid: 'ESP32_009', records: 40, logs: 6 },
      { uuid: 'ESP32_010', records: 20, logs: 4 }
    ];
    
    // 3. Insert recent data for each device (starting from now and going backwards)
    const currentTimeUTC8 = getCurrentTimeUTC8();
    console.log(`\n🕒 Current UTC+8 time: ${currentTimeUTC8.toLocaleString()}`);
    console.log(`   📊 Generating data for the last 2 hours with realistic intervals`);
    
    for (const device of devices) {
      console.log(`\n📱 Inserting ${device.records} records and ${device.logs} logs for device: ${device.uuid}`);
      
      let cumulativeTimeOffset = 0; // Start from now and go backwards

      for (let i = 0; i < device.records; i++) {
        // Generate realistic intervals: mostly 10-60 seconds, with some faster bursts
        let randomInterval;
        if (Math.random() < 0.2) {
          // 20% chance of fast burst (1-10 seconds)
          randomInterval = Math.floor(Math.random() * 9000) + 1000; // 1-10 seconds
        } else {
          // 80% chance of normal interval (10-60 seconds)
          randomInterval = Math.floor(Math.random() * 50000) + 10000; // 10-60 seconds
        }
        
        cumulativeTimeOffset += randomInterval;
        
        // Create timestamp in UTC+8 (subtract offset from current UTC+8 time)
        const timestamp = new Date(currentTimeUTC8.getTime() - cumulativeTimeOffset);
        const allSensorData = generateSensorData(device.uuid, timestamp);
        
        // Distribute data into each sensor table
        for (const sensorKey in config.sensors) {
          const sensorConfig = config.sensors[sensorKey];
          
          // Random chance to skip this sensor for realistic missing data testing
          const skipProbabilities = {
            'bme280': 0.05,    // BME280 is most reliable
            'pms7003': 0.15,   // PMS7003 occasionally fails
            'mhz19b': 0.08,    // CO2 sensor occasionally fails
            'wind': 0.20,      // Wind sensor can be intermittent
            'ltr390': 0.18,    // UV sensor affected by weather
            'gps': 0.25        // GPS can lose signal
          };
          
          const skipChance = skipProbabilities[sensorKey] || 0.10;
          if (Math.random() < skipChance) {
            continue; // Skip this sensor data
          }
          
          const dataToInsert = { device_uuid: device.uuid, timestamp };
          let hasData = false;

          for (const [prefixedName, columnName] of Object.entries(sensorConfig.mapping)) {
            if (allSensorData.hasOwnProperty(prefixedName)) {
              dataToInsert[columnName] = allSensorData[prefixedName];
              hasData = true;
            }
          }
          
          if (hasData) {
            await sql`INSERT INTO ${sql(sensorConfig.table)} ${sql(dataToInsert)}`;
          }
        }
        
        // Show progress every 50 records or for key records
        if ((i + 1) % 50 === 0 || i === 0 || i === device.records - 1) {
          const minutesAgo = Math.floor(cumulativeTimeOffset / 60000);
          console.log(`  ✓ Record ${i + 1}/${device.records} inserted (${timestamp.toLocaleTimeString()}) - ${minutesAgo} min ago`);
        }
      }

      // Insert log data for the device (realistic log intervals)
      let logCumulativeOffset = 0;
      for (let i = 0; i < device.logs; i++) {
        // Generate log intervals between 1-5 minutes
        const logInterval = Math.floor(Math.random() * 240000) + 60000; // 1-5 minutes
        logCumulativeOffset += logInterval;
        
        const logTimestamp = new Date(currentTimeUTC8.getTime() - logCumulativeOffset);
        const logData = generateLogData(device.uuid, logTimestamp);
        
        await sql`INSERT INTO device_logs ${sql(logData)}`;
        
        // Show log progress
        if ((i + 1) % 10 === 0 || i === 0 || i === device.logs - 1) {
          console.log(`  ✓ Log ${i + 1}/${device.logs} inserted (${logData.log_type}: ${logData.message})`);
        }
      }
    }
    
    // 4. Verify insertion and show time range
    const totalRecords = await sql`SELECT COUNT(*) as count FROM bme280_data`;
    const totalDevices = await sql`SELECT COUNT(DISTINCT device_uuid) as count FROM bme280_data`;
    
    // Get time range of inserted data
    const timeRange = await sql`
      SELECT 
        MIN(timestamp) as earliest_time,
        MAX(timestamp) as latest_time
      FROM bme280_data
    `;
    
    console.log('\n📊 Data insertion summary:');
    console.log(`✅ Total records in bme280_data (example): ${totalRecords[0].count}`);
    console.log(`✅ Total devices: ${totalDevices[0].count}`);
    
    if (timeRange[0] && timeRange[0].earliest_time) {
      const earliestTime = new Date(timeRange[0].earliest_time);
      const latestTime = new Date(timeRange[0].latest_time);
      const timeSpan = latestTime.getTime() - earliestTime.getTime();
      
      console.log(`\n🕒 Time range of data (UTC+8):`);
      console.log(`   📅 Earliest: ${earliestTime.toLocaleString()}`);
      console.log(`   📅 Latest: ${latestTime.toLocaleString()}`);
      console.log(`   ⏱️  Total span: ${(timeSpan / 3600000).toFixed(1)} hours`);
      console.log(`   📊 This recent data is perfect for real-time dashboard testing!`);
    }
    
    // Show record counts per table
    console.log('\n📋 Records per sensor table:');
    for (const sensorKey in config.sensors) {
      const sensorConfig = config.sensors[sensorKey];
      const tableCount = await sql`SELECT COUNT(*) as count FROM ${sql(sensorConfig.table)}`;
      console.log(`  ${sensorConfig.table}: ${tableCount[0].count} records`);
    }
    
    // Show log count
    const logCount = await sql`SELECT COUNT(*) as count FROM device_logs`;
    console.log(`  device_logs: ${logCount[0].count} records`);
    
    const deviceCounts = await sql`
      SELECT device_uuid, COUNT(*) as count 
      FROM bme280_data 
      GROUP BY device_uuid 
      ORDER BY device_uuid
    `;
    
    console.log('\n📋 Records per device (from bme280_data):');
    deviceCounts.forEach(row => {
      console.log(`  ${row.device_uuid}: ${row.count} records`);
    });
    
    console.log('\n🎉 Recent time data insertion completed successfully!');
    console.log('🔧 Data is ready for real-time dashboard testing with proper UTC+8 timestamps!');
    
  } catch (error) {
    console.error('❌ Error inserting data:', error);
  } finally {
    await sql.end();
  }
}

// Run the script
insertCorrectData(); 