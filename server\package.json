{"name": "robot-teaching-system-server", "version": "2.0.0", "type": "module", "description": "Robot Teaching System - Comprehensive sensor data collection and Arduino code generation server", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "start:legacy": "node server.js", "test": "echo \"Error: no test specified\" && exit 1", "db:generate": "drizzle-kit generate:pg", "db:push": "drizzle-kit push:pg", "db:studio": "drizzle-kit studio", "db:migrate": "node src/scripts/migrate.js", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "docker:db": "docker-compose up -d postgres", "docker:clean": "docker-compose down -v --remove-orphans", "docker:reset": "docker-compose down -v --remove-orphans && docker-compose up -d"}, "keywords": ["robot", "sensors", "a<PERSON><PERSON><PERSON>", "esp32", "iot", "education", "teaching"], "author": "Robot Teaching System", "license": "MIT", "dependencies": {"axios": "^1.10.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "drizzle-orm": "^0.29.0", "express": "^4.18.2", "postgres": "^3.4.3"}, "devDependencies": {"drizzle-kit": "^0.20.4", "nodemon": "^3.0.1", "pg": "^8.16.0"}, "engines": {"node": ">=14.0.0"}}