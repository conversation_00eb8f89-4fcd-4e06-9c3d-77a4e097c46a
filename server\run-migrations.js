#!/usr/bin/env node

import postgres from 'postgres';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Database connection
const connectionString = process.env.DATABASE_URL || 'postgresql://blockly_user:blockly_password@localhost:5432/blockly_db';
const sql = postgres(connectionString);

async function runMigrations() {
  console.log('🚀 Starting database migrations...');
  
  try {
    // Create migrations tracking table if it doesn't exist
    await sql`
      CREATE TABLE IF NOT EXISTS migrations (
        id SERIAL PRIMARY KEY,
        filename TEXT NOT NULL UNIQUE,
        executed_at TIMESTAMP NOT NULL DEFAULT NOW()
      )
    `;
    
    // Get list of migration files
    const migrationsDir = path.join(__dirname, 'src', 'db', 'migrations');
    const migrationFiles = fs.readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort();
    
    console.log(`📁 Found ${migrationFiles.length} migration files`);
    
    // Get already executed migrations
    const executedMigrations = await sql`
      SELECT filename FROM migrations ORDER BY executed_at
    `;
    const executedFilenames = executedMigrations.map(row => row.filename);
    
    console.log(`✅ Already executed: ${executedFilenames.length} migrations`);
    
    // Run pending migrations
    let executedCount = 0;
    for (const filename of migrationFiles) {
      if (executedFilenames.includes(filename)) {
        console.log(`⏭️  Skipping ${filename} (already executed)`);
        continue;
      }
      
      console.log(`🔄 Executing migration: ${filename}`);
      
      // Read migration file
      const migrationPath = path.join(migrationsDir, filename);
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      
      // Execute migration in a transaction
      await sql.begin(async sql => {
        // Execute the migration SQL
        await sql.unsafe(migrationSQL);
        
        // Record the migration as executed
        await sql`
          INSERT INTO migrations (filename) VALUES (${filename})
        `;
      });
      
      console.log(`✅ Successfully executed: ${filename}`);
      executedCount++;
    }
    
    if (executedCount === 0) {
      console.log('🎉 No new migrations to execute. Database is up to date!');
    } else {
      console.log(`🎉 Successfully executed ${executedCount} new migrations!`);
    }
    
    // Show final migration status
    const totalMigrations = await sql`SELECT COUNT(*) as count FROM migrations`;
    console.log(`📊 Total migrations executed: ${totalMigrations[0].count}`);
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await sql.end();
  }
}

// Run migrations
runMigrations();
