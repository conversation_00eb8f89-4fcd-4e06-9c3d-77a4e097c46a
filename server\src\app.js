import express from 'express';
import cors from 'cors';
import config from './config/config.js';
import { errorHandler, notFoundHandler } from './middleware/errorHandler.js';
import requestLogger from './middleware/requestLogger.js';
import { getLocalIP, generateServerURLs } from './utils/networkUtils.js';

// Controllers
import ApiController from './controllers/apiController.js';

// Routes
import createApiRoutes from './routes/apiRoutes.js';

class SimplifiedSensorServer {
  constructor() {
    this.app = express();
    this.port = config.server.port;
    this.localIP = getLocalIP();
    
    // Initialize controller
    this.apiController = new ApiController();
    
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  setupMiddleware() {
    // CORS configuration
    this.app.use(cors({
      origin: config.server.cors.origins,
      credentials: config.server.cors.credentials
    }));

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request logging
    this.app.use(requestLogger);
  }

  setupRoutes() {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: process.version
      });
    });

    // Root endpoint with API information
    this.app.get('/', async (req, res) => {
      const serverURLs = generateServerURLs(this.port);
      
      res.json({
        message: 'Blockly Robot Teaching System API Server',
        version: '3.0.0',
        status: 'online',
        database: 'PostgreSQL',
        server_urls: serverURLs,
        api_endpoints: {
          'POST /api/grouped/data': 'Store grouped sensor data from ESP32',
          'GET /api/grouped/latest?uuid=<uuid>': 'Get latest grouped sensor data for device',
          'GET /api/grouped/devices/summary': 'Get devices summary with statistics',
          'GET /api/grouped/devices/<uuid>/history': 'Get unified historical data for single device',
          'DELETE /api/grouped/data/<uuid>': 'Delete device data from all grouped sensor tables',
        },
        server_info: {
          node_version: process.version,
          platform: process.platform,
          arch: process.arch,
          uptime: process.uptime()
        }
      });
    });

    // Main API routes
    const apiRouter = createApiRoutes(this.apiController);
    this.app.use('/api', apiRouter);
    this.app.use('/', apiRouter); // Also mount at root for compatibility
  }

  setupErrorHandling() {
    // 404 handler
    this.app.use(notFoundHandler);
    
    // Error handler
    this.app.use(errorHandler);
  }

  async start() {
    try {
      return new Promise(async (resolve, reject) => {
        this.server = this.app.listen(this.port, config.server.host, async () => {
          await this.printStartupInfo();
          resolve(this.server);
        });

        this.server.on('error', (error) => {
          console.error('Server error:', error);
          reject(error);
        });
      });
      } catch (error) {
      console.error('Failed to initialize server:', error);
      throw error;
      }
  }

  stop() {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          console.log('Server stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  async printStartupInfo() {
    const serverURLs = generateServerURLs(this.port);
    
    console.log('🚀 Blockly Robot Teaching System API Server v3.0 Started!');
    console.log('');
    console.log('📡 Server URLs:');
    serverURLs.forEach(url => {
      console.log(`   ${url}`);
    });
    console.log('');
    console.log('📋 API Endpoints:');
    console.log('   POST  /api/grouped/data                    - Store grouped sensor data');
    console.log('   GET  /api/grouped/latest?uuid=X           - Get latest grouped data for device');
    console.log('   GET  /api/grouped/devices/summary         - Get devices summary with statistics');
    console.log('   GET  /api/grouped/devices/:uuid/history   - Get unified device history');
    console.log('   DELETE /api/grouped/data/:uuid            - Delete device data from all tables');
    console.log('');
    console.log('💡 ESP32 Example:');
    console.log(`   curl -X POST ${serverURLs[0]}/api/grouped/data \\`);
    console.log('        -H "Content-Type: application/json" \\');
    console.log('        -d \'{"uuid":"ESP32_001","mhz19b_co2":400,"bme280_temperature":25.5}\'');
    console.log('');
    console.log('💡 API Usage Examples:');
    console.log(`   curl "${serverURLs[0]}/api/grouped/devices/summary"`);
    console.log(`   curl "${serverURLs[0]}/api/grouped/devices/ESP32_001/history?pageSize=20"`);
    console.log(`   curl "${serverURLs[0]}/api/grouped/latest?uuid=ESP32_001"`);
    console.log('');
    console.log('Waiting for connections...');
  }

  // Getter methods for testing
  getApiController() {
    return this.apiController;
  }
}

export default SimplifiedSensorServer; 