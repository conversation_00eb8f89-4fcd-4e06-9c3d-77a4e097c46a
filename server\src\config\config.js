import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const config = {
  server: {
    port: process.env.PORT || 8086,
    host: '0.0.0.0',
    cors: {
      origins: [
        'http://localhost:8085',  // Frontend on localhost
        'http://localhost:8086',  // Backend on localhost  
        'http://**********:8085', // Frontend on public IP
        'http://**********:8086'  // Backend on public IP
      ],
      credentials: true
    }
  },
  
  database: {
    // Database configuration - PostgreSQL required
    connectionString: process.env.DATABASE_URL || 'postgresql://blockly_user:blockly_password@localhost:5432/blockly_db',
    
    // Connection pool settings
    pool: {
      min: 2,
      max: 10
    }
  },
  
  sensors: {
    // Sensor data validation schemas - Updated to support prefixed format from ESP32
    // Removed latestTable references as part of architecture optimization
    mhz19b: {
      fields: ['mhz19b_co2', 'mhz19b_min_co2'],
      table: 'mhz19b_data',
      mapping: {
        'mhz19b_co2': 'co2',
        'mhz19b_min_co2': 'min_co2'
      }
    },
    pms7003: {
      fields: ['pms7003_pm01', 'pms7003_pm25', 'pms7003_pm10', 'pms7003_n0p3', 'pms7003_n0p5', 'pms7003_n1p0', 'pms7003_n2p5', 'pms7003_n5p0', 'pms7003_n10p0'],
      table: 'pms7003_data',
      mapping: {
        'pms7003_pm01': 'pm01',
        'pms7003_pm25': 'pm25',
        'pms7003_pm10': 'pm10',
        'pms7003_n0p3': 'n0p3',
        'pms7003_n0p5': 'n0p5',
        'pms7003_n1p0': 'n1p0',
        'pms7003_n2p5': 'n2p5',
        'pms7003_n5p0': 'n5p0',
        'pms7003_n10p0': 'n10p0'
      }
    },
    bme280: {
      fields: ['bme280_temperature', 'bme280_humidity', 'bme280_pressure'],
      table: 'bme280_data',
      mapping: {
        'bme280_temperature': 'temperature',
        'bme280_humidity': 'humidity',
        'bme280_pressure': 'pressure'
      }
    },
    // Additional sensors with prefixed format
    wind: {
      fields: ['wind_speed'],
      table: 'wind_data',
      mapping: {
        'wind_speed': 'speed'
      }
    },
    ltr390: {
      fields: ['ltr390_uvi', 'ltr390_lux'],
      table: 'ltr390_data',
      mapping: {
        'ltr390_uvi': 'uvi',
        'ltr390_lux': 'lux'
      }
    },
    gps: {
      fields: ['gps_quality', 'gps_satellites', 'gps_accuracy', 'gps_altitude', 'gps_latitude', 'gps_longitude'],
      table: 'gps_data',
      mapping: {
        'gps_quality': 'quality',
        'gps_satellites': 'satellites',
        'gps_accuracy': 'accuracy',
        'gps_altitude': 'altitude',
        'gps_latitude': 'latitude',
        'gps_longitude': 'longitude'
      }
    },
  },
  
  arduino: {
    // Arduino code generation settings
    defaultBaudRate: 115200,
    defaultWifiTimeout: 10000,
    defaultSensorInterval: 5000
  },
  
  api: {
    // API configuration
    defaultPageSize: 50,
    maxPageSize: 1000
  }
};

export default config; 