import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export const databaseConfig = {
  // Database connection string
  connectionString: process.env.DATABASE_URL || 'postgresql://username:password@localhost:5432/blockly_db',
  
  // Migration settings
  migrations: {
    directory: './src/db/migrations',
    schema: './src/db/schema.js'
  },
  
  // Connection pool settings
  pool: {
    min: 2,
    max: 10
  },
  
  // Enable database logging in development
  logging: process.env.NODE_ENV === 'development'
}; 