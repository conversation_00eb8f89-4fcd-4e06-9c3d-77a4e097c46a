import config from '../config/config.js';
import GroupedSensorDataService from '../models/GroupedSensorDataService.js';

class ApiController {
  constructor(sensorDataModel) {
    this.groupedSensorData = new GroupedSensorDataService();
  }

  // === NEW GROUPED SENSOR DATA ENDPOINTS ===

  // POST grouped sensor data from ESP32 (new structured approach)
  async postGroupedSensorData(req, res) {
    try {
      console.log('Grouped sensor data received:', req.body);
      const result = await this.groupedSensorData.storeGroupedSensorData(req.body);

      if (result.status === 'success') {
        res.status(200).json({ status: 'success', message: result.message });
      } else {
        res.status(400).json({ status: 'failure', error: result.error });
      }
    } catch (error) {
      console.error('Error in postGroupedSensorData:', error);
      res.status(500).json({ status: 'failure', error: 'Internal server error' });
    }
  }

  // GET latest grouped sensor data for a device
  async getLatestGroupedData(req, res) {
    try {
      const { uuid } = req.query;

      if (!uuid) {
        return res.status(400).json({ status: 'failure', error: 'UUID parameter is required' });
      }

      const result = await this.groupedSensorData.getLatestGroupedData(uuid);

      if (result.status === 'success') {
        res.status(200).json({ row: result.data });
      } else {
        res.status(404).json({ status: 'failure', error: result.error });
      }
    } catch (error) {
      console.error('Error in getLatestGroupedData:', error);
      res.status(500).json({ status: 'failure', error: 'Internal server error' });
    }
  }

  // DELETE device data from all grouped sensor tables
  async deleteGroupedDeviceData(req, res) {
    try {
      const { uuid } = req.params;

      if (!uuid) {
        return res.status(400).json({ status: 'failure', error: 'UUID parameter is required' });
      }

      const result = await this.groupedSensorData.deleteDeviceData(uuid);

      if (result.status === 'success') {
        res.status(200).json({ status: 'success' });
      } else {
        res.status(404).json({ status: 'failure', error: result.error });
      }
    } catch (error) {
      console.error('Error in deleteGroupedDeviceData:', error);
      res.status(500).json({ status: 'failure', error: 'Internal server error' });
    }
  }

  // GET devices summary with statistics
  async getGroupedDevicesSummary(req, res) {
    try {
      const result = await this.groupedSensorData.getDevicesSummary();

      if (result.status === 'success') {
        res.status(200).json(result.devices);
      } else {
        res.status(500).json({ status: 'failure', error: result.error });
      }
    } catch (error) {
      console.error('Error in getGroupedDevicesSummary:', error);
      res.status(500).json({ status: 'failure', error: 'Internal server error' });
    }
  }

  // GET lightweight devices info for polling
  async getGroupedDevicesBasicInfo(req, res) {
    try {
      const result = await this.groupedSensorData.getDevicesBasicInfo();

      if (result.status === 'success') {
        res.status(200).json(result.devices);
      } else {
        res.status(500).json({ status: 'failure', error: result.error });
      }
    } catch (error) {
      console.error('Error in getGroupedDevicesBasicInfo:', error);
      res.status(500).json({ status: 'failure', error: 'Internal server error' });
    }
  }

  // GET unified historical data for a single device
  async getGroupedDeviceHistory(req, res) {
    try {
      const { uuid } = req.params;
      const {
        pageSize = config.api.defaultPageSize,
        offset = 0,
        startDate,
        endDate,
        includeSensors,
        timeWindow = 0 // Default 0ms (no aggregation)
      } = req.query;

      if (!uuid) {
        return res.status(400).json({ status: 'failure', error: 'Device UUID is required' });
      }

      const filters = {
        pageSize: parseInt(pageSize),
        offset: parseInt(offset),
        startDate,
        endDate,
        includeSensors: includeSensors ? includeSensors.split(',') : null,
        timeWindow: parseInt(timeWindow)
      };

      const result = await this.groupedSensorData.getDeviceUnifiedHistory(uuid, filters);

      if (result.status === 'success') {
        res.status(200).json(result);
      } else {
        res.status(400).json({ status: 'failure', error: result.error });
      }
    } catch (error) {
      console.error('Error in getGroupedDeviceHistory:', error);
      res.status(500).json({ status: 'failure', error: 'Internal server error' });
    }
  }

  // POST log data from ESP32
  async postLogData(req, res) {
    try {
      console.log('Log data received:', req.body);
      const result = await this.groupedSensorData.storeLogData(req.body);

      if (result.status === 'success') {
        res.status(200).json({ status: 'success', message: result.message });
      } else {
        res.status(400).json({ status: 'failure', error: result.error });
      }
    } catch (error) {
      console.error('Error in postLogData:', error);
      res.status(500).json({ status: 'failure', error: 'Internal server error' });
    }
  }

  // GET log data for a specific device
  async getDeviceLogs(req, res) {
    try {
      const { uuid } = req.params;
      const { limit = 100, offset = 0 } = req.query;

      if (!uuid) {
        return res.status(400).json({ status: 'failure', error: 'Device UUID is required' });
      }

      const result = await this.groupedSensorData.getDeviceLogs(uuid, {
        limit: parseInt(limit),
        offset: parseInt(offset)
      });

      if (result.status === 'success') {
        res.status(200).json({ status: 'success', logs: result.logs });
      } else {
        res.status(400).json({ status: 'failure', error: result.error });
      }
    } catch (error) {
      console.error('Error in getDeviceLogs:', error);
      res.status(500).json({ status: 'failure', error: 'Internal server error' });
    }
  }
}

export default ApiController;