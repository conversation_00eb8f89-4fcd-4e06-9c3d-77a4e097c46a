CREATE TABLE IF NOT EXISTS "bme280_data" (
	"id" serial PRIMARY KEY NOT NULL,
	"device_uuid" text NOT NULL,
	"timestamp" timestamp NOT NULL,
	"temperature" real,
	"humidity" real,
	"pressure" real,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "device_logs" (
	"id" serial PRIMARY KEY NOT NULL,
	"device_uuid" text NOT NULL,
	"timestamp" timestamp NOT NULL,
	"log_type" text NOT NULL,
	"message" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "gps_data" (
	"id" serial PRIMARY KEY NOT NULL,
	"device_uuid" text NOT NULL,
	"timestamp" timestamp NOT NULL,
	"latitude" real,
	"longitude" real,
	"altitude" real,
	"quality" real,
	"satellites" real,
	"accuracy" real,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "ltr390_data" (
	"id" serial PRIMARY KEY NOT NULL,
	"device_uuid" text NOT NULL,
	"timestamp" timestamp NOT NULL,
	"uvi" real,
	"lux" real,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "mhz19b_data" (
	"id" serial PRIMARY KEY NOT NULL,
	"device_uuid" text NOT NULL,
	"timestamp" timestamp NOT NULL,
	"co2" integer,
	"min_co2" integer,
	"temperature" real,
	"accuracy" integer,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "pms7003_data" (
	"id" serial PRIMARY KEY NOT NULL,
	"device_uuid" text NOT NULL,
	"timestamp" timestamp NOT NULL,
	"pm01" integer,
	"pm25" integer,
	"pm10" integer,
	"n0p3" integer,
	"n0p5" integer,
	"n1p0" integer,
	"n2p5" integer,
	"n5p0" integer,
	"n10p0" integer,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "wind_data" (
	"id" serial PRIMARY KEY NOT NULL,
	"device_uuid" text NOT NULL,
	"timestamp" timestamp NOT NULL,
	"speed" real,
	"created_at" timestamp DEFAULT now() NOT NULL
);

-- Performance indexes for optimized latest data queries
CREATE INDEX IF NOT EXISTS idx_bme280_data_device_timestamp ON "bme280_data" (device_uuid, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_gps_data_device_timestamp ON "gps_data" (device_uuid, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_ltr390_data_device_timestamp ON "ltr390_data" (device_uuid, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_mhz19b_data_device_timestamp ON "mhz19b_data" (device_uuid, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_pms7003_data_device_timestamp ON "pms7003_data" (device_uuid, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_wind_data_device_timestamp ON "wind_data" (device_uuid, timestamp DESC);
CREATE INDEX IF NOT EXISTS idx_device_logs_device_timestamp ON "device_logs" (device_uuid, timestamp DESC);