import { pgTable, serial, text, timestamp, real, integer } from 'drizzle-orm/pg-core';

// Schema for historical BME280 sensor data
export const bme280Data = pgTable('bme280_data', {
  id: serial('id').primaryKey(),
  deviceUuid: text('device_uuid').notNull(),
  timestamp: timestamp('timestamp').notNull(),
  temperature: real('temperature'),
  humidity: real('humidity'),
  pressure: real('pressure'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// Schema for device logs
export const deviceLogs = pgTable('device_logs', {
  id: serial('id').primaryKey(),
  deviceUuid: text('device_uuid').notNull(),
  timestamp: timestamp('timestamp').defaultNow().notNull(),
  logType: text('log_type').notNull(), // e.g., 'message', 'warning', 'error'
  message: text('message').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// Schema for historical GPS data
export const gpsData = pgTable('gps_data', {
  id: serial('id').primaryKey(),
  deviceUuid: text('device_uuid').notNull(),
  timestamp: timestamp('timestamp').notNull(),
  latitude: real('latitude'),
  longitude: real('longitude'),
  altitude: real('altitude'),
  quality: real('quality'),
  satellites: real('satellites'),
  accuracy: real('accuracy'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// Schema for historical LTR390 UV sensor data
export const ltr390Data = pgTable('ltr390_data', {
  id: serial('id').primaryKey(),
  deviceUuid: text('device_uuid').notNull(),
  timestamp: timestamp('timestamp').notNull(),
  uvi: real('uvi'),
  lux: real('lux'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// Schema for historical MH-Z19B CO2 sensor data
export const mhz19bData = pgTable('mhz19b_data', {
  id: serial('id').primaryKey(),
  deviceUuid: text('device_uuid').notNull(),
  timestamp: timestamp('timestamp').notNull(),
  co2: integer('co2'),
  minCo2: integer('min_co2'),
  temperature: real('temperature'),
  accuracy: integer('accuracy'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// Schema for historical PMS7003 particulate matter sensor data
export const pms7003Data = pgTable('pms7003_data', {
  id: serial('id').primaryKey(),
  deviceUuid: text('device_uuid').notNull(),
  timestamp: timestamp('timestamp').notNull(),
  pm01: integer('pm01'),
  pm25: integer('pm25'),
  pm10: integer('pm10'),
  n0p3: integer('n0p3'),
  n0p5: integer('n0p5'),
  n1p0: integer('n1p0'),
  n2p5: integer('n2p5'),
  n5p0: integer('n5p0'),
  n10p0: integer('n10p0'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// Schema for historical wind sensor data
export const windData = pgTable('wind_data', {
  id: serial('id').primaryKey(),
  deviceUuid: text('device_uuid').notNull(),
  timestamp: timestamp('timestamp').notNull(),
  speed: real('speed'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});