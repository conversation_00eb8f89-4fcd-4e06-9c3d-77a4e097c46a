// Request logging middleware
const requestLogger = (req, res, next) => {
  const start = Date.now();
  
  // Log request
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.path}`);
  
  // Log query parameters if present
  if (Object.keys(req.query).length > 0) {
    console.log('  Query:', req.query);
  }
  
  // Log body for POST/PUT requests (but not for sensor data to avoid spam)
  if ((req.method === 'POST' || req.method === 'PUT') && 
      !req.path.includes('/sensor-data') && 
      req.body && Object.keys(req.body).length > 0) {
    console.log('  Body:', req.body);
  }

  // Override res.json to log response time
  const originalJson = res.json;
  res.json = function(data) {
    const duration = Date.now() - start;
    console.log(`[${new Date().toISOString()}] ${req.method} ${req.path} - ${res.statusCode} (${duration}ms)`);
    return originalJson.call(this, data);
  };

  next();
};

export default requestLogger; 