import postgres from 'postgres';
import config from '../config/config.js';

class GroupedSensorDataService {
  constructor() {
    this.sql = postgres(config.database.connectionString, {
      ...config.database.pool,
      types: {
        // Treat `timestamp without time zone` (oid 1114) as a string to bypass parsing issues.
        // This ensures we get the raw timestamp string as it is stored in the database.
        1114: {
          to: 1114,
          from: [1114],
          serialize: (x) => x,
          parse: (x) => x,
        },
      },
    });
  }

  /**
   * Process and store grouped sensor data from ESP32
   * @param {Object} data - Sensor data with prefixed field names
   * @returns {Object} - Result object with status
   */
  async storeGroupedSensorData(data) {
    try {
      const { uuid, timestamp: espTimestamp, ...sensorData } = data;

      if (!uuid) {
        return { status: 'error', error: 'Device UUID is required' };
      }

      const timestamp = espTimestamp ? new Date(espTimestamp) : new Date();
      const results = [];

      // Process each sensor group
      for (const [sensorType, sensorConfig] of Object.entries(config.sensors)) {
        const sensorFields = this.extractSensorFields(sensorData, sensorConfig.fields);
        
        if (Object.keys(sensorFields).length > 0) {
          // Map prefixed field names to database column names
          const mappedFields = this.mapFieldNames(sensorFields, sensorConfig.mapping);
          
          // Store in sensor-specific table
          const result = await this.storeSensorGroup(
            sensorConfig.table,
            uuid,
            timestamp,
            mappedFields
          );
          
          results.push({ sensor: sensorType, result });
        }
      }

      return { 
        status: 'success', 
        message: `Stored data for ${results.length} sensor groups`,
        details: results
      };

    } catch (error) {
      console.error('Error storing grouped sensor data:', error);
      return { status: 'error', error: error.message };
    }
  }

  /**
   * Extract fields for a specific sensor from the data
   * @param {Object} data - All sensor data
   * @param {Array} fields - Expected field names for this sensor
   * @returns {Object} - Filtered sensor data
   */
  extractSensorFields(data, fields) {
    const sensorData = {};
    
    for (const field of fields) {
      if (data.hasOwnProperty(field) && data[field] !== null && data[field] !== undefined) {
        sensorData[field] = data[field];
      }
    }
    
    return sensorData;
  }

  /**
   * Map prefixed field names to database column names
   * @param {Object} sensorData - Sensor data with prefixed names
   * @param {Object} mapping - Field name mapping
   * @returns {Object} - Mapped sensor data
   */
  mapFieldNames(sensorData, mapping) {
    const mappedData = {};
    
    for (const [prefixedName, value] of Object.entries(sensorData)) {
      const columnName = mapping[prefixedName];
      if (columnName) {
        mappedData[columnName] = value;
      }
    }
    
    return mappedData;
  }

  /**
   * Store sensor data in the appropriate sensor table
   * Simplified version that only stores in main table (latest_* tables removed)
   * @param {string} tableName - Main sensor table name
   * @param {string} uuid - Device UUID
   * @param {Date} timestamp - Data timestamp
   * @param {Object} sensorData - Mapped sensor data
   * @returns {Object} - Result object
   */
  async storeSensorGroup(tableName, uuid, timestamp, sensorData) {
    try {
      // Prepare data for insertion
      const insertData = {
        device_uuid: uuid,
        timestamp: timestamp,
        ...sensorData
      };

      // Insert into main sensor table only
      await this.sql`
        INSERT INTO ${this.sql(tableName)} ${this.sql(insertData)}
      `;

      return { status: 'success', recordsInserted: 1 };

    } catch (error) {
      console.error(`Error storing ${tableName} data:`, error);
      return { status: 'error', error: error.message };
    }
  }

  /**
   * Get latest sensor data for a device (combined from all sensor tables)
   * Uses optimized individual queries with window functions (avoiding UNION issues)
   * @param {string} uuid - Device UUID
   * @returns {Object} - Combined latest sensor data
   */
  async getLatestGroupedData(uuid) {
    try {
      const combinedData = { uuid };
      let latestTimestamp = null;

      // Use individual optimized queries instead of UNION to avoid column mismatch issues
      // Each query uses window functions for optimal performance
      for (const [, sensorConfig] of Object.entries(config.sensors)) {
        const query = `
          SELECT device_uuid, timestamp, ${Object.values(sensorConfig.mapping).join(', ')}
          FROM (
            SELECT device_uuid, timestamp, ${Object.values(sensorConfig.mapping).join(', ')},
                   ROW_NUMBER() OVER (PARTITION BY device_uuid ORDER BY timestamp DESC) as rn
            FROM ${sensorConfig.table}
            WHERE device_uuid = $1
          ) ranked
          WHERE rn = 1
        `;

        const results = await this.sql.unsafe(query, [uuid]);

        if (results.length > 0) {
          const data = results[0];

          // Map database column names back to prefixed field names
          for (const [prefixedName, columnName] of Object.entries(sensorConfig.mapping)) {
            if (data[columnName] !== null && data[columnName] !== undefined) {
              combinedData[prefixedName] = data[columnName];
            }
          }

          // Track the most recent timestamp across all sensors
          if (!latestTimestamp || new Date(data.timestamp) > new Date(latestTimestamp)) {
            latestTimestamp = data.timestamp;
          }
        }
      }

      if (Object.keys(combinedData).length === 1) {
        return { status: 'error', error: 'No data found for device' };
      }

      // Set the latest timestamp
      if (latestTimestamp) {
        combinedData.timestamp = new Date(latestTimestamp).toISOString();
      }

      return { status: 'success', data: combinedData };

    } catch (error) {
      console.error('Error getting latest grouped data:', error);
      return { status: 'error', error: error.message };
    }
  }

  /**
   * Delete all data for a specific device from all sensor tables
   * @param {string} uuid - Device UUID
   * @returns {Object} - Result object
   */
  async deleteDeviceData(uuid) {
    try {
      const results = [];

      // Delete from each sensor table (only main tables now)
      for (const [sensorType, sensorConfig] of Object.entries(config.sensors)) {
        // Delete from main table
        const mainResult = await this.sql`
          DELETE FROM ${this.sql(sensorConfig.table)}
          WHERE device_uuid = ${uuid}
        `;

        results.push({
          sensor: sensorType,
          recordsDeleted: mainResult.count
        });
      }

      return { status: 'success', results };

    } catch (error) {
      console.error('Error deleting device data:', error);
      return { status: 'error', error: error.message };
    }
  }

  /**
   * Store log data from a device
   * @param {Object} logData - The log data containing uuid, logType, and message
   * @returns {Object} - Result object
   */
  async storeLogData(logData) {
    try {
      const { uuid, timestamp: espTimestamp, logType, message } = logData;

      if (!uuid || !logType || !message) {
        return { status: 'error', error: 'UUID, logType, and message are required' };
      }

      const insertData = {
        device_uuid: uuid,
        log_type: logType,
        message: message,
        timestamp: espTimestamp ? new Date(espTimestamp) : new Date(),
      };

      await this.sql`
        INSERT INTO device_logs ${this.sql(insertData)}
      `;

      return { status: 'success', message: 'Log stored successfully' };
    } catch (error) {
      console.error('Error storing log data:', error);
      return { status: 'error', error: error.message };
    }
  }

  /**
   * Get log data for a specific device
   * @param {string} uuid - Device UUID
   * @param {Object} options - Query options (limit, offset)
   * @returns {Object} - Result object with logs
   */
  async getDeviceLogs(uuid, options = {}) {
    try {
      const { limit = 100, offset = 0 } = options;

      const logs = await this.sql`
        SELECT * FROM device_logs
        WHERE device_uuid = ${uuid}
        ORDER BY timestamp DESC
        LIMIT ${limit}
        OFFSET ${offset}
      `;

      // Convert timestamp to ISO string for frontend consistency
      const processedLogs = logs.map(log => ({
        ...log,
        timestamp: new Date(log.timestamp).toISOString()
      }));

      return { status: 'success', logs: processedLogs };
    } catch (error) {
      console.error('Error getting device logs:', error);
      return { status: 'error', error: error.message };
    }
  }

  /**
   * Close database connection
   */
  async close() {
    await this.sql.end();
  }

  /**
   * Get device summary with statistics from all sensor tables
   * @returns {Object} - Array of device summaries
   */
  async getDevicesSummary() {
    try {
      const deviceMap = new Map();

      // Collect device information from all sensor tables
      for (const [sensorType, sensorConfig] of Object.entries(config.sensors)) {
        // Get device UUIDs and record counts
        const deviceStats = await this.sql`
          SELECT 
            device_uuid,
            COUNT(*) as record_count,
            MIN(timestamp) as earliest_timestamp,
            MAX(timestamp) as latest_timestamp
          FROM ${this.sql(sensorConfig.table)}
          GROUP BY device_uuid
        `;

        deviceStats.forEach(stat => {
          const uuid = stat.device_uuid;
          if (!deviceMap.has(uuid)) {
            deviceMap.set(uuid, {
              uuid,
              totalRecords: 0,
              lastUpdateTime: null,
              earliestRecord: null,
              availableSensors: []
            });
          }

          const device = deviceMap.get(uuid);
          device.totalRecords += parseInt(stat.record_count, 10);
          device.availableSensors.push(sensorType);
          
          if (!device.lastUpdateTime || new Date(stat.latest_timestamp) > new Date(device.lastUpdateTime)) {
            device.lastUpdateTime = stat.latest_timestamp;
          }
          
          if (!device.earliestRecord || new Date(stat.earliest_timestamp) < new Date(device.earliestRecord)) {
            device.earliestRecord = stat.earliest_timestamp;
          }
        });
      }
      
      const devices = Array.from(deviceMap.values()).map(d => {
        if (d.lastUpdateTime) d.lastUpdateTime = new Date(d.lastUpdateTime).toISOString();
        if (d.earliestRecord) d.earliestRecord = new Date(d.earliestRecord).toISOString();
        return d;
      });

      return { status: 'success', devices };

    } catch (error) {
      console.error('Error getting devices summary:', error);
      return { status: 'error', error: error.message };
    }
  }

  /**
   * Get unified historical data for a single device from all sensor tables
   * @param {string} uuid - Device UUID
   * @param {Object} filters - Query filters (pageSize, offset, startDate, endDate, includeSensors, timeWindow)
   * @returns {Object} - Unified historical data
   */
  async getDeviceUnifiedHistory(uuid, filters) {
    try {
      const { 
        pageSize = 20, 
        offset = 0, 
        startDate, 
        endDate, 
        includeSensors,
        timeWindow = 0 // Default 0ms (no aggregation)
      } = filters;

      const sensorTables = includeSensors 
        ? Object.entries(config.sensors).filter(([type]) => includeSensors.includes(type))
        : Object.entries(config.sensors);

      if (sensorTables.length === 0) {
        return {
          status: 'success',
          data: [],
          pagination: { total: 0, pageSize, offset, hasMore: false },
          metadata: { availableSensors: [], timeRange: null }
        };
      }

      let finalData, totalCount, hasMore, availableSensors, timeRange;

      if (timeWindow === 0) {
        // Original logic without aggregation for 0ms time window
        const originalData = await this.getOriginalDataWithoutAggregation(
          uuid, sensorTables, { startDate, endDate, pageSize, offset }
        );
        
        finalData = originalData.data;
        totalCount = originalData.total;
        hasMore = originalData.hasMore;
        availableSensors = originalData.availableSensors;
        timeRange = originalData.timeRange;
      } else {
        // Get aggregated data using time windows
        const aggregatedData = await this.getAggregatedDataWithTimeWindow(
          uuid, sensorTables, { startDate, endDate, timeWindow }
        );

        if (aggregatedData.length === 0) {
          return {
            status: 'success',
            data: [],
            pagination: { total: 0, pageSize, offset, hasMore: false },
            metadata: { availableSensors: [], timeRange: null }
          };
        }

        // Sort by timestamp descending and apply pagination
        const sortedData = aggregatedData
          .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
          .slice(offset, offset + pageSize);

        // Calculate pagination info
        totalCount = aggregatedData.length;
        hasMore = offset + pageSize < totalCount;

        // Get metadata
        availableSensors = Array.from(new Set(
          aggregatedData.flatMap(record => 
            Object.keys(record).filter(key => key !== 'timestamp').map(key => key.split('_')[0])
          )
        ));

        timeRange = aggregatedData.length > 0 ? {
          earliest: new Date(Math.min(...aggregatedData.map(d => new Date(d.timestamp)))).toISOString(),
          latest: new Date(Math.max(...aggregatedData.map(d => new Date(d.timestamp)))).toISOString()
        } : null;

        finalData = sortedData;
      }

      // Convert timestamps to ISO strings
      const dataWithIsoTimestamps = finalData.map(d => ({
        ...d,
        timestamp: new Date(d.timestamp).toISOString()
      }));

      return {
        status: 'success',
        data: dataWithIsoTimestamps,
        pagination: { total: totalCount, pageSize, offset, hasMore },
        metadata: { availableSensors, timeRange, timeWindow }
      };

    } catch (error) {
      console.error('Error getting device unified history:', error);
      return { status: 'error', error: error.message };
    }
  }

  /**
   * Get original sensor data without time window aggregation (0ms mode)
   * @param {string} uuid - Device UUID
   * @param {Array} sensorTables - Array of [sensorType, sensorConfig] pairs
   * @param {Object} options - Query options (startDate, endDate, pageSize, offset)
   * @returns {Object} - Original data with pagination
   */
  async getOriginalDataWithoutAggregation(uuid, sensorTables, options) {
    const { startDate, endDate, pageSize, offset } = options;

    // Build timestamp collection query from all relevant sensor tables
    let timestampQueries = [];

    for (const [sensorType, sensorConfig] of sensorTables) {
      let timestampQuery = this.sql`
        SELECT DISTINCT timestamp FROM ${this.sql(sensorConfig.table)}
        WHERE device_uuid = ${uuid}
      `;

      if (startDate) {
        timestampQuery = this.sql`${timestampQuery} AND timestamp >= ${startDate}`;
      }

      if (endDate) {
        timestampQuery = this.sql`${timestampQuery} AND timestamp <= ${endDate}`;
      }

      timestampQueries.push(timestampQuery);
    }

    // Get all unique timestamps
    const timestampResults = await Promise.all(timestampQueries);
    const allTimestamps = new Set();
    
    timestampResults.forEach(result => {
      result.forEach(row => allTimestamps.add(row.timestamp));
    });

    // Sort timestamps descending and apply pagination
    const sortedTimestamps = Array.from(allTimestamps)
      .sort((a, b) => new Date(b) - new Date(a))
      .slice(offset, offset + pageSize);

    if (sortedTimestamps.length === 0) {
      return {
        data: [],
        total: 0,
        hasMore: false,
        availableSensors: [],
        timeRange: null
      };
    }

    // For each timestamp, collect all sensor data
    const unifiedData = [];
    
    for (const timestamp of sortedTimestamps) {
      const timestampData = { timestamp };

      // Collect data from each sensor table for this timestamp
      for (const [sensorType, sensorConfig] of sensorTables) {
        const sensorData = await this.sql`
          SELECT * FROM ${this.sql(sensorConfig.table)}
          WHERE device_uuid = ${uuid} AND timestamp = ${timestamp}
          LIMIT 1
        `;

        if (sensorData.length > 0) {
          const data = sensorData[0];
          
          // Map database column names back to prefixed field names
          for (const [prefixedName, columnName] of Object.entries(sensorConfig.mapping)) {
            if (data[columnName] !== null && data[columnName] !== undefined) {
              timestampData[prefixedName] = data[columnName];
            }
          }
        }
      }

      unifiedData.push(timestampData);
    }

    // Calculate total count for pagination
    const totalCount = allTimestamps.size;
    const hasMore = offset + pageSize < totalCount;

    // Get metadata
    const availableSensors = Array.from(new Set(
      unifiedData.flatMap(record => 
        Object.keys(record).filter(key => key !== 'timestamp').map(key => key.split('_')[0])
      )
    ));

    const timeRange = sortedTimestamps.length > 0 ? {
      earliest: new Date(sortedTimestamps[sortedTimestamps.length - 1]).toISOString(),
      latest: new Date(sortedTimestamps[0]).toISOString()
    } : null;

    return {
      data: unifiedData,
      total: totalCount,
      hasMore,
      availableSensors,
      timeRange
    };
  }

  /**
   * Get aggregated sensor data using configurable time windows
   * @param {string} uuid - Device UUID
   * @param {Array} sensorTables - Array of [sensorType, sensorConfig] pairs
   * @param {Object} options - Query options (startDate, endDate, timeWindow)
   * @returns {Array} - Aggregated data grouped by time windows
   */
  async getAggregatedDataWithTimeWindow(uuid, sensorTables, options) {
    const { startDate, endDate, timeWindow } = options;
    const timeWindowMs = parseInt(timeWindow, 10);

    // Create a map to store aggregated data by time windows
    const timeWindowMap = new Map();

    // Collect data from all sensor tables
    for (const [sensorType, sensorConfig] of sensorTables) {
      let query = this.sql`
        SELECT timestamp, ${this.sql(Object.values(sensorConfig.mapping))}
        FROM ${this.sql(sensorConfig.table)}
        WHERE device_uuid = ${uuid}
      `;

      if (startDate) {
        query = this.sql`${query} AND timestamp >= ${startDate}`;
      }

      if (endDate) {
        query = this.sql`${query} AND timestamp <= ${endDate}`;
      }

      query = this.sql`${query} ORDER BY timestamp DESC`;

      const sensorData = await query;

      // Group data by time windows
      for (const row of sensorData) {
        const timestamp = new Date(row.timestamp);
        const windowStart = this.getTimeWindowStart(timestamp, timeWindowMs);
        const windowKey = windowStart.getTime();

        if (!timeWindowMap.has(windowKey)) {
          timeWindowMap.set(windowKey, {
            timestamp: windowStart,
            sensorData: new Map(),
            latestTimestamp: timestamp
          });
        }

        const windowData = timeWindowMap.get(windowKey);
        
        // Update with latest timestamp in this window
        if (timestamp > windowData.latestTimestamp) {
          windowData.latestTimestamp = timestamp;
          windowData.timestamp = timestamp; // Use latest timestamp as window representative
        }

        // Map database columns back to prefixed field names and store latest values
        for (const [prefixedName, columnName] of Object.entries(sensorConfig.mapping)) {
          if (row[columnName] !== null && row[columnName] !== undefined) {
            const existingEntry = windowData.sensorData.get(prefixedName);
            
            // Keep the value from the latest timestamp
            if (!existingEntry || timestamp >= existingEntry.timestamp) {
              windowData.sensorData.set(prefixedName, {
                value: row[columnName],
                timestamp: timestamp
              });
            }
          }
        }
      }
    }

    // Convert map to array and flatten sensor data
    const result = Array.from(timeWindowMap.values()).map(windowData => {
      const record = { timestamp: windowData.timestamp };
      
      // Add all sensor values from this time window
      for (const [prefixedName, entry] of windowData.sensorData) {
        record[prefixedName] = entry.value;
      }
      
      return record;
    });

    return result;
  }

  /**
   * Calculate the start of a time window for a given timestamp
   * @param {Date} timestamp - The timestamp to process
   * @param {number} windowMs - Time window size in milliseconds
   * @returns {Date} - Start of the time window
   */
  getTimeWindowStart(timestamp, windowMs) {
    const ms = timestamp.getTime();
    const windowStart = Math.floor(ms / windowMs) * windowMs;
    return new Date(windowStart);
  }

  /**
   * Get lightweight list of device UUIDs for polling
   * @returns {Object} - Array of device UUIDs
   */
  async getDevicesBasicInfo() {
    try {
      const deviceMap = new Map();

      // Query from main tables to get distinct device UUIDs
      for (const [, sensorConfig] of Object.entries(config.sensors)) {
        const deviceInfo = await this.sql`
          SELECT DISTINCT device_uuid FROM ${this.sql(sensorConfig.table)}
        `;

        deviceInfo.forEach(info => {
          deviceMap.set(info.device_uuid, info.device_uuid);
        });
      }

      const devices = Array.from(deviceMap.values());
      return { status: 'success', devices };

    } catch (error) {
      console.error('Error getting devices basic info:', error);
      return { status: 'error', error: error.message };
    }
  }
}

export default GroupedSensorDataService;
