import express from 'express';

const createApiRoutes = (apiController) => {
  const router = express.Router();

  // === GROUPED SENSOR DATA ROUTES ===

  // POST grouped sensor data from ESP32
  router.post('/grouped/data', apiController.postGroupedSensorData.bind(apiController));

  // GET latest grouped sensor data for a device
  router.get('/grouped/latest', apiController.getLatestGroupedData.bind(apiController));

  // GET devices summary with statistics
  router.get('/grouped/devices/summary', apiController.getGroupedDevicesSummary.bind(apiController));

  // GET lightweight devices info for polling
  router.get('/grouped/devices/basic', apiController.getGroupedDevicesBasicInfo.bind(apiController));

  // GET unified historical data for a single device (replaces 6 separate calls)
  router.get('/grouped/devices/:uuid/history', apiController.getGroupedDeviceHistory.bind(apiController));

  // DELETE device data from all grouped sensor tables
  router.delete('/grouped/data/:uuid', apiController.deleteGroupedDeviceData.bind(apiController));

  // === LOG DATA ROUTES ===

  // POST log data from ESP32
  router.post('/logs/data', apiController.postLogData.bind(apiController));

  // GET log data for a specific device
  router.get('/logs/device/:uuid', apiController.getDeviceLogs.bind(apiController));

  return router;
};

export default createApiRoutes; 