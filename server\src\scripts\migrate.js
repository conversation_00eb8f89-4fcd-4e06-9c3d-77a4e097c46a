#!/usr/bin/env node

import { execSync } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';
import dotenv from 'dotenv';
import { drizzle } from 'drizzle-orm/postgres-js';
import { migrate as drizzleMigrate } from 'drizzle-orm/postgres-js/migrator';
import postgres from 'postgres';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = resolve(__dirname, '..', '..');

async function migrate() {
  try {
    console.log('🚀 Starting database migration...');
    
    // Check if DATABASE_URL is set
    if (!process.env.DATABASE_URL) {
      console.error('❌ DATABASE_URL environment variable is not set');
      console.log('💡 Please set DATABASE_URL in your .env file or environment');
      console.log('   Example: DATABASE_URL=postgresql://username:password@localhost:5432/blockly_db');
      process.exit(1);
    }
    
    console.log('📊 Generating migration files...');
    execSync('npm run db:generate', { cwd: rootDir, stdio: 'inherit' });
    
    console.log('📦 Applying migrations to database...');
    const connectionString = process.env.DATABASE_URL;
    const sql = postgres(connectionString, { max: 1 });
    const db = drizzle(sql);

    await drizzleMigrate(db, { migrationsFolder: resolve(__dirname, '..', 'db', 'migrations') });

    await sql.end();
    
    console.log('✅ Database migration completed successfully!');
    console.log('💡 You can now start the server and it will use PostgreSQL');
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  }
}

// Run migration if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  migrate();
}

export default migrate; 
 