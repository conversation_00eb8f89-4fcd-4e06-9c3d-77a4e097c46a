import { networkInterfaces } from 'os';

// Get local IP address
function getLocalIP() {
  const nets = networkInterfaces();
  const results = {};

  for (const name of Object.keys(nets)) {
    for (const net of nets[name]) {
      if (net.family === 'IPv4' && !net.internal) {
        if (!results[name]) {
          results[name] = [];
        }
        results[name].push(net.address);
      }
    }
  }
  
  // Return first non-internal IPv4 address
  for (const name of Object.keys(results)) {
    if (results[name].length > 0) {
      return results[name][0];
    }
  }
  return 'localhost';
}

// Get all network interfaces
function getAllNetworkInterfaces() {
  const nets = networkInterfaces();
  const interfaces = [];

  for (const name of Object.keys(nets)) {
    for (const net of nets[name]) {
      interfaces.push({
        name: name,
        address: net.address,
        family: net.family,
        internal: net.internal,
        mac: net.mac
      });
    }
  }

  return interfaces;
}

// Generate server URLs for different interfaces
function generateServerURLs(port) {
  const interfaces = getAllNetworkInterfaces();
  const urls = [];

  // Add localhost
  urls.push(`http://localhost:${port}`);

  // Add external IP addresses
  interfaces
    .filter(iface => iface.family === 'IPv4' && !iface.internal)
    .forEach(iface => {
      urls.push(`http://${iface.address}:${port}`);
    });

  return urls;
}

export {
  getLocalIP,
  getAllNetworkInterfaces,
  generateServerURLs
}; 